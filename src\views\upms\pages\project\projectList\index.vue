<template>
  <div class="content">
    <div class="box">
      <div class="search-div">
        <p class="title"> 项目信息搜索 </p>
        <basic-search
          :searchArray="searchFormSchema"
          class="search-all"
          ref="searchRef"
          :labelWidth="80"
          :labelShow="false"
          @reset="projectList"
          @onSearch="projectList"
        />
      </div>
      <div class="table-content">
        <div class="table-top-div">
          <p>项目信息列表</p>
          <div class="top-button">
            <el-button
              type="primary"
              @click="handleAdd"
              v-auth="['project_project_add']"
              :icon="Plus"
              >新增</el-button
            >
          </div>
        </div>
        <basic-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :total="page.total"
          :page-size="page.pageSize"
          :current-page="page.pageNum"
          @page-change="pageChange"
          @size-change="sizeChange"
          :downSetting="true"
          height="calc(100vh - 392px)"
        >
          <template #action="{ record }">
            <el-button
              type="primary"
              link
              @click="handleDelete(record)"
              v-auth="['project_project_delete']"
              >删除</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleEdit(record)"
              v-auth="['project_project_edit']"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleDetail(record)"
              v-auth="['project_project_details']"
              >查看</el-button
            >
          </template>
        </basic-table>
      </div>
    </div>

    <el-drawer v-model="drawer" direction="rtl" size="600px" :destroy-on-close="true">
      <template #header>
        <h4>{{ title }}</h4>
      </template>

      <basic-form
        :formList="formSchema"
        :isCreate="false"
        :formData="formData"
        :showSubmit="false"
        :check-strictly="true"
        :disabled="!isEdit"
        ref="formTenant"
      >
        <template #adminIds>
          <el-select
            style="width: 100%"
            v-model="formData.adminIds"
            clearable
            multiple
            @focus="handleFocusUserEvent"
            @change="handleChangeUser"
            @remove-tag="removeTag"
            ref="userSelect"
          >
            <el-option
              v-for="item in userOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          /></el-select>
        </template>
      </basic-form>

      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmClick"
            :loading="loading"
            v-if="isEdit"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>

    <el-dialog
      v-model="visibleUser"
      title="选择账号"
      width="60%"
      :destroy-on-close="true"
    >
      <account-list
        @selectAccounts="selectAccounts"
        ref="userListRef"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleUser = false">取消</el-button>
        <el-button type="primary" @click="saveAccount">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, markRaw, nextTick } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { CircleClose, Plus } from '@element-plus/icons-vue';
  import { columns, formSchema, searchFormSchema } from './projectList.data';
  import BasicTable from '/@/components/sys/BasicTable';
  import { Option } from '/@/components/sys/BasicForm/types';
  import accountList from './component/account.vue';
  import {
    GetProjectGroup,
    AddProjectGroup,
    DelProjectGroup,
  } from '../../../api/project';
  import { ProjectListGetResultModel } from '/@/views/upms/api/model/projectModel';
  import { useTenantStore } from '/@/stores/modules/tenant';
  import { useUserStore } from '/@/stores/modules/user';
  import { getAuthStorage, setAuthStorage } from '/@/utils/storage/auth';
  import { userPermission } from '/@/views/upms/api/system';
  const userStore = useUserStore();
  const useTenant = useTenantStore();
  const tenantId = useTenant.getCurTenant.id;
  const tableData = ref<any[]>([]);
  const searchRef = ref({});
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const formData = ref<any>({
    projectName: '',
    projectAlias: '',
    description: '',
    adminId: '',
    adminIds: [],
    id: '',
  });
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const drawer = ref(false);
  const isEdit = ref(true);
  const title = ref('新增');
  const formTenant = ref();

  const userSelect = ref<HTMLDivElement | null>(null);
  const userListRef = ref<HTMLDivElement | null>(null);
  const visibleUser = ref(false);
  const selectUserData = ref<any>([]);
  const userOptions = ref<Option[]>([]);
  const loading = ref(false);
  const projectList = () => {
    const { pageNum, pageSize } = page.value;
    const { projectName, projectAlias } = searchRef?.value?.['searchValue'] || {};
    GetProjectGroup({
      pageNum,
      pageSize,
      projectName,
      projectAlias,
      opType: 'tenant',
    }).then((res: ProjectListGetResultModel) => {
      const { list, total, pageNum, pageSize } = res;
      tableData.value = list;
      page.value = {
        total,
        pageNum,
        pageSize,
      };
    });
  };
  /**
   * 切换分页，每页显示数量
   */
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    projectList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    projectList();
  };
  projectList();
  const getuserPermission = (userInfo) => {
    userPermission().then((res) => {
      userInfo.permissionMap = res;
      setAuthStorage(userInfo);
    });
  };
  // rank();
  const confirmClick = () => {
    const getData = formTenant.value.submitForm;
    const ruleFormRef = formTenant.value.ruleFormRef;
    loading.value = true;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        data.adminId = data.adminIds.join(',');
        data.tenantId = tenantId;
        AddProjectGroup(data)
          .then(async (res) => {
            if (res) {
              ElMessage({
                type: 'success',
                message: data.id ? `修改项目成功` : `新增项目成功`,
              });
              projectList();
              drawer.value = false;

              await useTenant.requestData(userStore.getUserInfo);
            }
            loading.value = false;
            let userInfo = getAuthStorage();
            getuserPermission(userInfo);
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
      }
      loading.value = false;
    });
  };
  const handleAdd = () => {
    if (!tenantId) {
      ElMessage({
        type: 'warning',
        message: '请选择租户信息!',
      });
      return false;
    }
    formData.value = {
      tenantName: '',
      tenantAlias: '',
      adminName: '',
      description: '',
      adminId: '',
      id: '',
      adminIds: [],
    };
    isEdit.value = true;
    drawer.value = true;
    formSchema[1].componentProps.disabled = false;
    title.value = '新增项目';
    selectUserData.value = [];
  };

  const handleEdit = (item) => {
    isEdit.value = true;
    drawer.value = true;
    title.value = '编辑项目';
    formData.value = item;
    formSchema[1].componentProps.disabled = true;
    userOptions.value = [];
    formData.value.adminIds = [];
    const userNames = item.adminName.split(',');
    const adminListId: string[] = item.adminId.split(',');
    selectUserData.value = [];
    for (let i = 0; i < adminListId.length; i++) {
      formData.value.adminIds.push(adminListId[i]);
      let userObj = {
        label: userNames[i],
        value: adminListId[i],
      };
      userOptions.value.push(userObj);
      let selectUserObj = {
        userName: userNames[i],
        id: adminListId[i],
        label: userNames[i],
      };
      selectUserData.value.push(selectUserObj);
    }
  };
  const handleDetail = (item) => {
    isEdit.value = false;
    drawer.value = true;
    title.value = '查看项目';
    formData.value = item;
    formSchema[1].componentProps.disabled = true;
    userOptions.value = [];
    formData.value.adminIds = [];
    const userNames = item.adminName.split(',');
    const adminListId: string[] = item.adminId.split(',');
    selectUserData.value = [];
    for (let i = 0; i < adminListId.length; i++) {
      formData.value.adminIds.push(adminListId[i]);
      let userObj = {
        label: userNames[i],
        value: adminListId[i],
      };
      userOptions.value.push(userObj);
      let selectUserObj = {
        userName: userNames[i],
        id: adminListId[i],
        label: userNames[i],
      };
      selectUserData.value.push(selectUserObj);
    }
  };

  const handleDelete = (item) => {
    // type 1 批量删除  0 单独删除
    ElMessageBox.confirm('确认删除该项目？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        DelProjectGroup({ id: item.id }).then(async () => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '项目删除成功',
          });
          projectList();
          await useTenant.requestData(userStore.getUserInfo);
        });
      })
      .catch(() => {});
  };

  const handleFocusUserEvent = (e) => {
    visibleUser.value = true;
    nextTick(() => {
      // userSelect.value && userSelect.value?.blur();
      e.target.blur();
      if (selectUserData.value.length > 0) {
        if (formData.value.adminIds) {
          userListRef.value &&
            userListRef.value?.handleSetSelectData(selectUserData.value);
        } else {
          userListRef.value && userListRef.value?.handleSetSelectData(null);
        }
      }
    });
  };
  const handleChangeUser = (data) => {
    if (data.length > 0) {
      userListRef.value && userListRef.value?.handleSetSelectData(selectUserData.value);
    }
  };

  const selectAccounts = (data) => {
    selectUserData.value = data;
  };
  const saveAccount = () => {
    userOptions.value = [];
    formData.value.adminIds = [];
    for (let i = 0; i < selectUserData.value.length; i++) {
      const obj = {
        label: selectUserData.value[i].userName,
        value: selectUserData.value[i].id,
      };
      formData.value.adminIds.push(selectUserData.value[i].id);
      userOptions.value.push(obj);
    }
    visibleUser.value = false;
  };

  const removeTag = (val) => {
    selectUserData.value = removeById(selectUserData.value, val);
  };
  const removeById = (arr, id) => {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].id === id) {
        arr.splice(i, 1);
        break;
      }
    }
    return arr;
  };
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 12px 12px;
    overflow: hidden;

    // .title {
    //   border-bottom: 1px solid #e4e7ed;
    //   line-height: 68px;
    //   font-size: 18px;
    //   padding-left: 24px;
    //   background: #ffffff;
    //   font-weight: 700;
    // }

    // .search-all {
    //   background: #ffffff;
    //   padding-left: 12px;
    // }
    .search-div {
      border-radius: 6px;
      background: #fff;
      // padding: 10px 16px;
      .title {
        // border-bottom: 1px solid #e4e7ed;
        // line-height: 68px;

        font-size: 18px;
        padding: 20px 0 6px 20px;
        // background: #ffffff;
        font-weight: 700;
        color: #333333;
      }
    }
    .table-top-div {
      display: flex;
      justify-content: space-between;
      // line-height: 64px;
      margin-bottom: 20px;
      p {
        font-size: 18px;
        color: #333333;
        font-weight: 700;
        margin: 0;
        padding: 0;
        line-height: 32px;
      }
    }

    .table-content {
      margin-top: 20px;
      background: #ffffff;
      // height: calc(100% - 180px);
      padding: 20px 20px 0 20px;
      position: relative;
      overflow: auto;
      .top-button {
        // position: absolute;
        // top: 10px;
        // left: 24px;
        z-index: 9;

        .upload-btn {
          display: inline-block;
          margin: 0 12px;
        }
      }
      .tip {
        position: absolute;
        top: 22px;
        right: 70px;
        z-index: 9;
        color: #409eff;
      }

      .active-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #00a854;
        margin-right: 5px;
      }
      .close-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #bfbfbf;
        margin-right: 5px;
      }
    }
  }

  h4 {
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 700;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
</style>
