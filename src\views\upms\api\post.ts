import { sysHttp, downloadHttp, uploadHttp } from '/@/views/upms/common/http';
import {
  PostListResultModel,
  PostListPramas,
  AddPostParams,
  AuthorPramas,
} from './model/post';
enum Api {
  postList = '/post/station/', //岗位列表
  rank = '/post/station/rank', // 职级
  childDept = '/dept/child-list/', // 获取子机构
  authorPost = '/post/station/authorize', //岗位列表
  downloadUrl = '/post/station/download',
  uploadUrl = '/post/station/upload',
}

// 岗位列表
export const getPostList = (params?: PostListPramas) =>
  sysHttp.post<PostListResultModel>({ url: Api.postList + 'page', params: params });
// 新增岗位
export const SavePostApi = (params: AddPostParams) =>
  sysHttp.post({ url: Api.postList, params });
// 删除岗位
export const DelPost = (id: string) => sysHttp.delete({ url: `${Api.postList}${id}` });
// 获取职级
export const getRank = () => sysHttp.get({ url: Api.rank });
// 岗位列表
export const getChildDept = (id: string) =>
  sysHttp.get({ url: `${Api.childDept}${id}` });
// 授权岗位
export const authorPost = (params: AuthorPramas) =>
  sysHttp.post({ url: Api.authorPost, params: params });

export const downLoadApi = (params) =>
  downloadHttp.get({ url: Api.downloadUrl, params });
//

export const uploadApi = (params) => {
  return uploadHttp.post({
    url: Api.uploadUrl,
    params,
  });
};
