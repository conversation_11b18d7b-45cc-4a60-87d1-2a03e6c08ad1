import { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel';
import { DataEntityVo } from '/@/api/model/DataEntityVo';

export interface LogDataVo extends DataEntityVo {
  id: number; // 日志编号
  title: string; // 日志标题
  // type: string;        // 日志类型
  operateType: string; // 业务操作类别（0其它 1新增 2修改 3删除）
  operatorType: string; // 业务操作人类型（0其它 1后台用户 2手机端用户）
  username: string; // 业务操作人
  serviceId: string; // 服务ID
  ipAddress: string; // 操作端IP地址
  ipLocation: string; // 登录地点
  browser: string;
  os: string;
  userAgent: string; // 用户代理
  requestUri: string; // 请求URI
  method: string; // 请求方式
  params: string; // 请求提交的参数
  time: number; // 执行时间
  exception: string; // 异常信息
  deptId: string; // 机构ID
  deptName: string; // 机构名称
}

export interface LogDictItem {
  codes?: string;
}
/**
 * 日志列表请求参数
 */
export type LogListParams = BasicPageParams & {
  // title: string;       // 日志标题 模糊搜索
  // method: string;      // 请求方法
  deptId: string; // 根据机构ID精确搜索
  // deptName: string;    // 根据机构名称模糊搜索
  cascade: string; // 是否级联查询
  operateType: string; // 操作方式
  // requestUri: string;  // 请求URI
  userName: string; // 用户名
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  tenantId?: string;
  projectId?: string;
};

export type LogListResult = BasicFetchResult<LogDataVo[]> & {
  list: [];
};

export type LogDict = Array<string>;

export interface HeaderForm {
  tenantId?: string;
  projectId?: string;
}
