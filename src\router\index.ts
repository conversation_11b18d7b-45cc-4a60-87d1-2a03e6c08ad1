/* eslint-disable no-param-reassign */
import { createRouter, createWebHistory, RouterOptions } from 'vue-router';
import { setupLayouts } from 'virtual:generated-layouts';
import generatedRoutes from 'virtual:generated-pages';
import { PageEnum } from '/@/enums/pageEnum';
import { warpperEnv } from '@build/index';
// import './menus';
export const allRoutes = setupLayouts(generatedRoutes);

// 菜单白名单，以下菜单不在权限范围内，并且不在最终Menu中显示
const LOGIN_PATH = PageEnum.BASE_LOGIN;
const ERROR_PAGE = PageEnum.ERROR_PAGE;
// const USER_INFO = PageEnum.USER_INFO;
const BASE_RETRIEVE = PageEnum.BASE_RETRIEVE;
const BASE_REGIDSTER = PageEnum.BASE_REGIDSTER;
// console.log(BASE_RETRIEVE);
// 系统白名单
const SYS_WHITE_LIST: string[] = [ERROR_PAGE, LOGIN_PATH, BASE_RETRIEVE, BASE_REGIDSTER];
// 业务白名单
export const BUS_WHITE_LIST: string[] = [];
export const WHITE_MENU_PATH_LIST = [...SYS_WHITE_LIST, ...BUS_WHITE_LIST];
// console.log(WHITE_MENU_PATH_LIST);
const notFound = () => {
  let notFoundRoute;
  notFoundRoute = allRoutes.find(
    (route) => route.children && route.children[0].name === 'NOT_FOUND',
  );
  notFoundRoute = notFoundRoute.children && notFoundRoute.children[0];
  return notFoundRoute;
};
export const PAGE_NOT_FOUND = notFound();

// 筛选白名单的路由
const routes = allRoutes.filter((item) =>
  WHITE_MENU_PATH_LIST.includes(item.path as string),
);

// console.log(allRoutes);
// console.log(routes);
const { VITE_PUBLIC_PATH } = warpperEnv();
const router = createRouter(<RouterOptions>{
  // history: createWebHashHistory(),
  history: createWebHistory(VITE_PUBLIC_PATH),
  routes,
  strict: true,
});

// 保存 addRoute的回调函数，用来清空路由
export const addedRoute: (() => void)[] = [];

export function removeUserRouter() {
  // 清空路由
  addedRoute.forEach((f) => f());
}
console.log(router);
export default router;

export function installRouter(app: any) {
  app.use(router);
}
