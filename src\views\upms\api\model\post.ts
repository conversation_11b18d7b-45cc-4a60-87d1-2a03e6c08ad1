import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface PostListItem {
  applyDeptIdList?: string[];
  applyDeptName?: string;
  createdBy?: string;
  createdDate?: Date;
  createdName?: string;
  delFlag?: string;
  deptId?: string;
  deptName?: string;
  description?: string;
  id?: string;
  lastModifiedDate?: string;
  pageNum?: number;
  pageSize?: number;
  postStationCode?: string;
  postStationName?: string;
  rankId?: string;
  rankName?: string;
  version?: number;
  display?: string;
}

export type PostListPramas = BasicPageParams & {
  opType?: string;
  deptId?: string;
};

export type PostListResultModel = BasicFetchResult<PostListItem> & {
  list: [];
  pageNum: number;
  pageSize: number;
  total: number;
};

export interface AddPostParams {
  id?: string;
  deptId?: string;
  postStationName: string;
  postStationCode: string;
  rankId?: string;
  display?: string;
}

export interface SearchParams {
  postStationName: string;
  postStationCode: string;
  display: string;
}

export interface AuthorPramas {
  id: string;
  applyDeptIdList: string[];
}

export interface DepTreeItem {
  id: string;
  label: string;
  parentId?: string;
  children?: DepTreeItem[];
}
