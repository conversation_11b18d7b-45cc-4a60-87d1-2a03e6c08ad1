import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface ListItem {
  id: string;
  microName: string;
  name: string;
  path: string;
  url: string;
  tenantId?: string;
  projectId?: string;
}

export interface SaveParams {
  id?: string;
  microName: string;
  name: string;
  path: string;
  url: string;
  opType?: number;
}

export interface SearchParams {
  name?: string;
  microName?: string;
  url?: string;
}

export type ListParams = BasicPageParams &
  SearchParams & {
    tenantId?: string;
    projectId?: string;
    opType?: string;
  };

export type ListResultModel = BasicFetchResult<ListItem> & BasicPageParams;
