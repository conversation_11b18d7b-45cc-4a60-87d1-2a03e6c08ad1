import { ref } from 'vue';
import { TableOptions } from '/@/components/sys/BasicTable/types';
import { getDictStorage } from '/@/utils/storage/dict';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
import { FormOptions } from '/@/components/sys/BasicForm/types';
import { getWebModel } from '/@/utils/storage/auth';
import { resetPwdType } from '/@/views/upms/api/model/accountModel';
import { checkApi } from '../../api/account';
import { PwdCheck } from '/@/utils/operate/PwdCheckUtils';
const sensitiveWords =
  getDictStorage()?.biz_sensitive_words &&
  getDictStorage().biz_sensitive_words.length > 0
    ? getDictStorage().biz_sensitive_words.map((obj) => obj.value)
    : [];

export const formData = ref({
  id: '',
  employeeId: '',
  available: '',
  userPasswd: '',
  roleIdList: [],
  userName: '',
  postStationIdList: [],
  deptName: '',
  deptId: [],
});
export const formDataResetPwd = ref<resetPwdType>({
  id: '',
  userName: '',
  sendEmail: [],
  sendSMS: [],
  userPasswd: '',
});
// export const formAccount = ref();
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
  },
  {
    label: '账号名',
    prop: 'userName',
    width: 100,
    align: 'center',
    sortable: 'custom',
  },
  {
    label: '员工姓名',
    prop: 'employeeName',
    width: 120,
    align: 'center',
  },
  {
    label: '角色',
    prop: 'roleIdList',
    minWidth: 220,
    align: 'left',
    slot: 'roleIdList',
    tooltip: false,
    headerAlign: 'left',
  },
  {
    label: '机构',
    prop: 'deptName',
    minWidth: 180,
    sortable: 'custom',
    align: 'center',
    hideColumn: () => {
      const sysUserDeptShow = getDictStorage()?.sys_user_dept
        ? getDictStorage().sys_user_dept[0].value
        : '0';
      return sysUserDeptShow == '0';
    },
  },
  {
    label: '账号状态',
    prop: 'available',
    width: 90,
    align: 'center',
    slot: 'available',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    align: 'center',
    width: getWebModel() === 'mix' ? 280 : 420,
  },
];
export const searchFormSchema: SearchOptions[] = [
  {
    field: 'userName',
    label: '账号名:',
    component: 'Input',
    placeholder: '请输入账号名',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },

  {
    field: 'available',
    label: '账号状态',
    component: 'Select',
    placeholder: '请选择账号状态',
    span: 8,

    componentProps: {
      clearable: true,
      options: [
        {
          label: '已激活',
          value: '1',
        },
        {
          label: '已禁用',
          value: '0',
        },
        {
          label: '已锁定',
          value: '2',
        },
      ],
    },
  },
  {
    field: 'employeeName',
    label: '员工姓名',
    component: 'Input',
    placeholder: '请输入员工姓名',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'sub',
    label: '查看本机构及下属机构员工',
    componentProps: {
      defaultValue: '1',
    },
    span: 8,
    labelWidth: 50,
    slot: 'sub',
  },
];
export const accountFormSchema: FormOptions[] = [
  {
    field: 'userName',
    label: '账号名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入账号名',
      disabled: false,
    },
    slot: 'userName',
    required: true,
    rules: [
      { max: 30, message: '超过长度限制，最多30字' },
      {
        validator: (_, value) => {
          console.log('formData', formData.value);
          if (sensitiveWords.length > 0) {
            const pattern = new RegExp(sensitiveWords.join('|'), 'i');
            if (pattern.test(value)) {
              return Promise.reject('账号名中不能包含敏感词汇！');
            }
          }
          return new Promise<void>((resolve, reject) => {
            checkApi({
              id: formData.value.id,
              userName: value,
            }).then((res) => {
              res ? resolve() : reject(res.message || '账号名已存在');
            });
          });
        },
      },
      // {
      //   validator: (_, value) => {
      //     // console.log(value)
      //     // console.log(formAccount.value)
      //     // formAccount.value && formAccount.value.validateField('userPasswd')
      //     // const pattern = new RegExp(sensitiveWords.join('|'), 'i');
      //     // if (pattern.test(value)) {
      //     //   return Promise.reject('用户名中不能包含敏感词汇！');
      //     // }
      //     // return Promise.resolve();
      //   },
      // }
    ],
  },
  {
    field: 'userPasswd',
    label: '登录密码',
    component: 'InputPassword',
    componentProps: {
      placeholder: '请输入新密码',
    },
    dynamicRules: [
      {
        validator: (_, value) => {
          const result = PwdCheck(value);
          if (result) {
            return Promise.reject(result);
          }
          return Promise.resolve();
        },
      },
    ],
    ifShow: true,
    required: true,
  },
  {
    field: 'avatar',
    label: '头像',
    component: 'Input',
    slot: 'avatar',
  },
  {
    label: '关联员工',
    component: 'Slot',
    field: 'employeeId',
    slot: 'user',
    required: true,
  },
  {
    label: '关联角色',
    component: 'Slot',
    field: 'roleIdList',
    slot: 'roleIdList',
    // required: true,
  },
  {
    label: '账号状态',
    field: 'available',
    component: 'RadioGroup',
    componentProps: {
      options: [
        {
          label: '已激活',
          value: '1',
        },
        {
          label: '已禁用',
          value: '0',
        },
        {
          label: '已锁定',
          value: '2',
        },
      ],
    },
    required: true,
  },
  {
    field: 'postStationIdList',
    label: '岗位：',
    component: 'Slot',
    slot: 'postStationIdList',
  },
];

export const passwordFormSchema: FormOptions[] = [
  {
    field: 'userName',
    label: '账号名：',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    required: true,
  },
  {
    label: '默认密码',
    field: 'userPasswd',
    component: 'InputPassword',
    required: true,
    dynamicRules: [
      {
        validator: (_, value) => {
          const result = PwdCheck(value);
          if (result) {
            return Promise.reject(result);
          }
          return Promise.resolve();
        },
      },
    ],
  },
];
