<template>
  <div class="content">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane class="pane-left" size="20" min-size="20">
        <div class="left">
          <div class="tree-content">
            <div class="tree-div">
              <VTreeSearch
                ref="tree"
                selectable
                :load="getDeptData"
                @click="handleTreeClick"
              >
                <template #actions>
                  <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
                </template>
                <template #search-input>
                  <el-input
                    class="search-input"
                    placeholder="至少3个字才能触发自动搜索"
                    v-model="searchName"
                    @input="handleSearch(0)"
                  />
                </template>
                <template #node="{ node }">
                  <span
                    :class="node.id == nodeId ? 'active' : ''"
                    :style="{
                      color:
                        name && node.instName.indexOf(name) > -1
                          ? 'rgb(166, 0, 0)'
                          : '',
                      fontWeight:
                        name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                    }"
                    >{{ node.instName }}</span
                  >
                </template>
              </VTreeSearch>
            </div>
          </div></div
        >
      </Pane>
      <Pane class="mid" size="80">
        <!--右侧div内容-->
        <div class="right">
          <div class="search-div">
            <p class="title"> 租户员工搜索 </p>
            <basic-search
              :searchArray="searchFormSchema"
              class="search-all"
              ref="searchRef"
              :labelWidth="80"
              :labelShow="false"
              @reset="accountList"
              @onSearch="accountList"
            >
              <template #sub>
                <el-checkbox
                  v-model="sub"
                  label="查看本机构及下属机构员工"
                  @change="handleCheckBox"
                />
              </template>
            </basic-search>
          </div>
          <div class="table-content">
            <div class="table-top-div">
              <p>租户员工列表</p>
              <div class="top-button">
                <el-button
                  type="primary"
                  :disabled="deptId ? false : true"
                  @click="handleAdd"
                  v-auth="['tenant_employee_add']"
                  :icon="Plus"
                  >新增</el-button
                >
                <el-button
                  type="primary"
                  @click="downLoad"
                  v-auth="['tenant_employee_export']"
                  :icon="Download"
                  plain
                  >导出</el-button
                >
                <el-button
                  type="primary"
                  @click="handlefileVisible"
                  v-auth="['tenant_employee_import']"
                  :icon="Upload"
                  plain
                  >导入</el-button
                >
                <el-button
                  type="danger"
                  @click="batchDeletion"
                  :disabled="selectionIds.length > 0 ? false : true"
                  v-auth="['tenant_employee_delete']"
                  :icon="Delete"
                  plain
                  >批量删除</el-button
                >
              </div>
            </div>
            <basic-table
              ref="tableRef"
              :columns="columns"
              :data="tableData"
              :total="page.total"
              :page-size="page.pageSize"
              :current-page="page.pageNum"
              @page-change="pageChange"
              @size-change="sizeChange"
              @selectionChange="handleCurrentChange"
              @sortChange="sortChange"
              :downSetting="true"
              height="calc(100vh - 472px)"
            >
              <template #empCode="{ record }">
                <span class="inst-code" @click="handleDetail(record)">{{
                  record.empCode
                }}</span>
              </template>
              <template #gender="{ record }">
                <el-tag v-if="record.gender === 'F'">女</el-tag>
                <el-tag v-else-if="record.gender === 'M'">男</el-tag>
              </template>
              <template #industryType="{ record }">
                <el-tag v-if="record.industryType">
                  <span
                    v-for="type in getDictStorage().dic_industry_type"
                    :key="type.value"
                  >
                    <span v-if="type.value == record.industryType">{{
                      type.label
                    }}</span>
                  </span>
                </el-tag>
              </template>
              <template #action="{ record }">
                <el-button
                  type="primary"
                  link
                  @click="handleDelete(record, 0)"
                  v-auth="['tenant_employee_delete']"
                  >删除</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="handleDetail(record)"
                  v-auth="['tenant_employee_details']"
                  >查看</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="handleEdit(record)"
                  v-auth="['tenant_employee_edit']"
                  >编辑</el-button
                >
              </template>
            </basic-table>
          </div>
        </div>
      </Pane>
    </Splitpanes>
    <el-drawer
      v-model="drawer"
      direction="rtl"
      size="600px"
      :destroy-on-close="true"
      modal-class="user-edit"
    >
      <template #header>
        <h4>{{ title }}</h4>
      </template>
      <template #default>
        <basic-form
          class="add-account"
          :formList="userId ? allAccountFormSchema : addAccountFormSchema"
          :isCreate="false"
          :formData="formData"
          :showSubmit="false"
          :check-strictly="true"
          ref="formAccount"
          :disabled="!isEdit"
        >
          <template #phone>
            <el-input
              style="width: 92%"
              v-model="formData.phone"
              placeholder="请输入手机号"
              :disabled="!checkFlag.phone" /><el-icon
              class="user-check-full"
              @click="getAccurate('phone')"
              ><View /></el-icon
          ></template>
          <template #autocomplete>
            <el-autocomplete
              style="width: 100%"
              v-model="formData.email"
              :fetch-suggestions="querySearchEmail"
              :trigger-on-focus="false"
              placeholder="请输入电子邮箱"
          /></template>
          <template #email>
            <el-autocomplete
              style="width: 92%"
              v-model="formData.email"
              :fetch-suggestions="querySearchEmail"
              :trigger-on-focus="false"
              placeholder="请输入电子邮箱"
              :disabled="!checkFlag.email" /><el-icon
              class="user-check-full"
              @click="getAccurate('email')"
              ><View /></el-icon
          ></template>
          <template #idCard>
            <el-input
              style="width: 92%"
              v-model="formData.idCard"
              placeholder="请输入身份证号"
              :disabled="!checkFlag.idCard" /><el-icon
              class="user-check-full"
              @click="getAccurate('idCard')"
              ><View /></el-icon
          ></template>
        </basic-form>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmClick"
            v-if="isEdit"
            :loading="loading"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>

    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
    >
      <el-button type="primary" link @click="downLoad" style="margin-bottom: 12px"
        >下载租户员工导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="batchConfirmationVisible"
      title="删除警告"
      width="400px"
      class="batch-Confirmation-dialog"
      :destroy-on-close="true"
    >
      <BatchConfirmation @getPassword="getPassword" />
      <template #footer>
        <el-button @click="batchConfirmationVisible = false">取消</el-button>
        <el-button
          type="info"
          @click="batchConfirmationSave"
          :loading="batchConfirmationLoading"
          >删除</el-button
        >
      </template>
    </el-dialog>
    <el-dialog
      v-model="accurateVisible"
      title="查看完整员工信息"
      width="400px"
      class="batch-Confirmation-dialog"
      :destroy-on-close="true"
    >
      <AccurateConfirm @getPassword="(value) => (accuratePassWord = value)" />
      <template #footer>
        <el-button @click="accurateVisible = false">取消</el-button>
        <el-button @click="confirmAccurate">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, markRaw } from 'vue';
  import { cloneDeep } from 'lodash-es';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import {
    AccountListGetResultModel,
    tableItem,
  } from '/@/views/upms/api/model/accountModel';
  import BasicForm from '/@/components/sys/BasicForm';
  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { CircleClose, Plus, Download, Upload, Delete } from '@element-plus/icons-vue';
  import { searchFormSchema, columns, addAccountFormSchema } from './user.data';
  import { getDictStorage } from '/@/utils/storage/dict';
  import { FormOptions } from '/@/components/sys/BasicForm/types';

  import {
    getUserList,
    saveUser,
    getEmployeeDetail,
    moreDeleteApi,
    checkApi,
    tenantUserdownloadUrlApi,
    tenantUserUploadApi,
    accurate,
  } from '/@/views/upms/api/user';
  import { deptAsyn } from '/@/views/upms/api/dept';
  import useUpload from '/@/hooks/upms/upload/useUpload';
  import type { UploadInstance } from 'element-plus';
  import { getAuthStorage } from '/@/utils/storage/auth';
  import BatchConfirmation from '/@/views/upms/components/BatchConfirmation.vue';
  import AccurateConfirm from './accurateConfirm.vue';
  import userBatchConfirmation from '/@/views/upms/common/hooks/userBatchConfirmation';
  const tree = ref<InstanceType<typeof VTreeSearch> | null>(null);
  const userInfo = getAuthStorage();
  const upload = ref<UploadInstance>();
  const phoneReg =
    /^((\+86|0086)?\s*)((134[0-8]\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\d{8})|(14(0|1|4)0\d{7})|(1740([0-5]|[6-9]|[10-12])\d{7}))$/;
  const card =
    /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/;

  const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  const empReg = /^([\u4e00-\u9fa5·]|[A-Za-z]){2,15}$/;
  const checkFlag = ref({
    phone: false,
    email: false,
    idCard: false,
  });
  const currentFlag = ref<string>('');
  const allAccountForm = [
    {
      field: 'instName',
      label: '机构名称',
      component: 'Text',
    },
    {
      field: 'empName',
      label: '员工姓名',
      component: 'Input',
      componentProps: {
        placeholder: '请输入员工姓名',
        disabled: false,
      },
      rules: [
        { max: 15, message: '超过长度限制，最多15字' },
        { min: 2, message: '少于长度限制，最少2字' },
        {
          trigger: 'blur',
          pattern: empReg,
          message: '请输入正确的员工姓名',
        },
      ],
      required: true,
    },
    {
      field: 'empCode',
      label: '员工工号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入员工工号',
        disabled: true,
      },
      rules: [{ max: 90, message: '超过长度限制，最多90字' }],
      required: true,
    },
    {
      label: '手机号',
      field: 'phone',
      component: 'Input',
      slot: 'phone',
      componentProps: {
        placeholder: '请输入手机号',
      },
      required: true,
      rules: [
        {
          pattern: checkFlag.value.phone ? phoneReg : false,
          message: '请输入正确的手机号',
        },
        {
          validator: (rule, value) => {
            if (!value) {
              /* eslint-disable-next-line */
              return true;
            }
            return new Promise<void>((resolve, reject) => {
              const params = {
                type: 2,
                value: value,
                id: userId.value,
              };
              checkApi(params).then((res) => {
                res ? resolve() : reject(res.message || '手机号码已存在');
              });
            });
          },
        },
      ],
    },
    {
      label: '电子邮箱',
      field: 'email',
      component: 'Slot',
      slot: 'email',
      rules: [
        {
          required: true,
          trigger: 'blur',
          /* eslint-disable */
          pattern: checkFlag.value.email ? emailReg : false,
          message: '请输入正确的电子邮箱',
        },
        {
          validator: (rule, value) => {
            if (!value) {
              /* eslint-disable */
              return true;
            }
            return new Promise<void>((resolve, reject) => {
              const params = {
                type: 0,
                value: value,
                id: userId.value,
              };
              checkApi(params).then((res) => {
                res ? resolve() : reject(res.message || '电子邮箱已存在');
              });
            });
          },
        },
        { max: 100, message: '超过长度限制，最多100字' },
        { min: 3, message: '少于长度限制，最少3字' },
      ],
      required: true,
    },
    {
      label: '所属板块',
      field: 'industryType',
      component: 'RadioGroup',
      componentProps: {
        options: getDictStorage()?.dic_industry_type
          ? getDictStorage().dic_industry_type
          : [],
      },
      // required: true,
    },
    {
      label: '性别',
      field: 'gender',
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '男',
            value: 'M',
          },
          {
            label: '女',
            value: 'F',
          },
        ],
      },
      // required: true,
    },
    {
      label: '员工类型',
      field: 'empType',
      component: 'Select',
      componentProps: {
        clearable: true,
        options: getDictStorage().biz_emp_type ? getDictStorage().biz_emp_type : [],
      },
      required: false,
    },
    {
      label: '员工状态',
      field: 'status',
      component: 'RadioGroup',
      componentProps: {
        options: getDictStorage().biz_status ? getDictStorage().biz_status : [],
      },
      required: true,
    },
    {
      label: '环节',
      field: 'nodeCode',
      component: 'Select',
      componentProps: {
        clearable: true,
        options: getDictStorage().biz_node_code ? getDictStorage().biz_node_code : [],
      },
      required: false,
    },
    {
      label: '专业',
      field: 'serviceCode',
      component: 'Select',
      componentProps: {
        clearable: true,
        options: getDictStorage().biz_service_code
          ? getDictStorage().biz_service_code
          : [],
      },
      required: false,
    },
    {
      label: '员工职位',
      field: 'empPost',
      component: 'Select',
      componentProps: {
        clearable: true,
        options: getDictStorage().biz_emp_post ? getDictStorage().biz_emp_post : [],
      },
      required: false,
    },
    {
      label: '身份证号',
      field: 'idCard',
      component: 'Input',
      slot: 'idCard',
      required: true,
      rules: [
        {
          pattern: checkFlag.value.idCard ? card : false,
          message: '请输入正确的身份证号',
        },
        {
          validator: (rule, value) => {
            if (!value) {
              /* eslint-disable-next-line */
              return true;
            }
            return new Promise<void>((resolve, reject) => {
              const params = {
                type: 1,
                value: value,
                id: userId.value,
              };
              checkApi(params).then((res) => {
                res ? resolve() : reject(res.message || '身份证号码已存在');
              });
            });
          },
        },
      ],
    },
  ];
  const allAccountFormSchema = ref<FormOptions[]>(allAccountForm);
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const accurateVisible = ref(false);
  const accuratePassWord = ref<string>('');
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  const deptName = ref('');
  const instName = ref('');

  const deptId = ref('');
  const detaileDeptId = ref('');
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const searchRef = ref({});
  const sub = ref(true);
  const tableData = ref([]);
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const formData = ref<tableItem>({
    id: '',
    gender: '',
    email: '',
    industryType: '',
    phone: '',
    empName: '',
    instCode: '',
    deptId: '',
    deptName: '',
    empCode: '',
    empPost: '',
    empType: '',
    nodeCode: '',
    serviceCode: '',
    sortOrder: '',
    status: '01',
  });
  const drawer = ref(false);
  const isEdit = ref(true);
  const formAccount = ref();
  const deptCode = ref('');
  const title = ref('');
  const selectionIds = ref<string[]>([]);
  const userId = ref('');
  const loading = ref(false);
  const uploadLoading = ref(false);
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      // currentIsLeaf.value = e.isLeaf;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  const handleCheckBox = (data) => {
    sub.value = data;
    accountList();
  };
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    accountList();
  };
  const accountList = () => {
    const { pageNum, pageSize } = page.value;
    const { empName, email, industryType, instName, empCode, status, phone } =
      searchRef?.value?.['searchValue'] || {};
    getUserList({
      opType: 'tenant',
      industryType,
      instName,
      empCode,
      status,
      pageNum,
      pageSize,
      instCode: deptCode.value,
      deptId: deptId.value,
      empName,
      phone: phone ? encryptedSM4(phone) : '',
      email: email ? encryptedSM4(email) : '',
      sub: sub.value ? '1' : '0',
      ...sortData.value,
      selfInstCode: userInfo.sysEmployee.instCode,
    }).then((res: AccountListGetResultModel) => {
      const { list, total, currentPage, pageSize } = res;
      tableData.value = list;
      page.value = {
        total,
        pageNum: currentPage,
        pageSize,
      };
    });
  };
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    accountList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    accountList();
  };

  const confirmClick = () => {
    loading.value = true;
    const getData = formAccount.value.submitForm;
    const ruleFormRef = formAccount.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        if (!data.id) {
          checkFlag.value = {
            phone: true,
            email: true,
            idCard: true,
          };
        }
        data.opType = 'tenant';
        saveUser({
          ...data,
          deptId: detaileDeptId.value,
          phone: checkFlag.value.phone ? encryptedSM4(data.phone) : undefined,
          idCard: checkFlag.value.idCard ? encryptedSM4(data.idCard) : undefined,
          email: checkFlag.value.email ? encryptedSM4(data.email) : undefined,
        })
          .then(() => {
            drawer.value = false;
            ElMessage({
              type: 'success',
              showClose: true,
              message: data.id ? '修改员工成功' : '新增员工成功',
            });
            loading.value = false;
            accountList();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const handleAdd = () => {
    if (!!userInfo.tenantId) {
      drawer.value = true;
      title.value = '新增员工';
      isEdit.value = true;
      userId.value = '';
      formData.value = {
        id: '',
        gender: '',
        instCode: deptCode.value,
        instName: instName.value,
        deptName: deptName.value,
        deptId: deptId.value,
        email: '',
        empName: '',
        industryType: '',
        phone: '',
        empCode: '',
        empPost: '',
        empType: '',
        nodeCode: '',
        serviceCode: '',
        sortOrder: '',
        status: '01',
      };
      detaileDeptId.value = deptId.value;
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择租户',
      });
    }
  };

  const handleEdit = (item) => {
    drawer.value = true;
    title.value = '编辑员工';
    isEdit.value = true;
    userId.value = item.id;
    checkFlag.value = {
      phone: false,
      email: false,
      idCard: false,
    };
    allAccountFormSchema.value = cloneDeep(allAccountForm);
    getEmployeeDetail(item.id).then((res) => {
      detaileDeptId.value = res.deptId;
      formData.value = res;
    });
  };
  const handleDetail = (item) => {
    allAccountFormSchema.value = cloneDeep(allAccountForm);
    isEdit.value = false;
    drawer.value = true;
    title.value = '查看员工';
    userId.value = item.id;
    getEmployeeDetail(item.id).then((res) => {
      formData.value = res;
    });
  };

  const handleCurrentChange = (val) => {
    selectionIds.value = [];
    for (let i = 0; i < val.length; i++) {
      selectionIds.value.push(val[i].id);
    }
  };
  const handleDelete = (item, type) => {
    // type 1 批量删除  0 单独删除
    let title = '';
    if (type === 1) {
      title = `确认删除当前所选中${selectionIds.value.length}条员工数据？`;
    } else {
      title = '确认删除该员工？';
    }
    ElMessageBox.confirm(title, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        let params = {};
        if (item === null) {
          params = selectionIds.value;
        } else {
          params = [item.id];
        }
        moreDeleteApi({ ids: params }).then(() => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '员工删除成功',
          });
          accountList();
          if (type === '1') {
            selectionIds.value = [];
            tableRef.value!.clearSelection();
          }
        });
      })
      .catch(() => {});
  };

  const callDelete = (params) => {
    moreDeleteApi({ ids: params }).then(() => {
      ElMessage({
        showClose: true,
        type: 'success',
        message: '员工删除成功',
      });
      selectionIds.value = [];
      tableRef.value!.clearSelection();
      accountList();
    });
  };
  const {
    batchConfirmationVisible,
    batchDeletion,
    batchConfirmationSave,
    getPassword,
    batchConfirmationLoading,
  } = userBatchConfirmation(selectionIds, callDelete);

  // moreDeleteApi
  function querySearchEmail(queryString, callback) {
    const emailList = [
      { value: '@chinapost.com.cn' },
      { value: '@qq.com' },
      { value: '@163.com' },
      { value: '@sina.com' },
      { value: '@sohu.com' },
      { value: '@yahoo.com.cn' },
    ];
    let results: string[] = [];
    let queryList: any[] = [];
    emailList.map((item) =>
      queryList.push({ value: queryString.split('@')[0] + item.value }),
    );
    results = queryString ? queryList.filter(createFilter(queryString)) : queryList;
    callback(results);
  }
  // 邮箱填写过滤
  function createFilter(queryString) {
    return (item) => {
      return item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
    };
  }
  function downLoad() {
    const { empName, industryType, instName, empCode, status } =
      searchRef?.value?.['searchValue'] || {};
    const params = {
      employeeName: empName,
      industryType,
      instName,
      empCode,
      status,
      deptId: deptId.value,
      sub: sub.value ? '1' : '0',
      ...sortData.value,
      inlet: '0',
    };

    tenantUserdownloadUrlApi(params).then((res) => {
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
      });
      const fileName = '租户管理-员工管理' + '.xlsx';
      const elink = document.createElement('a'); // 创建a标签
      elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click(); // 点击下载
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink); // 释放标签
    });
  }
  const handlefileVisible = () => {
    if (!!userInfo.tenantId) {
      fileVisible.value = true;
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择租户',
      });
    }
  };
  const uploadFile = () => {
    if (!!userInfo.tenantId) {
      uploadLoading.value = true;
      let formData = new FormData();
      formData.append('file', fileData.value?.raw);
      tenantUserUploadApi(formData)
        .then(() => {
          ElMessage({
            type: 'success',
            message: '导入成功',
          });
          fileVisible.value = false;
          accountList();
          uploadLoading.value = false;
        })
        .catch(() => {
          uploadLoading.value = false;
        });
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择租户',
      });
    }
  };
  const getAccurate = (flag) => {
    currentFlag.value = flag;
    accurateVisible.value = true;
  };
  const confirmAccurate = () => {
    if (!accuratePassWord.value) {
      ElMessage({
        message: '请输入密码确认！',
        grouping: true,
        type: 'warning',
      });
    } else {
      accurate(accuratePassWord.value, formData.value.id).then((res) => {
        accurateVisible.value = false;
        let pattern = '';
        if (currentFlag.value == 'phone') {
          formData.value.phone = res.phone;
          pattern = phoneReg;
          checkFlag.value.phone = true;
        } else if (currentFlag.value == 'email') {
          formData.value.email = res.email;
          pattern = emailReg;
          checkFlag.value.email = true;
        } else {
          formData.value.idCard = res.idCard;
          pattern = card;
          checkFlag.value.idCard = true;
        }
        allAccountFormSchema.value.map((item, index) => {
          if (item.field == currentFlag.value) {
            allAccountFormSchema.value[index].rules[0].pattern = pattern;
          }
        });
        accuratePassWord.value = '';
      });
    }
  };
  function getAllParentNames(tree, result) {
    result.push(tree.instName);
    if (tree._parent) {
      //存在子节点就递归
      getAllParentNames(tree._parent, result);
    } else {
      deptName.value = result.reverse().join('/');
    }
  }
  watch(
    () => nodeId.value,
    (val) => {
      if (!!val) {
        const currentNode = tree.value.getNode(nodeId.value);
        getAllParentNames(currentNode, []);
        instName.value = currentNode.instName;
        deptId.value = val;
        accountList();
      } else {
        deptId.value = '';
        accountList();
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;

    .mid {
      height: 100%;
      // box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }

  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: calc(100vh - 30px);
      .active {
        background-color: #c9e9f7;
      }
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 12px 20px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    // background: #fff;
    // border-radius: 6px;
    .search-div {
      border-radius: 6px;
      background: #fff;
      // padding: 10px 16px;
      .title {
        // border-bottom: 1px solid #e4e7ed;
        // line-height: 68px;

        font-size: 18px;
        padding: 20px 0 6px 20px;
        // background: #ffffff;
        font-weight: 700;
        color: #333333;
      }
    }
    .table-top-div {
      display: flex;
      justify-content: space-between;
      // line-height: 64px;
      margin-bottom: 20px;
      p {
        font-size: 18px;
        color: #333333;
        font-weight: 700;
        margin: 0;
        padding: 0;
        line-height: 32px;
      }
    }

    .table-content {
      margin-top: 16px;
      background: #ffffff;
      height: calc(100% - 180px);
      padding: 20px 20px 0 20px;
      // position: relative;
      overflow: auto;
      border-radius: 6px;
      .top-button {
        // position: absolute;
        // top: 10px;
        // left: 16px;
        z-index: 9;
        .upload-btn {
          display: inline-block;
          margin: 0 12px;
        }
      }
    }
  }

  // .table-content {
  //   background: #ffffff;
  //   margin-top: 20px;
  // }
  h4 {
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 700;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
  .user-edit {
    background: red;
    .user-check-full {
      padding-left: 10px;
      cursor: pointer;
      color: #1a5efe;
      font-size: 26px;
    }
  }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }

  .file-dialog .el-dialog__body {
    padding: 20px;
  }
</style>
