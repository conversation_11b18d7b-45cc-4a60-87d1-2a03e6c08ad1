<!--
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
-->
<template>
  <div class="tool-container">
    <div class="tool-header">
      <Icon icon="carbon:tool-box" />
      <div class="tool-name">{{ toolData.toolName }}</div>
      <div class="tool-status" :class="getStatusClass(toolData.status)">
        {{ getStatusText(toolData.status) }}
      </div>
      <!-- 添加详细信息查看按钮 -->
      <button
        v-if="toolData.status === 'completed' && canShowDetails"
        class="detail-btn"
        @click="showToolDetails"
        title="查看工具详细信息"
      >
        <Icon icon="carbon:information" />
        <span>详情</span>
      </button>
    </div>

    <div class="tool-content">
      <!-- 工具参数 -->
      <div v-if="toolData.parameters" class="tool-params">
        <div
          class="tool-section-title"
          :class="{ collapsed: paramsCollapsed }"
          @click="toggleParams"
        >
          <span>参数</span>
          <Icon icon="carbon:chevron-down" class="collapse-icon" />
        </div>
        <div class="tool-section-content" :class="{ collapsed: paramsCollapsed }">
          <pre>{{ formatJson(toolData.parameters) }}</pre>
        </div>
      </div>

      <!-- 工具结果 -->
      <div v-if="toolData.result !== undefined" class="tool-result">
        <div
          class="tool-section-title"
          :class="{ collapsed: resultCollapsed }"
          @click="toggleResult"
        >
          <span>结果</span>
          <Icon icon="carbon:chevron-down" class="collapse-icon" />
        </div>
        <div class="tool-section-content" :class="{ collapsed: resultCollapsed }">
          <!-- SQL查询结果显示 -->
          <div v-if="toolData.sqlQueryData" class="sql-query-result">
            <!-- SQL信息 -->
            <div class="sql-info">
              <div class="sql-header">
                <Icon icon="carbon:sql" />
                <span>SQL查询</span>
              </div>
              <div class="sql-details">
                <div class="sql-item">
                  <strong>SQL:</strong>
                  <pre class="sql-code">{{ toolData.sqlQueryData.sql }}</pre>
                </div>
                <div class="sql-stats">
                  <span><strong>执行时间:</strong> {{ toolData.sqlQueryData.duration || 0 }}ms</span>
                  <span><strong>返回行数:</strong> {{ toolData.sqlQueryData.rowCount || 0 }}</span>
                </div>
              </div>
            </div>

            <!-- 图表显示 -->
            <div v-if="toolData.sqlQueryData.chart_config" class="chart-container">
              <div class="chart-header">
                <Icon icon="carbon:chart-pie" />
                <span>{{ toolData.sqlQueryData.chart_config.title?.text || '数据图表' }}</span>
              </div>
              <div class="chart-content">
                <div ref="chartRef" class="chart-canvas"></div>
              </div>
            </div>

            <!-- 数据表格 -->
            <div v-if="toolData.sqlQueryData.data && toolData.sqlQueryData.data.length > 0" class="data-table">
              <div class="table-header">
                <Icon icon="carbon:table" />
                <span>数据表格 ({{ toolData.sqlQueryData.data.length }} 行)</span>
              </div>
              <div class="table-content">
                <table class="result-table">
                  <thead>
                    <tr>
                      <th v-for="column in toolData.sqlQueryData.columns" :key="column.name">
                        {{ column.name }}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(row, index) in toolData.sqlQueryData.data" :key="index">
                      <td v-for="column in toolData.sqlQueryData.columns" :key="column.name">
                        {{ row[column.name] }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- Terminate消息显示 -->
          <div v-else-if="toolData.terminateMessage" class="terminate-message">
            <div class="terminate-header">
              <Icon icon="carbon:checkmark-filled" />
              <span>执行总结</span>
            </div>
            <div class="terminate-content">
              <pre>{{ toolData.terminateMessage }}</pre>
            </div>
          </div>

          <!-- 原始结果 -->
          <div v-else class="tool-result-content">
            <pre v-if="typeof toolData.result === 'string'">{{ toolData.result }}</pre>
            <pre v-else>{{ formatJson(toolData.result) }}</pre>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="toolData.status === 'running'" class="tool-loading">
        <div class="loading-spinner"></div>
        <span>工具执行中...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, watch, computed } from 'vue'
import { Icon } from '@iconify/vue'
import * as echarts from 'echarts'

interface ToolData {
  agentId: string
  toolName: string
  parameters?: any
  status: 'running' | 'completed' | 'error'
  startTime: Date
  endTime?: Date
  result?: any
  sqlQueryData?: {
    success: boolean
    data: any[]
    columns: any[]
    chart_config?: any
    sql: string
    rowCount: number
    duration?: number
    message: string
  }
  terminateMessage?: string
  thinkActId?: number  // 添加ThinkActRecord的ID，用于查询详细信息
}

const props = defineProps<{
  toolData: ToolData
}>()

const paramsCollapsed = ref(false)
const resultCollapsed = ref(false)
const chartRef = ref<HTMLDivElement>()
let chartInstance: echarts.ECharts | null = null

// 计算是否可以显示详细信息
const canShowDetails = computed(() => {
  return props.toolData.thinkActId && props.toolData.thinkActId > 0
})

// 显示工具详细信息
const showToolDetails = async () => {
  if (!props.toolData.thinkActId) {
    console.warn('没有ThinkActRecord ID，无法查看详细信息')
    return
  }

  try {
    console.log('🔍 查看工具详细信息:', props.toolData.thinkActId)

    // 调用API获取详细信息
    const response = await fetch(`/api/think-act/${props.toolData.thinkActId}/detail`)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const detailData = await response.json()
    console.log('📋 获取到的详细信息:', detailData)

    // 创建详细信息弹窗
    showDetailModal(detailData)

  } catch (error) {
    console.error('❌ 获取工具详细信息失败:', error)
    alert('获取详细信息失败: ' + error.message)
  }
}

// 显示详细信息模态框
const showDetailModal = (detailData: any) => {
  // 创建模态框内容
  const modalContent = `
    <div class="tool-detail-modal">
      <div class="modal-header">
        <h3>工具执行详细信息</h3>
        <span class="tool-name-badge">${detailData.toolName || '未知工具'}</span>
      </div>

      <div class="modal-content">
        <div class="detail-section">
          <h4>🧠 思考阶段</h4>
          <div class="detail-item">
            <label>思考输入:</label>
            <pre class="detail-text">${detailData.thinkDetails?.thinkInput || '无'}</pre>
          </div>
          <div class="detail-item">
            <label>思考输出:</label>
            <pre class="detail-text">${detailData.thinkDetails?.thinkOutput || '无'}</pre>
          </div>
        </div>

        <div class="detail-section">
          <h4>⚡ 执行阶段</h4>
          <div class="detail-item">
            <label>工具参数:</label>
            <pre class="detail-text">${detailData.actionDetails?.toolParameters || '无'}</pre>
          </div>
          <div class="detail-item">
            <label>执行结果:</label>
            <pre class="detail-text">${detailData.actionDetails?.actionResult || '无'}</pre>
          </div>
        </div>

        <div class="detail-section">
          <h4>📊 执行信息</h4>
          <div class="detail-grid">
            <div class="detail-item">
              <label>状态:</label>
              <span class="status-badge status-${detailData.status?.toLowerCase()}">${detailData.status || '未知'}</span>
            </div>
            <div class="detail-item">
              <label>开始时间:</label>
              <span>${formatDetailTime(detailData.thinkStartTime)}</span>
            </div>
            <div class="detail-item">
              <label>结束时间:</label>
              <span>${formatDetailTime(detailData.actEndTime)}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="close-btn" onclick="this.closest('.tool-detail-overlay').remove()">关闭</button>
      </div>
    </div>
  `

  // 创建模态框覆盖层
  const overlay = document.createElement('div')
  overlay.className = 'tool-detail-overlay'
  overlay.innerHTML = modalContent

  // 添加样式
  overlay.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: 20px;
  `

  // 点击覆盖层关闭
  overlay.addEventListener('click', (e) => {
    if (e.target === overlay) {
      overlay.remove()
    }
  })

  // 添加到页面
  document.body.appendChild(overlay)
}

// 格式化详细信息中的时间
const formatDetailTime = (timeStr: string) => {
  if (!timeStr) return '未知'
  try {
    return new Date(timeStr).toLocaleString('zh-CN')
  } catch {
    return timeStr
  }
}

const toggleParams = () => {
  paramsCollapsed.value = !paramsCollapsed.value
}

const toggleResult = () => {
  resultCollapsed.value = !resultCollapsed.value
}

const getStatusClass = (status: string) => {
  return `status-${status}`
}

const getStatusText = (status: string) => {
  const statusMap = {
    'running': '执行中',
    'completed': '完成',
    'error': '错误'
  }
  return statusMap[status] || status
}

const formatJson = (obj: any) => {
  try {
    return JSON.stringify(obj, null, 2)
  } catch {
    return String(obj)
  }
}

// 渲染图表
const renderChart = () => {
  if (!chartRef.value || !props.toolData.sqlQueryData?.chart_config) {
    return
  }

  try {
    // 销毁现有图表实例
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }

    // 创建新的图表实例
    chartInstance = echarts.init(chartRef.value)

    // 设置图表配置
    const option = {
      ...props.toolData.sqlQueryData.chart_config,
      animation: true,
      animationDuration: 1000,
      animationEasing: 'cubicOut'
    }

    console.log('📊 渲染图表配置:', option)
    chartInstance.setOption(option)

    // 监听窗口大小变化
    const resizeObserver = new ResizeObserver(() => {
      if (chartInstance) {
        chartInstance.resize()
      }
    })
    resizeObserver.observe(chartRef.value)

  } catch (error) {
    console.error('❌ 渲染图表失败:', error)
  }
}

// 监听图表数据变化
watch(
  () => props.toolData.sqlQueryData?.chart_config,
  (newConfig) => {
    if (newConfig) {
      nextTick(() => {
        renderChart()
      })
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.tool-container {
  margin: 8px 0;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(245, 158, 11, 0.02));
  overflow: hidden;
}

.tool-header {
  background: #f59e0b;
  color: white;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  font-size: 0.85rem;
}

.tool-name {
  flex: 1;
}

.tool-status {
  font-size: 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;

  &.status-not-started {
    background: rgba(107, 114, 128, 0.2);
  }

  &.status-in-progress {
    background: rgba(251, 191, 36, 0.2);
  }

  &.status-completed {
    background: rgba(16, 185, 129, 0.2);
  }

  &.status-error {
    background: rgba(239, 68, 68, 0.2);
  }
}

.detail-btn {
  margin-left: auto;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.tool-content {
  padding: 0;
  background: rgba(245, 158, 11, 0.03);
}

.tool-params, .tool-result {
  border-bottom: 1px solid rgba(245, 158, 11, 0.1);
}

.tool-result {
  border-bottom: none;
}

.tool-section-title {
  background: rgba(245, 158, 11, 0.08);
  padding: 8px 12px;
  font-weight: 500;
  font-size: 0.8rem;
  color: #d97706;
  border-bottom: 1px solid rgba(245, 158, 11, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(245, 158, 11, 0.12);
  }

  .collapse-icon {
    font-size: 0.7rem;
    transition: transform 0.2s ease;
    color: #d97706;
  }

  &.collapsed .collapse-icon {
    transform: rotate(-90deg);
  }
}

.tool-section-content {
  padding: 12px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.8rem;
  line-height: 1.5;
  white-space: pre-wrap;
  color: #374151;
  background: white;
  overflow: hidden;
  transition: max-height 0.3s ease, padding 0.3s ease;
  max-height: 1000px;

  pre {
    margin: 0;
    font-family: inherit;
    white-space: pre-wrap;
    word-wrap: break-word;
  }

  &.collapsed {
    max-height: 0;
    padding: 0 12px;
  }
}

.tool-result-content {
  color: #059669;
  min-height: 24px;
}

// SQL查询结果样式
.sql-query-result {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sql-info {
  padding: 12px;
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(16, 185, 129, 0.02));
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 6px;
}

.sql-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #10b981;
  font-size: 0.9rem;
}

.sql-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.sql-item {
  font-size: 0.85rem;
  line-height: 1.4;

  strong {
    color: #059669;
    margin-right: 8px;
  }
}

.sql-stats {
  display: flex;
  gap: 16px;
  font-size: 0.8rem;
  color: #6b7280;

  span {
    strong {
      color: #374151;
    }
  }
}

.sql-code {
  margin: 4px 0 0 0;
  padding: 8px;
  background: rgba(16, 185, 129, 0.08);
  border-radius: 4px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.8rem;
  color: #047857;
  border: 1px solid rgba(16, 185, 129, 0.15);
}

// 图表容器样式
.chart-container {
  padding: 12px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(99, 102, 241, 0.02));
  border: 1px solid rgba(99, 102, 241, 0.2);
  border-radius: 6px;
}

.chart-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #6366f1;
  font-size: 0.9rem;
}

.chart-content {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-canvas {
  width: 100%;
  height: 300px;
}

// 数据表格样式
.data-table {
  padding: 12px;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.05), rgba(245, 158, 11, 0.02));
  border: 1px solid rgba(245, 158, 11, 0.2);
  border-radius: 6px;
}

.table-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #d97706;
  font-size: 0.9rem;
}

.table-content {
  max-height: 400px;
  overflow: auto;
}

.result-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.8rem;

  th, td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid rgba(245, 158, 11, 0.15);
  }

  th {
    background: rgba(245, 158, 11, 0.08);
    font-weight: 600;
    color: #92400e;
    position: sticky;
    top: 0;
  }

  td {
    color: #374151;
  }

  tr:hover {
    background: rgba(245, 158, 11, 0.03);
  }
}

// Terminate消息样式
.terminate-message {
  padding: 12px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05), rgba(34, 197, 94, 0.02));
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: 6px;
}

.terminate-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
  color: #22c55e;
  font-size: 0.9rem;
}

.terminate-content {
  font-size: 0.85rem;
  line-height: 1.6;
  color: #374151;

  pre {
    margin: 0;
    font-family: inherit;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

.tool-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-style: italic;
  padding: 12px;
}

.loading-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #d97706;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
