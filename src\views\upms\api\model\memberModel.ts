import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface UserListItem {
  id: string;
  tenantId: string;
  userId: string;
  createdDate: string;
  userVo: Object;
}

export type getTenantUserPramas = BasicPageParams & {
  tenantId?: number | string;
  projectId?: string;
  opType?: string;
  deptId?: string;
  available?: string;
  userName?: string;
  employeeName?: string;
  sub?: string;
  orderBy?: string;
  sortOrder?: string;
  selfInstCode?: string;
  selfDeptId?: string;
};
export interface addupdateUser {
  id?: string | number;
  userId: string;
  roleIds: string[];
  tenantId?: string;
  projectId?: string;
}
/**
 * @description: Request list return value
 */
export type UserListGetResultModel = BasicFetchResult<UserListItem> & {
  list: [];
  pageNum: number;
  pageSize: number;
  total: number;
  currentPage?: number;
};

export interface deleteTenantUserPramas {
  userId: string;
  tenantId?: string;
  projectId?: string;
}

export interface tableItem {
  id?: string | number;
  userId: string[];
  roleIds: string[];
  tenantId?: string;
  projectId?: string;
  userVo?: any;
}

export interface formSearchType {
  tenantId?: string;
  projectId?: string;
}

export interface tenantUserItem {
  userName: string;
  id: string;
}
export interface tenantRoleItem {
  label: string;
  value: string;
}

export interface UserParams {
  opType?: string;
}

export interface SearchType {
  opType?: string;
}
