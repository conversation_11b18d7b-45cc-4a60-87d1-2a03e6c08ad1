import { sysHttp } from '/@/views/upms/common/http';
import {
  getTenantUserPramas,
  addupdateUser,
  deleteTenantUserPramas,
  UserListGetResultModel,
  SearchType,
  UserParams,
} from './model/memberModel';
import {
  DeleteProjectUserDeptRoleLinkPramas,
  UserDeptRoleLinkSeachParams,
} from './model/memberUserDeptRoleLInkModel';
enum Api {
  qryTenantUser = '/tenant-user/qryTenantUser', //成员列表
  delUser = '/tenant-user/delUser', //删除成员
  editUser = '/tenant-user/editUser', //新增、修改成员
  TenantUser = '/user/getUsers', //获取平台下的用户
  ComboData = '/role/combo-data', //租户下的角色
  qryProjectUser = '/project-user/qryProjectUser', //项目管理-成员列表
  syncUserDeptURL = '/project/dept/role/sync', //成员机构同步
  projecteditUser = '/project-user/editUser', //项目管理-新增、修改成员
  projectDelUser = '/project-user/delUser', //项目管理-删除成员
  projectAddUserDeptRole = '/project/dept/role/add', //项目管理-新增、修改成员
  projectEditUserDeptRole = '/project/dept/role/edit', //项目管理-新增、修改成员
  getProjectUserDeptRole = '/project/dept/role/', //获取成员对应的机构-角色信息
  delProjectUserDeptRole = '/project/dept/role/', //获取成员对应的机构-角色信息
}

// 成员列表
export const GetqryTenantUser = (params: getTenantUserPramas) =>
  sysHttp.get<UserListGetResultModel>({ url: Api.qryTenantUser, params });
// 新增成员|修改成员
export const PosteditUser = (params: addupdateUser) =>
  sysHttp.post<addupdateUser>({ url: Api.editUser, params });
// 删除成员
export const DeleteUser = (params) => sysHttp.post({ url: `${Api.delUser}`, params });
// 获取账号字典表
export const GetTenantUser = (params: UserParams) =>
  sysHttp.post({ url: `${Api.TenantUser}`, params: params });
// 获取租户下角色字典表
export const GetComboData = (params: SearchType) => {
  return sysHttp.post({
    url: Api.ComboData,
    params,
  });
};
//项目管理-成员列表
export const GetqryProjectUser = (params: getTenantUserPramas) =>
  sysHttp.get<UserListGetResultModel>({ url: Api.qryProjectUser, params });
// 项目管理-新增成员|修改成员
export const PostProjectEditUser = (params: addupdateUser) =>
  sysHttp.post<addupdateUser>({ url: Api.projecteditUser, params });
// 删除成员
export const projectDeleteUser = (params: deleteTenantUserPramas) =>
  sysHttp.post({ url: `${Api.projectDelUser}`, params });

// 应用管理-新增|修改  成员-机构-角色 绑定关系
export const postProjectEditUserDeptRole = (params: addupdateUser) =>
  sysHttp.post<addupdateUser>({ url: Api.projectEditUserDeptRole, params });

// 应用管理-新增|修改  成员-机构-角色 绑定关系
export const getProjectUserDeptRoleLink = (params: UserDeptRoleLinkSeachParams) =>
  sysHttp.get({ url: Api.getProjectUserDeptRole, params }, { joinParamsToUrl: true });

// 成员-机构-角色 绑定关系
export const projectDeleteUserDeptRoleLink = (
  params: DeleteProjectUserDeptRoleLinkPramas,
) => sysHttp.delete({ url: `${Api.delProjectUserDeptRole}`, params });

// 成员-机构-角色 同步
export const syncUserDept = (params: addupdateUser) =>
  sysHttp.post({ url: `${Api.syncUserDeptURL}`, params });
