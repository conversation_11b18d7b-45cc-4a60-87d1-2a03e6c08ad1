import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface UserListItem {
  id: string;
  tenantId: string;
  userId: string;
  createdDate: string;
  userVo: Object;
}

export type getTenantUserPramas = BasicPageParams & {
  tenantId?: number | string;
  projectId?: string;
  opType?: string;
};
export interface addupdateUser {
  id?: string | number;
  userId: string;
  roleIds: string[];
  tenantId?: string;
  projectId?: string;
}

/**
 * @description: Request list return value
 */
export type UserListGetResultModel = BasicFetchResult<UserListItem> & {
  list: [];
  pageNum: number;
  pageSize: number;
  total: number;
  currentPage?: number;
};

export interface deleteTenantUserPramas {
  userId: string;
  tenantId?: string;
  projectId?: string;
}
export interface TableItem {
  id?: string | number;
  userId: string[];
  roleIds?: string[];
  tenantId?: string;
  projectId?: string;
  userVo?: any;
}

export interface LinkTableItem {
  id?: string | number;
  userId: string;
  deptId?: string;
  deptName?: string[];
  roleIds?: string[];
  tenantId?: string;
  projectId?: string;
  userVo?: any;
}

export interface LinkMemberForm {
  id?: string | number;
  userId: string;
  userName?: string;
  deptId?: string;
  shortName?: string;
  roleIds?: string[];
  roleNames?: string[];
  tenantId?: string;
  projectId?: string;
  options?: any;
}

export interface formSearchType {
  tenantId?: string;
  projectId?: string;
}

export interface tenantUserItem {
  userName: string;
  id: string;
}
export interface tenantRoleItem {
  label: string;
  value: string;
}

export interface UserParams {
  opType?: string;
}

export interface SearchType {
  opType?: string;
}

export type UserDeptRoleLinkSeachParams = BasicPageParams & {
  userId?: string;
  projectId?: string;
};

export interface DeleteProjectUserDeptRoleLinkPramas {
  userId: string;
  deptId: string;
}
