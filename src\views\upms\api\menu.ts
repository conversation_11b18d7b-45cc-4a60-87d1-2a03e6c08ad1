import { sysHttp, downloadHttp, uploadHttp } from '../common/http';
import {
  IgetMenuTreeSpace,
  IgetMenuByIdSpace,
  MenuListParams,
  deleteMenuType,
  UserMenuParams,
  DownLoadParmas,
} from './model/menuModel';
enum Api {
  GetUserMenu = '/menu/user-tree',
  menuList = '/menu/menuList',
  downloadUrl = '/menu/download',
  uploadUrl = '/menu/upload',
  sortUrl = '/menu/change/sort',
}

/**
 * @description: Get user menu based on id
 */
// 获取menu树数据  租户管理下参数是MenuListParams类型;平台下的菜单参数为空
export const getRoleUserMenu = (params?: UserMenuParams) => {
  console.log('paramsparams', params);
  return sysHttp.get<IgetMenuTreeSpace.Data[]>({
    url: `/menu/auth-menu`,
    params: params,
  });
};
export const getUserMenu = (params?: MenuListParams) => {
  return sysHttp.get<IgetMenuTreeSpace.Data[]>({
    url: Api.GetUserMenu,
    params,
  });
};
// 根据id查详情
export const getMenuDetailById = (id: string) => {
  return sysHttp.get<IgetMenuByIdSpace.Data>({ url: `/menu/${id}` });
};
// 新增或者编辑
export const addOrEditMenuDetail = (params) => {
  return sysHttp.post({ url: `/menu/`, params });
};
// 删除
export const deleteMenu = (params: deleteMenuType) => {
  return sysHttp.delete({
    url: `/menu/${params.ids}${params.opType ? '?opType=' + params.opType : ''}`,
  });
};

// export const getMenu = () => {
//   return sysHttp.get({ url: `/user-menu` });
// };
// 删除
export const getMenuList = (params) => {
  return sysHttp.get({
    url: Api.menuList,
    params: params,
  });
};

export const downLoadApi = (params: DownLoadParmas) =>
  downloadHttp.get({ url: Api.downloadUrl, params });
//

export const uploadApi = (params) => {
  return uploadHttp.post({
    url: Api.uploadUrl,
    params,
  });
};

export const getSortUrlApi = (params) =>
  sysHttp.get({ url: Api.sortUrl, params: params });
