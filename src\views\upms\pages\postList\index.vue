<template>
  <div class="content">
    <div class="box">
      <div class="search-div">
        <p class="title"> 平台岗位搜索 </p>
        <basic-search
          :searchArray="searchFormSchema"
          class="search-all"
          ref="searchRef"
          :labelWidth="80"
          :labelShow="false"
          @reset="postList"
          @onSearch="postList"
        />
      </div>
      <div class="table-content">
        <div class="table-top-div">
          <p>平台岗位列表</p>
          <div class="top-button">
            <el-button
              type="primary"
              @click="handleAdd"
              v-auth="['platform_post_add']"
              :icon="Plus"
              >新增</el-button
            >
            <el-button
              type="primary"
              @click="downLoad(false)"
              v-auth="['platform_post_export']"
              :icon="Download"
              plain
              >导出</el-button
            >
            <el-button
              type="primary"
              @click="fileVisible = true"
              v-auth="['platform_post_import']"
              :icon="Upload"
              plain
              >导入</el-button
            >
            <el-button
              type="danger"
              @click="batchDeletion()"
              :disabled="selectionIds.length > 0 ? false : true"
              v-auth="['platform_post_delete']"
              :icon="Delete"
              plain
              >批量删除</el-button
            >
          </div>
        </div>
        <basic-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :total="page.total"
          :page-size="page.pageSize"
          :current-page="page.pageNum"
          @page-change="pageChange"
          @size-change="sizeChange"
          @selectionChange="handleCurrentChange"
          @sortChange="sortChange"
          :downSetting="true"
          height="calc(100vh - 392px)"
        >
          <template #display="{ record }">
            <span v-if="record.display === '1'"
              ><span class="active-span"></span> 启用</span
            >
            <span v-else type="danger"> <span class="close-span"></span> 禁用</span>
          </template>
          <template #action="{ record }">
            <el-button
              type="primary"
              link
              v-show="record.display === '0'"
              @click="handleDelete(record, '0')"
              v-auth="['platform_post_delete']"
              >删除</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleEdit(record)"
              v-show="record.display === '1'"
              v-auth="['platform_post_edit']"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleDetail(record)"
              v-show="record.display === '1'"
              v-auth="['platform_post_details']"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleStatus(record)"
              v-show="record.display === '1'"
              v-auth="['platform_post_enable_disable']"
              >禁用</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleStatus(record)"
              v-show="record.display !== '1'"
              v-auth="['platform_post_enable_disable']"
              >启用</el-button
            >
          </template>
        </basic-table>
      </div>
    </div>

    <el-drawer v-model="drawer" direction="rtl" size="600px" :destroy-on-close="true">
      <template #header>
        <h4>{{ title }}</h4>
      </template>
      <template #default>
        <basic-form
          :formList="postFormSchema"
          :isCreate="false"
          :formData="formData"
          :showSubmit="false"
          :check-strictly="true"
          :disabled="!isEdit"
          ref="formPost"
        />
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmClick"
            v-if="isEdit"
            :loading="loading"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>

    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
      class="file-dialog"
    >
      <el-button type="primary" link @click="downLoad(true)" style="margin-bottom: 12px"
        >下载平台岗位导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="batchConfirmationVisible"
      title="删除警告"
      width="400px"
      class="batch-Confirmation-dialog"
      :destroy-on-close="true"
    >
      <BatchConfirmation @getPassword="getPassword" />
      <template #footer>
        <el-button @click="batchConfirmationVisible = false">取消</el-button>
        <el-button
          type="info"
          @click="batchConfirmationSave"
          :loading="batchConfirmationLoading"
          >删除</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, markRaw } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { CircleClose, Plus, Download, Upload, Delete } from '@element-plus/icons-vue';
  import { columns, postFormSchema, searchFormSchema } from './post.data';
  import BasicTable from '/@/components/sys/BasicTable';
  import {
    getPostList,
    SavePostApi,
    getRank,
    DelPost,
    downLoadApi,
    uploadApi,
  } from '/@/views/upms/api/post';
  import {
    PostListResultModel,
    AddPostParams,
    PostListItem,
  } from '/@/views/upms/api/model/post';
  import useUpload from '/@/hooks/upms/upload/useUpload';
  import type { UploadInstance } from 'element-plus';
  import { cloneDeep } from 'lodash-es';
  import BatchConfirmation from '/@/views/upms/components/BatchConfirmation.vue';
  import userBatchConfirmation from '/@/views/upms/common/hooks/userBatchConfirmation';

  const upload = ref<UploadInstance>();
  const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const tableData = ref<PostListItem[]>([]);
  const searchRef = ref({});
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const formData = ref<AddPostParams>({
    postStationCode: '',
    postStationName: '',
    rankId: '',
    id: '',
    display: '1',
  });
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const drawer = ref(false);
  const isEdit = ref(true);
  const title = ref('新增');
  const formPost = ref();
  const selectionIds = ref<string[]>([]);

  const loading = ref(false);
  const uploadLoading = ref(false);
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    postList();
  };
  // 岗位列表
  const postList = () => {
    const { pageNum, pageSize } = page.value;
    const { display, postStationName, postStationCode } =
      searchRef?.value?.['searchValue'] || {};
    const params = {
      pageNum,
      pageSize,
      display,
      postStationName,
      postStationCode,
      ...sortData.value,
    };
    getPostList(params).then((res: PostListResultModel) => {
      tableData.value = res.list;
      page.value.total = res.total;
    });
  };
  /**
   * 切换分页，每页显示数量
   */
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    postList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    postList();
  };
  function rank() {
    getRank().then((res) => {
      postFormSchema.forEach((item) => {
        if (item.field === 'rankId') {
          item.componentProps.options = res;
        }
      });
    });
  }
  postList();
  rank();
  const confirmClick = () => {
    const getData = formPost.value.submitForm;
    const ruleFormRef = formPost.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        loading.value = true;
        if (!data.id) {
          delete data.postStationCode;
          data.display = '1';
        }
        console.log(data);
        SavePostApi(data)
          .then((res) => {
            console.log('res', res);
            if (res) {
              ElMessage({
                type: 'success',
                message: data.id ? `修改岗位成功` : `新增岗位成功`,
              });
              postList();
              drawer.value = false;
              loading.value = false;
            }
          })
          .catch(() => {
            loading.value = false;
          });
      }
    });
  };
  const handleAdd = () => {
    formData.value = {
      id: '',
      postStationCode: '',
      postStationName: '',
      rankId: '',
      display: '1',
    };
    isEdit.value = true;
    drawer.value = true;
    title.value = '新增岗位';
    postFormSchema.forEach((item: any) => {
      if (item.field == 'postStationCode') {
        item.ifShow = false;
        item.componentProps.disabled = false;
      }
    });
  };

  const handleEdit = (item) => {
    isEdit.value = true;
    drawer.value = true;
    title.value = '编辑岗位';
    postFormSchema.forEach((item: any) => {
      if (item.field == 'postStationCode') {
        item.ifShow = true;
        item.componentProps.disabled = true;
      }
    });
    formData.value = item;
  };
  const handleDetail = (item) => {
    isEdit.value = false;
    drawer.value = true;
    title.value = '查看岗位';
    formData.value = item;
  };
  const handleStatus = (item) => {
    // 禁用操作
    if (item.display === '1') {
      ElMessageBox.confirm('是否要禁用该岗位', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        customStyle: {
          backgroundColor: 'rgba(255, 241, 240, 1)',
          border: '1px soild rgba(255, 163, 158, 1)',
        },
      })
        .then(() => {
          const params = cloneDeep(item);
          params.display = '0';
          SavePostApi(params).then((res) => {
            console.log('res', res);
            if (res) {
              ElMessage({
                type: 'success',
                message: '禁用成功',
              });
              postList();
            }
          });
          // .catch(() => {
          //   item.display = '1';
          // });
        })
        .catch(() => {});
    } else {
      // item.display = '1';
      const params = cloneDeep(item);
      params.display = '1';
      SavePostApi(params).then((res) => {
        console.log('res', res);
        if (res) {
          ElMessage({
            type: 'success',
            message: '启用成功',
          });
          postList();
        }
      });
      // .catch(() => {
      //   item.display = '0';
      // });
    }
  };

  const handleDelete = (item, type) => {
    // type 1 批量删除  0 单独删除
    let title = '';
    if (type === 1) {
      title = `确认删除当前所选中${selectionIds.value.length}条岗位数据？`;
    } else {
      title = '确认删除该岗位？';
    }
    ElMessageBox.confirm(title, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        let params = '';
        if (item === null) {
          params = selectionIds.value.join(',');
        } else {
          params = item.id;
        }
        console.log(params);
        DelPost(params).then(() => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '岗位删除成功',
          });
          if (type === '1') {
            selectionIds.value = [];
            tableRef.value!.clearSelection();
          }

          postList();
        });
      })
      .catch(() => {});
  };

  const callDelete = (params) => {
    params = selectionIds.value.join(',');
    DelPost(params).then(() => {
      ElMessage({
        showClose: true,
        type: 'success',
        message: '岗位删除成功',
      });
      selectionIds.value = [];
      tableRef.value!.clearSelection();
      postList();
    });
  };
  const {
    batchConfirmationVisible,
    batchDeletion,
    batchConfirmationSave,
    getPassword,
    batchConfirmationLoading,
  } = userBatchConfirmation(selectionIds, callDelete);
  const handleCurrentChange = (val) => {
    selectionIds.value = [];
    for (let i = 0; i < val.length; i++) {
      selectionIds.value.push(val[i].id);
    }
  };

  const downLoad = (isTemplate) => {
    const { display, postStationName, postStationCode } =
      searchRef?.value?.['searchValue'] || {};
    const params = {
      display,
      postStationName,
      postStationCode,
      isTemplate,
    };
    downLoadApi(params).then((res) => {
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
      });
      const fileName = '平台岗位管理' + '.xlsx';
      const elink = document.createElement('a'); // 创建a标签
      elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click(); // 点击下载
      console.log(elink.href);
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink); // 释放标签
    });
  };

  const uploadFile = () => {
    uploadLoading.value = true;
    let formData = new FormData();
    formData.append('file', fileData.value?.raw);
    uploadApi(formData)
      .then(() => {
        ElMessage({
          type: 'success',
          message: '导入成功',
        });
        fileVisible.value = false;
        postList();
        uploadLoading.value = false;
      })
      .catch(() => {
        uploadLoading.value = false;
      });
  };
</script>

<style scoped lang="scss">
  /* 拖拽相关样式 */
  /*包围div样式*/

  .content {
    display: flex;
    height: calc(100vh - 94px);
    /* height: 500px; */
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    // display: flex;
    // background: #ffffff;

    // .title {
    //   border-bottom: 1px solid #e4e7ed;
    //   line-height: 68px;
    //   font-size: 18px;
    //   padding-left: 24px;
    //   background: #ffffff;
    //   font-weight: 700;
    // }

    // .search-all {
    //   background: #ffffff;
    //   padding-left: 12px;
    // }
    .search-div {
      border-radius: 6px;
      background: #fff;
      // padding: 10px 16px;
      .title {
        // border-bottom: 1px solid #e4e7ed;
        // line-height: 68px;

        font-size: 18px;
        padding: 20px 0 6px 20px;
        // background: #ffffff;
        font-weight: 700;
        color: #333333;
      }
    }
    .table-top-div {
      display: flex;
      justify-content: space-between;
      // line-height: 64px;
      margin-bottom: 20px;
      p {
        font-size: 18px;
        color: #333333;
        font-weight: 700;
        margin: 0;
        padding: 0;
        line-height: 32px;
      }
    }
    .table-content {
      margin-top: 16px;
      background: #ffffff;
      // height: calc(100% - 154px);
      // height: 100%;
      padding: 20px 20px 0 20px;
      position: relative;
      overflow: auto;
      .top-button {
        // position: absolute;
        // top: 10px;
        // left: 24px;
        z-index: 9;

        .upload-btn {
          display: inline-block;
          margin: 0 12px;
        }
      }
      .tip {
        position: absolute;
        top: 22px;
        right: 70px;
        z-index: 9;
        color: #409eff;
      }

      .active-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #00a854;
        margin-right: 5px;
      }
      .close-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #bfbfbf;
        margin-right: 5px;
      }
    }
    // .table-content {
    //   background: #ffffff;
    //   margin-top: 20px;
    //   /* padding-top: 18px; */
    // }
  }

  h4 {
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 700;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
  .file-dialog .el-dialog__body {
    padding: 20px;
  }
</style>
