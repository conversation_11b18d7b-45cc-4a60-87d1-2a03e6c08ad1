<template>
  <div>
    <basic-tree v-bind="$attrs" ref="treeRef" :data="data" :tree-title="treeTitle">
      <template #default="{ data: scopeData }">
        <span>{{ scopeData.label }}</span>
        <span class="text-gray-300 pl-2">{{
          scopeData.menuType == 'system'
            ? '- 系统'
            : scopeData.menuType == 'tenant'
            ? '- 租户'
            : '- 项目'
        }}</span>
      </template>
    </basic-tree>
  </div>
</template>
<script setup lang="ts">
  import { PropType, ref, watch } from 'vue';
  import { BasicTree } from '/@/components/sys/BasicTree';
  import { TreeItem } from '/@/components/sys/BasicTree/types';
  import { getRoleUserMenu } from '/@/views/upms/api/menu';

  const emits = defineEmits(['treeData']);

  const props = defineProps({
    treeTitle: {
      type: String as PropType<string>,
      default: () => '菜单数据',
    },
    open: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    getMenuListParams: {
      type: Object,
      default: () => {},
    },
  });
  const data = ref<TreeItem[]>([]);
  const treeRef = ref<InstanceType<typeof BasicTree>>();
  function init() {
    console.log('props.getMenuListParams', props.getMenuListParams);
    getRoleUserMenu(props.getMenuListParams).then((res) => {
      data.value = res as any[];
      emits('treeData', data.value);
    });
  }
  init();
  watch(
    () => props.open,
    (value) => {
      if (value) {
        init();
        // 触发 数据渲染的操作
        emits('treeData', data.value);
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
  // watch(
  //   () => props.getMenuListParams,
  //   (value) => {
  //     if (value) {
  //       init();
  //     }
  //   },
  //   {
  //     immediate: true,
  //     deep: true,
  //   },
  // );
  function setCheckedKeys(keys: string[] | number[]) {
    treeRef.value?.setCheckedKeys(keys);
  }

  function getHalfCheckedKeys() {
    return treeRef.value?.getHalfCheckedKeys();
  }

  function getTreeData() {
    return data.value;
  }

  defineExpose({ setCheckedKeys, getHalfCheckedKeys, getTreeData });
</script>
<style scoped>
  .tree-title {
    margin-right: 9px;
    font-size: 18px;
  }

  .tree-header {
    padding-bottom: 5px;
    margin: 5px 10px;
    border-bottom: 1px solid rgba(228, 228, 231);
  }
</style>
