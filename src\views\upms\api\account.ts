import {
  AccountParams,
  AccountListGetResultModel,
  ResetParams,
  DeptResultModel,
  RoleParams,
} from './model/accountModel';
import { sysHttp, downloadHttp, uploadHttp } from '/@/views/upms/common/http';

enum Api {
  AccountList = '/user/',
  TentantAccountList = '/tenant-user/invite',
  getRole = '/role/combo-data',
  resetPsssword = '/user/resetPassword',
  DeptList = '/dept/tree',
  PostList = '/post/station/deptIds/', // 获取岗位列表
  delUsers = '/user/delUsers',
  disabledUserUrl = '/user/disable',
  activationUserUrl = '/user/activation',
  unLockUserUrl = '/user/unLock',
  downloadUrl = '/user/download',
  uploadUrl = '/user/upload',
  tenantUploadUrl = '/user/upload?opType=tenant',
  // projectUploadUrl = '/user/upload?opType=project',
  uploadRoleUrl = '/user/updateRole',
  authRoleUrl = '/user/auth/role/',
  checkApi = '/user/check?userName=',
  projectDownloadUrl = '/project-user/download', // 账目账号导出
  projectUploadUrl = '/project-user/upload', // 账目账号导入
}
// 获取账号列表
export const getTentantAccountList = (params: AccountParams) =>
  sysHttp.get<AccountListGetResultModel>({ url: Api.TentantAccountList, params });
// 获取账号列表
export const getAccountList = (params: AccountParams) =>
  sysHttp.get<AccountListGetResultModel>({ url: Api.AccountList, params });
// 获取角色
export const getRole = (params: RoleParams) =>
  sysHttp.get({ url: Api.getRole, params: params });
// 修改用户信息
export const saveUser = (params) => sysHttp.post({ url: Api.AccountList, params });
// 锁定和激活
export const lockUser = (id: string) => sysHttp.put({ url: `${Api.AccountList}${id}` });

export const userApi = (id: string) => sysHttp.get({ url: `${Api.AccountList}/${id}` });
// 删除用户
export const delUser = (id: string) =>
  sysHttp.delete({ url: `${Api.AccountList}${id}` });
// 重置密码
export const resetPsssword = (params: ResetParams) =>
  sysHttp.post({ url: Api.resetPsssword, params });
// 获取部门列表
export const getDeptList = () => sysHttp.get<DeptResultModel>({ url: Api.DeptList });
// 获取岗位列表
export const getPostList = (ids: string) =>
  sysHttp.get({ url: `${Api.PostList}${ids}` });

// 获取岗位列表
export const getPostListLimit = (params) =>
  sysHttp.post({ url: `${Api.PostList}`, params });

export const moreDeleteApi = (params) => sysHttp.post({ url: Api.delUsers, params });

export const disabledUserApi = (id: string) =>
  sysHttp.put({ url: `${Api.disabledUserUrl}/${id}` });

export const activationUserApi = (id: string) =>
  sysHttp.put({ url: `${Api.activationUserUrl}/${id}` });

export const unLockUserApi = (id: string) =>
  sysHttp.put({ url: `${Api.unLockUserUrl}/${id}` });

export const userDownLoadApi = (params) =>
  downloadHttp.get({ url: Api.downloadUrl, params });
export const projectDownloadUrl = (params) =>
  downloadHttp.get({ url: Api.projectDownloadUrl, params });
//

export const uploadApi = (params) => {
  return uploadHttp.post({
    url: Api.uploadUrl,
    params,
  });
};
export const tenantUploadApi = (params) => {
  return sysHttp.post({
    url: Api.tenantUploadUrl,
    params,
  });
};
export const projectUploadApi = (params) => {
  return sysHttp.post({
    url: Api.projectUploadUrl,
    params,
  });
};

export const uploadRoleApi = (params) => {
  return sysHttp.post({
    url: Api.uploadRoleUrl,
    params,
  });
};
export const checkApi = (params) =>
  sysHttp.get({ url: `${Api.checkApi}${params.userName}&id=${params.id}` });

export const authRoleApi = (params) => sysHttp.get({ url: Api.authRoleUrl, params });
