/*
 * @Author: your name
 * @Date: 2021-08-17 09:14:58
 * @LastEditTime: 2021-08-17 09:42:04
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \cpit-upms-ui\src\api\admin\model\roleSystemModel.ts
 */
import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';
import { DataEntityVo } from '/@/api/model/DataEntityVo';

export type MenuParams = {
  menuName?: string;
  status?: string;
  tenantId?: string;
  projectId?: string;
  special?: Boolean;
};

export type RolePageParams = BasicPageParams & RoleParams;

export type RoleParams = {
  roleName?: string;
  status?: string;
  projectId?: string;
  tenantId?: string;
  opType?: string;
  code?: string;
  name?: string;
  available?: string;
};
export interface MenuListItemAll {
  id: string;
}

export interface MenuListItem {
  id: string;
}

export type MenuListGetResultModel = BasicFetchResult<MenuListItem>;

export type MenuListGetResultModelAll = BasicFetchResult<MenuListItemAll>;

export type RolePageListGetResultModel = BasicFetchResult<RoleListItem>;

export type RoleDict = BasicFetchResult<RoleDictItem>;

export interface RoleDictItem {
  codes: string;
}

export type saveRoleModel = BasicFetchResult<SaveRoleItem>;

export interface SaveRoleItem {
  isEdit?: boolean;
  codePrefix?: string;
  tenantId?: string;
  projectId?: string;
  id?: string;
  code: string; // 角色编码
  name: string; // 角色名称
  remark: string; // 描述
  dataScope: string; // 数据权限code
  available: string; // 状态
  description: string; // 描述
  deptIdList: string[]; // 机构id
  menuIdList: string[]; // 菜单id
  opType?: string; // 是否需要租户id或项目id
}

export interface RoleListItem extends SaveRoleItem, DataEntityVo {
  dataScopeText: string; // 数据权限描述
  availableText: string; // 状态描述
}

export interface MenuListParams {
  tenantId?: string;
  projectId?: string;
  special?: Boolean;
  opType?: string;
  roleId?: string;
}
export interface DeleteRoleType {
  ids: string;
  tenantId?: string;
  projectId?: string;
}
export type AuthorizedUser = BasicPageParams & {
  roleId: string;
  deptIds?: string[];
  auth?: Boolean;
  userName?: string;
};
