import {
  AccountParams,
  AccountListGetResultModel,
  EditAccountParams,
  ResetParams,
  DeptResultModel,
  RoleParams,
} from './model/accountModel';
import { sysHttp, downloadHttp, uploadHttp } from '/@/views/upms/common/http';

enum Api {
  UserList = '/employee/',
  getRole = '/role/combo-data',
  resetPsssword = '/user/resetPassword',
  DeptList = '/dept/tree',
  PostList = '/post/station/deptIds/', // 获取岗位列表
  Detail = '/employee/',
  info = '/employee/update',
  moreDeleteUrl = '/employee/del',
  checkUrl = '/employee/check',
  tenantUserdownloadUrl = '/tenant-user/download',
  tenantUserUploadUrl = '/user/upload?opType=tenant',
  accurate = '/employee/accurate?userPasswd='//userPasswd={密码}
}

// 获取账号列表
export const getUserList = (params: AccountParams) =>
  sysHttp.get<AccountListGetResultModel>({ url: Api.UserList, params });
// 获取角色
export const getRole = (params: RoleParams) =>
  sysHttp.get({ url: Api.getRole, params: params });
// 修改用户信息
export const saveUser = (params: EditAccountParams) =>
  sysHttp.post({ url: Api.UserList, params });
// 锁定和激活
export const lockUser = (id: string) => sysHttp.put({ url: `${Api.UserList}${id}` });
// 删除用户
export const delUser = (id: string) => sysHttp.delete({ url: `${Api.UserList}${id}` });
// 重置密码
export const resetPsssword = (params: ResetParams) =>
  sysHttp.post({ url: Api.resetPsssword, params });
// 获取部门列表
export const getDeptList = () => sysHttp.get<DeptResultModel>({ url: Api.DeptList });
// 获取岗位列表
export const getPostList = (ids: string) =>
  sysHttp.get({ url: `${Api.PostList}${ids}` });
// 获取用户信息详情
export const getEmployeeDetail = (params: string) =>
  sysHttp.get({ url: `${Api.Detail}${params}` });

export const updateInfo = (params) => sysHttp.post({ url: Api.info, params });
// moreDeleteUrl;

export const moreDeleteApi = (params) =>
  sysHttp.post({ url: Api.moreDeleteUrl, params });

export const checkApi = (params) => sysHttp.get({ url: Api.checkUrl, params });

// tenantUserdownloadUrl;

export const tenantUserdownloadUrlApi = (params) =>
  downloadHttp.get({ url: Api.tenantUserdownloadUrl, params });
//

export const tenantUserUploadApi = (params) => {
  return uploadHttp.post({
    url: Api.tenantUserUploadUrl,
    params,
  });
};
export const accurate = (userPasswd, employeeId) => {
  return sysHttp.get({
    url:`${Api.accurate}${userPasswd}&employeeId=${employeeId}`,
  });
};
