export const columns: any[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
    selectable: function (row) {
      if (row.display == '1') {
        return false;
      } else {
        return true;
      }
    },
  },
  {
    label: '岗位名称',
    prop: 'postStationName',
    sortable: 'custom',
    align: 'center',
  },
  {
    label: '岗位编号',
    prop: 'postStationCode',
    align: 'center',
    sortable: 'custom',
  },
  {
    label: '岗位职级',
    prop: 'rankName',
    align: 'center',
  },
  {
    label: '状态',
    prop: 'display',
    minWidth: 80,
    align: 'center',
    sortable: 'custom',
    slot: 'display',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    width: 180,
    align: 'center',
  },
];

export const searchFormSchema: any[] = [
  {
    field: 'postStationName',
    label: '岗位名称:',
    component: 'Input',
    span: 6,
    labelWidth: 80,
    placeholder: '请输入岗位名称',
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'postStationCode',
    label: '岗位编号:',
    component: 'Input',
    span: 6,
    labelWidth: 80,
    placeholder: '请输入岗位编号',
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'display',
    label: '状态',
    component: 'Select',
    placeholder: '请选择状态',
    componentProps: {
      clearable: true,
      options: [
        {
          label: '启用',
          value: '1',
        },
        {
          label: '禁用',
          value: '0',
        },
      ],
    },
    span: 6,
  },
];

export const postFormSchema: any[] = [
  {
    field: 'postStationName',
    label: '岗位名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位名称',
    },
    required: true,
  },
  {
    field: 'postStationCode',
    label: '岗位编号',
    component: 'Input',
    componentProps: {
      placeholder: '请输入岗位编号',
    },
    required: true,
  },
  {
    field: 'rankId',
    label: '岗位职级',
    component: 'Select',
    componentProps: {
      placeholder: '请输入岗位职级',
      options: [],
    },
    required: true,
  },
  {
    field: 'description',
    label: '岗位描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入描述',
      options: [],
    },
    // required: true,
  },
];
