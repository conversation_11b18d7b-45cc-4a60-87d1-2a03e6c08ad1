import { sysHttp, downloadHttp } from '../common/http';
import {
  LogListParams,
  LogListResult,
  LogDict,
  LogDictItem,
} from '/@/views/upms/api/model/LogModel';

enum Api {
  getLog = '/log-operate/', // 操作日志
  LogDictData = '/log-operate/getLogOperateType',
  AuditLogDictData = '/login/log/getLogOperateType',
  getLoginLog = '/login/log/', // 审计日志
  downloadUrl = '/log-operate/download', // 操作日志
  loginDownloadUrl = '/login/log/download', // 审计日志
}

// export const logApi = {
//   getLogList(params: LogListParams) {
//     // const paramUrl = JsonToUrl(params);
//     return sysHttp.get<LogListResult>({ url: `${Api.getLog}`, params });
//   },
// };

export const getLogList = (params: LogListParams) =>
  sysHttp.get<LogListResult>({ url: `${Api.getLog}`, params });

//TODO 传''请求不到数据，可以先不传
export const getLogDict = (params: LogDictItem) =>
  sysHttp.get<LogDict>({ url: Api.LogDictData, params });
export const getAuditLogDict = (params: LogDictItem) =>
  sysHttp.get<LogDict>({ url: Api.AuditLogDictData, params });

export const getLoginLogList = (params: LogListParams) =>
  sysHttp.get<LogListResult>({ url: `${Api.getLoginLog}`, params });
export const loginDownLoadApi = (params) =>
  downloadHttp.get({ url: Api.loginDownloadUrl, params });
export const downLoadApi = (params) =>
  downloadHttp.get({ url: Api.downloadUrl, params });
