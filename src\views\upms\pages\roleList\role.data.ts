// const codeReg = /^((?![\-]).)*$/;
import { TableOptions } from '/@/components/sys/BasicTable/types';
import { FormOptions } from '/@/components/sys/BasicForm/types';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
    selectable: function (row) {
      if (row.builtIn === 1) {
        return false;
      } else {
        return true;
      }
    },
  },
  {
    label: '角色编码',
    prop: 'code',
    width: 150,
    align: 'center',
  },
  {
    label: '角色名称',
    prop: 'name',
    width: 150,
    ellipsis: true,
    align: 'center',
  },
  {
    label: '角色描述',
    prop: 'description',
    ellipsis: true,
    align: 'center',
  },
  {
    label: '类型',
    prop: 'builtIn',
    slot: 'builtIn',
    width: 100,
    align: 'center',
  },
  {
    label: '状态',
    prop: 'available',
    slot: 'available',
    width: 80,
    align: 'center',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    width: 420,
    align: 'center',
    fixed: 'right',
  },
];
export const FormSchema: FormOptions[] = [
  {
    field: 'code',
    slot: 'code',
    label: '角色编码',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '角色编码',
      disabled: false,
    },
    rules: [
      { required: true, message: '请输入角色编码' },
      { max: 20, message: '超过长度限制，最长20', trigger: 'blur' },
    ],
  },
  {
    field: 'name',
    label: '角色名称',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '角色名称',
    },
    rules: [{ max: 30, message: '超过长度限制，最多30字', trigger: 'blur' }],
  },
  {
    field: 'description',
    label: '角色描述',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '角色描述',
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字', trigger: 'blur' }],
  },
  {
    field: 'dataScope',
    slot: 'dataScope',
    label: '数据权限',
    component: 'Select',
    required: true,
    colProps: { span: 24 }, // 该搜索框的宽度按 100%的宽度分为24份 8为1/3
    componentProps: {
      defaultValue: '1',
    },
  },
  {
    field: 'available',
    label: '角色状态',
    required: true,
    component: 'RadioGroup',
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '禁用', value: '0' },
        { label: '启用', value: '1' },
      ],
    },
  },
  {
    field: 'upUser',
    label: '更新用户',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '更新用户',
      disabled: true,
    },
    ifShow: false,
  },
  {
    field: 'lastModifiedDate',
    label: '更新日期',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '更新日期',
      disabled: true,
    },
    ifShow: false,
  },
  {
    field: 'createUser',
    label: '创建用户',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '创建用户',
      disabled: true,
    },
    ifShow: false,
  },
  {
    field: 'createDate',
    label: '创建日期',
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '创建日期',
      disabled: true,
    },
    ifShow: false,
  },
];
export const formDetailSchema: FormOptions[] = [
  {
    label: '角色编码：',
    field: 'code',
    component: 'Text',
  },
  {
    label: '角色名称：',
    field: 'name',
    component: 'Text',
  },
  {
    label: '角色描述：',
    field: 'description',
    component: 'Text',
  },
  {
    label: '数据权限：',
    field: 'dataScopeText',
    component: 'Text',
  },
  {
    label: '更新日期：',
    field: 'lastModifiedDate',
    component: 'Text',
  },
];

export const searchFormSchema: SearchOptions[] = [
  {
    field: 'code',
    label: '角色编码',
    component: 'Input',
    span: 6,
    placeholder: '请输入角色编码',
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'name',
    label: '角色名称',
    component: 'Input',
    span: 6,
    placeholder: '请输入角色名称',
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'available',
    label: '角色状态',
    component: 'Select',
    span: 6,
    placeholder: '请选择角色状态',
    componentProps: {
      clearable: true,
      options: [
        { label: '禁用', value: '0' },
        { label: '启用', value: '1' },
      ],
    },
  },
];
