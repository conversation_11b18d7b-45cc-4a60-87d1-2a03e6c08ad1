export function createHistroyData(
  listKey: string | string[] = [],
  listName: string | string[] = [],
) {
  if (!listKey || !listName) return;
  if (typeof listName === 'string') {
    listName = listName.split(',');
  }
  if (typeof listKey === 'string') {
    listKey = listKey.split(',');
  }
  const data: any[] = [];
  listKey.forEach((item, index) => {
    data.push({ label: listName[index], value: item });
  });
  return data;
}
