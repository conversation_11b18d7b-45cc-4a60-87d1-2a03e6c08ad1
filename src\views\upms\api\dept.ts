import {
  DeptResultModel,
  DeptDetail,
  EditDetParams,
  DeptListParams,
  DownLoadParmas,
} from './model/deptModel';
// import { sysHttp } from '/@/views/upms/common/http';
import { sysHttp, downloadHttp, uploadHttp } from '/@/views/upms/common/http';
enum Api {
  DeptList = '/dept/tree',
  saveDept = '/dept/',
  GetList = '/dept/list',
  // listall = '/dept/listAll',
  // getByDeptIdUrl = '/dept/findDescendantList',
  getlistUrl = '/dept/list',
  codeUrl = '/dept/address/code',
  getDeptCodeUrl = '/dept/generate/code',
  sortUrl = '/dept/change/sort',
  downloadUrl = '/dept/download',
  uploadUrl = '/dept/upload',
  deptAsyn = '/dept/asyn/dept',
}
// 获取部门列表
export const getDeptList = (params?: DeptListParams) =>
  sysHttp.get<DeptResultModel>(
    { url: Api.DeptList, params: params },
    { joinParamsToUrl: true },
  );

// 修改部门详情、新增部门
export const saveDept = (params: EditDetParams) =>
  sysHttp.post({ url: Api.saveDept, params });
// 获取部门详情
export const getDeptDetail = (id: string) =>
  sysHttp.get<DeptDetail>({ url: `${Api.saveDept}${id}` });
// 删除部门
export const delDep = (id: string) => sysHttp.delete({ url: `${Api.saveDept}${id}` });
// 获取部门列表带分页
export const GetOrgList = (params) => sysHttp.get({ url: Api.GetList, params: params });

// export const ALLOrgList = () => sysHttp.get({ url: Api.listall });
// 根据部门id查询当前部门下所有的机构

export const getDeptListApi = (params) =>
  sysHttp.get({ url: Api.getlistUrl, params: params });
export const getCodeApi = (params) => sysHttp.post({ url: Api.codeUrl, params });

export const getDeptCodeUrlApi = (params) =>
  sysHttp.get({ url: Api.getDeptCodeUrl, params: params });

export const getSortUrlApi = (params) =>
  sysHttp.get({ url: Api.sortUrl, params: params });

export const orgDownLoadApi = (params: DownLoadParmas) =>
  downloadHttp.get({ url: Api.downloadUrl, params });
// downloadUrl

// uploadUrl
export const uploadApi = (params) => {
  return uploadHttp.post({
    url: Api.uploadUrl,
    params,
  });
};
// 获取单节点结构数据
export const deptAsyn = (params) => {
  const { name = '', deptId } = params;
  return sysHttp.get({ url: `${Api.deptAsyn}?deptId=${deptId}&name=${name}` });
};
