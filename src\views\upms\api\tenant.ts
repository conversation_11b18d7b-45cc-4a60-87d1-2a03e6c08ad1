import { sysHttp } from '/@/views/upms/common/http';
import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';
interface SaveTenantModel {
  adminId: string;
  tenantAlias: string;
  tenantName: string;
  id?: string | number;
}

interface AccountListItem {
  available: string;
  availableText: string;
  avatar: string;
  createdBy: string;
  createdDate: string;
  creatorName: string;
  delFlag: string;
  deptId: string;
  deptName: string;
  description: string;
  email: string;
  id: string;
  lastModifiedBy: string;
  lastModifiedDate: string;
  lastModifierName: string;
  nickName: string;
  pageNum: number;
  pageSize: number;
  parentDeptId: string;
  password: string;
  phone: string;
  qqOpenId: string;
  roleIdList: string[];
  roleList: [];
  roleNames: string;
  type: string;
  typeText: string;
  userName: string;
  version: number;
  wxOpenId: string;
}
export type AccountListGetResultModel = BasicFetchResult<AccountListItem> & {
  pageNum: number;
  pageSize: number;
};
type AccountParams = BasicPageParams & {
  deptId?: string;
  userName?: string;
  email?: string;
  phone?: number;
};
export type TenantPageParams = BasicPageParams & {
  tenantName?: string;
  tenantAlias?: string;
  opType?: string;
};
enum Api {
  TenantList = '/tenant/',
  SaveTenant = '/tenant/save',
  DelTenant = '/tenant/del',
  AccountList = '/user/',
  QryTenantList = '/tenant/qryTenant',
  QryProjectList = '/project/qryProject',
  USER_TREE = '/menu/user-tree',
  TenantTree = '/tenant/tenant-tree',
  tenantTreeUrl = '/tenant/qryTenant',
  tenantAccountUrl = '/tenant-user/qryTenantUser',
  inviteAccountUrl = '/tenant-user/editUser',
  tenantAccountDelUrl = '/tenant-user/delUser',
}

// 获取租户列表
export const TenantList = (params: TenantPageParams) =>
  sysHttp.get({ url: Api.TenantList, params });
// 编辑/添加租户
export const SaveTenant = (params?: SaveTenantModel) =>
  sysHttp.post({ url: Api.SaveTenant, params });
// 删除租户成员
export const DelTenant = (params) => sysHttp.delete({ url: Api.DelTenant, params });
// 获取账号列表
export const AccountList = (params: AccountParams) =>
  sysHttp.get<AccountListGetResultModel>({ url: Api.AccountList, params });
// 获取租户字典表
export const QryTenantList = () => sysHttp.get({ url: Api.QryTenantList });
// 获取项目字典表
export const QryProjectList = () => sysHttp.get({ url: Api.QryProjectList });
// 获取租户菜单
// export const getTenantUserTree = (tenantId: string, projectId: string) => {
//   return sysHttp.get(
//     {
//       url: Api.USER_TREE,
//       params: { tenantId: tenantId, projectId: projectId },
//     },
//     { joinParamsToUrl: true },
//   );
// };
// 获取树形租户数据
export const tenantTree = () => sysHttp.get({ url: Api.TenantTree });

export const tenantTreeApi = () => sysHttp.get({ url: Api.tenantTreeUrl });

// 获取账号列表
export const getAccountList = (params) =>
  sysHttp.get<AccountListGetResultModel>({ url: Api.tenantAccountUrl, params });

export const inviteAccountApi = (params) =>
  sysHttp.post({ url: `${Api.inviteAccountUrl}`, params });

export const tenantAccountDelApi = (params) =>
  sysHttp.post({ url: `${Api.tenantAccountDelUrl}`, params });
