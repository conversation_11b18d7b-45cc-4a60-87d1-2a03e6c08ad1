import { TableOptions } from '/@/components/sys/BasicTable/types';
import { FormOptions } from '/@/components/sys/BasicForm/types';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
const microNameReg = /^[a-zA-Z][a-zA-Z0-9_-]+$/;
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
    selectable: function (row) {
      if (row.status == '0') {
        return false;
      } else {
        return true;
      }
    },
  },
  {
    label: '微应用名称',
    prop: 'name',
    width: 180,
    align: 'center',
    sortable: 'custom',
  },
  {
    label: '微应用简称',
    prop: 'microName',
    width: 180,
    align: 'center',
  },
  {
    label: '主应用页面路由',
    prop: 'path',
    width: 200,
    align: 'center',
  },
  {
    label: '微应用地址',
    prop: 'url',
    align: 'center',
  },
  {
    label: '状态',
    prop: 'status',
    width: 120,
    align: 'center',
    slot: 'status',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    width: 200,
  },
];
export const FormSchema: FormOptions[] = [
  {
    field: 'name',
    label: '微应用名称',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入微应用名称',
      disabled: false,
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字', trigger: 'change' }],
  },
  {
    field: 'microName',
    label: '微应用简称',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入微应用简称',
    },
    rules: [
      { pattern: microNameReg, message: '英文开头，包含英文和数字', trigger: 'change' },
      { max: 32, message: '超过长度限制，最多32字', trigger: 'change' },
    ],
  },
  {
    field: 'path',
    label: '主应用页面路由',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入路由',
      prependText: '/micro/',
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字', trigger: 'change' }],
  },
  {
    field: 'url',
    label: '微应用地址',
    required: true,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入微应用地址',
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字', trigger: 'change' }],
  },
];

export const searchFormSchema: SearchOptions[] = [
  {
    field: 'name',
    label: '微应用名称',
    component: 'Input',
    span: 6,
    labelWidth: 50,
    placeholder: '请输入微应用名称',
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'microName',
    label: '微应用简称',
    component: 'Input',
    span: 6,
    labelWidth: 50,
    placeholder: '请输入微应用简称',
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'url',
    label: '微应用地址',
    component: 'Input',
    span: 6,
    labelWidth: 50,
    placeholder: '请输入微应用地址',
    componentProps: {
      clearable: true,
    },
  },
];
