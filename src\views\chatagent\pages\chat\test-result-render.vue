<template>
  <div class="test-container">
    <h2>测试结果渲染组件</h2>
    
    <div class="test-section">
      <h3>1. 表格数据测试 (detailtable.json)</h3>
      <button @click="testTableData" class="test-btn">测试表格渲染</button>
      
      <div v-if="tableResult" class="result-display">
        <div class="result-type">渲染类型: {{ getResultRenderType(tableResult) }}</div>
        
        <div v-if="getResultRenderType(tableResult) === 'table'" class="result-table-container">
          <VTable 
            :data="getTableData(tableResult)" 
            :columns="getTableColumns(tableResult)"
            height="300px"
          />
        </div>
        
        <div v-else class="json-viewer">
          <pre>{{ formatToolJson(tableResult) }}</pre>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>2. 图表数据测试 (detail.json)</h3>
      <button @click="testChartData" class="test-btn">测试图表渲染</button>
      
      <div v-if="chartResult" class="result-display">
        <div class="result-type">渲染类型: {{ getResultRenderType(chartResult) }}</div>
        
        <div v-if="getResultRenderType(chartResult) === 'chart'" class="result-chart-container">
          <div 
            id="test-chart" 
            class="chart-canvas" 
            style="width: 100%; height: 400px;"
          ></div>
        </div>
        
        <div v-else class="json-viewer">
          <pre>{{ formatToolJson(chartResult) }}</pre>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h3>3. SQL数据测试 (detailsql.json)</h3>
      <button @click="testSqlData" class="test-btn">测试SQL渲染</button>
      
      <div v-if="sqlResult" class="result-display">
        <div class="result-type">渲染类型: {{ getResultRenderType(sqlResult) }}</div>
        
        <div v-if="getResultRenderType(sqlResult) === 'sql'" class="result-sql-container">
          <div class="sql-header">
            <Icon icon="carbon:sql" />
            <span>SQL语句</span>
          </div>
          <div class="sql-content">
            <pre class="sql-code">{{ getSqlMessage(sqlResult) }}</pre>
          </div>
        </div>
        
        <div v-else class="json-viewer">
          <pre>{{ formatToolJson(sqlResult) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Icon } from '@iconify/vue'
import * as echarts from 'echarts'
import VTable from '../../components/VTable.vue'

// 测试数据
const tableResult = ref(null)
const chartResult = ref(null)
const sqlResult = ref(null)

// 测试数据加载
const testTableData = async () => {
  try {
    const response = await fetch('/src/views/chatagent/test-data/detailtable.json')
    const data = await response.json()
    tableResult.value = data
    console.log('表格测试数据:', data)
  } catch (error) {
    console.error('加载表格测试数据失败:', error)
  }
}

const testChartData = async () => {
  try {
    const response = await fetch('/src/views/chatagent/test-data/detail.json')
    const data = await response.json()
    chartResult.value = data
    console.log('图表测试数据:', data)
    
    // 渲染图表
    nextTick(() => {
      if (getResultRenderType(data) === 'chart') {
        setTimeout(() => {
          renderToolChart(data, 'test-chart')
        }, 100)
      }
    })
  } catch (error) {
    console.error('加载图表测试数据失败:', error)
  }
}

const testSqlData = async () => {
  try {
    const response = await fetch('/src/views/chatagent/test-data/detailsql.json')
    const data = await response.json()
    sqlResult.value = data
    console.log('SQL测试数据:', data)
  } catch (error) {
    console.error('加载SQL测试数据失败:', error)
  }
}

// 复制主组件中的方法
const getResultRenderType = (result: any) => {
  if (!result || !result.actionResult) return 'json'
  
  const actionResult = result.actionResult
  
  // 检查是否有表格数据 (tableType为"TABLE")
  if (actionResult.retrievedKnowledge?.tableSchemas) {
    const hasTableData = actionResult.retrievedKnowledge.tableSchemas.some(
      (schema: any) => schema.tableType === 'TABLE' && schema.data && schema.data.length > 0
    )
    if (hasTableData) return 'table'
  }
  
  // 检查是否有图表数据 (chartType为"line")
  if (actionResult.chartType === 'line' && actionResult.select_data && actionResult.select_data.length > 0) {
    return 'chart'
  }
  
  // 检查是否有SQL语句 (message字段包含SQL)
  if (actionResult.message && typeof actionResult.message === 'string') {
    const message = actionResult.message.trim().toUpperCase()
    if (message.includes('SELECT') || message.includes('INSERT') || message.includes('UPDATE') || message.includes('DELETE')) {
      return 'sql'
    }
  }
  
  return 'json'
}

const getTableData = (result: any) => {
  if (!result?.actionResult?.retrievedKnowledge?.tableSchemas) return []
  
  const tableSchema = result.actionResult.retrievedKnowledge.tableSchemas.find(
    (schema: any) => schema.tableType === 'TABLE' && schema.data && schema.data.length > 0
  )
  
  return tableSchema?.data || []
}

const getTableColumns = (result: any) => {
  const data = getTableData(result)
  if (!data || data.length === 0) return []
  
  return Object.keys(data[0])
}

const getSqlMessage = (result: any) => {
  return result?.actionResult?.message || ''
}

const formatToolJson = (obj: any) => {
  if (!obj) return '暂无数据'
  try {
    return JSON.stringify(obj, null, 2)
  } catch (e) {
    return String(obj)
  }
}

const renderToolChart = (result: any, containerId: string) => {
  if (!result?.actionResult?.select_data || !result.actionResult.chartType) return
  
  const container = document.getElementById(containerId)
  if (!container) return
  
  const chartData = result.actionResult.select_data
  const chartType = result.actionResult.chartType
  
  const chart = echarts.init(container)
  
  if (chartType === 'line') {
    const dates = chartData.map((item: any) => item.date)
    const salesData = chartData.map((item: any) => item.sales)
    const profitData = chartData.map((item: any) => item.profit)
    
    const option = {
      title: {
        text: '销售数据趋势图',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['销售额', '利润'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '销售额',
          type: 'line',
          data: salesData,
          smooth: true,
          lineStyle: { color: '#5470c6' },
          itemStyle: { color: '#5470c6' }
        },
        {
          name: '利润',
          type: 'line',
          data: profitData,
          smooth: true,
          lineStyle: { color: '#91cc75' },
          itemStyle: { color: '#91cc75' }
        }
      ]
    }
    
    chart.setOption(option)
  }
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 40px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
}

.test-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 16px;
}

.test-btn:hover {
  background: #0056b3;
}

.result-display {
  margin-top: 16px;
}

.result-type {
  font-weight: bold;
  margin-bottom: 12px;
  color: #495057;
}

/* 复制主组件的样式 */
.result-table-container {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
}

.result-chart-container {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  background: #fff;
}

.result-sql-container {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #fff;
  overflow: hidden;
}

.sql-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-weight: 500;
  color: #495057;
}

.sql-content {
  padding: 16px;
}

.sql-code {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 12px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #495057;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  max-height: 300px;
  overflow-y: auto;
}

.json-viewer {
  background: #1f2937;
  border-radius: 8px;
  overflow: hidden;
}

.json-viewer pre {
  color: #f9fafb;
  padding: 16px;
  margin: 0;
  font-size: 0.75rem;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
