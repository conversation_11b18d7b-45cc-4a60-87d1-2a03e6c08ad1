// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace IPostMenuSpace {
  export class RequestParams {
    code: string | undefined;
    name: string | undefined;
    parentId: string | undefined | null;
    show: number | undefined;
    sort: number | undefined;
    val: string | undefined;
    constructor() {
      this.code = '';
      this.name = '';
      this.parentId = '';
      this.show = 0;
      this.sort = 0;
      this.val = '';
    }
  }
}

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace IgetMenuTreeSpace {
  export interface children {
    id: string;
    label: string;
    parentId: string;
    authority: null | string;
    code: null | string | number;
    component: null | string | number;
    icon: string;
    keepAlive: string;
    spread: boolean;
    name: string;
    type: string;
    path: string;
    redirect: string | null;
    sort: number;
  }
  export interface Data {
    children: children[];
    id: string;
    label: string;
    parentId: string;
    authority: null | string;
    code: null | string | number;
    component: null | string | number;
    icon: string;
    keepAlive: string;
    spread: boolean;
    name: string;
    type: string;
    path: string;
    redirect: string | null;
    sort: number;
  }
}
// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace IgetMenuByIdSpace {
  export interface Data {
    component: null | string | number;
    createdBy: string;
    createdDate: string;
    creatorName: string;
    delFlag: string;
    description: string;
    icon: string;
    id: string;
    keepAlive: string;
    lastModifiedBy: string;
    lastModifiedDate: string;
    lastModifierName: string;
    leaf: boolean;
    name: string;
    pageNum: number;
    pageSize: number;
    parentId: string;
    parentIds: null | string;
    parentName: null | string;
    path: string;
    permission: null | string;
    display: number;
    sort: number;
    type: string;
    version: number;
    meta: string;
    microId?: string;
    hideMenu: string;
  }
}
interface meta {
  icon: string;
  title: string;
}
export interface NodeItem {
  children?: NodeItem[];
  component?: string;
  id: string;
  label?: string;
  meta?: meta;
  name?: string;
  parentId?: string;
  path?: string;
  type?: string;
}

export interface MenuListParams {
  tenantId?: string;
  projectId?: string;
  special?: boolean;
  opType?: string;
}

export interface SaveMenuParams {
  tenantId?: string;
  id?: string;
  parentId?: string;
  name: string;
  type: string;
  icon?: string;
  path: string;
  permission: string;
  show: number;
  sort?: number;
  description?: string;
  createdBy?: string;
  createdDate?: string;
  createdName?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  leaf?: string;
  menuType?: string;
  projectId?: string;
  version?: string;
}
export interface deleteMenuType {
  ids: string;
  opType?: string;
  tenantId?: string;
  projectId?: string;
}

export interface UserMenuParams {
  opType?: string;
}

export interface MetaConfigData {
  key: string;
  value: string;
  index: number;
}
// 导出/模板导出请求参数
export interface DownLoadParmas {
  opType?: string;
  isTemplate: boolean;
  targetId?: string;
}
