<template>
  <div class="batch-confirmation">
    <div class="batch-confirmation-pass">
      <el-input
        v-model="password"
        type="password"
        placeholder="请输入密码确认"
        show-password
        @input="getPassword"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { encryptedSM4 } from '/@/utils/cipher';

  let emits = defineEmits(['getPassword']);

  const password = ref('');
  const getPassword = () => {
    console.log(password.value);
    if (!!password.value) {
      emits('getPassword', encryptedSM4(password.value));
    } else {
      emits('getPassword', '');
    }
  };
</script>

<style scoped lang="scss">
  .el-icon {
    font-size: 24px;
    color: red;
    margin-right: 8px;
    // padding-top: 8px;
  }
  .batch-confirmation-tip {
    display: flex;
  }
  .batch-confirmation-pass {
    // margin-top: 24px;
    p {
      margin-bottom: 12px;
      font-size: 14px;
    }
  }
</style>
