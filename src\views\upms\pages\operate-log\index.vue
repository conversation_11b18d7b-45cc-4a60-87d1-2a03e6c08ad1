<template>
  <div class="content">
    <div class="box">
      <div class="search-div">
        <p class="title"> 操作日志搜索 </p>
        <basic-search
          :searchArray="searchArray"
          :labelShow="false"
          class="search-all"
          ref="searchRef"
          @onSearch="logList"
          @reset="logList"
        />
      </div>
      <div class="table-content">
        <div class="table-top-div">
          <p>操作日志列表</p>
          <div class="top-button">
            <el-button type="primary" @click="downLoad" :icon="Download" plain
              >导出</el-button
            >
          </div>
        </div>
        <basic-table
          :columns="columns"
          :data="tableData"
          :total="page.total"
          :page-size="page.pageSize"
          :current-page="page.pageNum"
          @page-change="pageChange"
          @size-change="sizeChange"
          :downSetting="true"
          height="calc(100vh - 392px)"
        >
          <template #operationType="{ record }">
            <el-tag :type="tagType(record.operationType)">{{
              record.operationType
            }}</el-tag>
          </template>
          <template #requestType="{ record }">
            <el-tag v-if="!!record.requestType" :type="tagType(record.requestType)">{{
              record.requestType
            }}</el-tag>
          </template>
          <!--  -->
          <template #action="scope">
            <el-button type="primary" link @click="viewClick(scope.record)"
              >查看</el-button
            >
          </template>
        </basic-table>
      </div>
    </div>
    <el-dialog v-model="visible" title="日志详情" width="1000">
      <view-log class="view-log" :record="record" />
      <template #footer>
        <el-button @click="visible = false">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup name="OperateLog">
  import { ref, watch, computed } from 'vue';
  import { View, Download } from '@element-plus/icons-vue';
  import dayjs from 'dayjs';
  import { searchArray, columns } from './log.data';
  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  import ViewLog from './ViewLog.vue';
  import {
    LogListResult,
    LogListParams,
    HeaderForm,
  } from '/@/views/upms/api/model/LogModel';
  import { getLogList, getLogDict, downLoadApi } from '../../api/log';
  import { useTenantStore } from '/@/stores/modules/tenant';
  defineExpose({ View, columns });
  let tableData = ref([]);
  // 分页信息
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const searchRef = ref({
    userName: '',
    operateType: '',
    time: '',
  });
  const searchHeaderForm = ref<HeaderForm>({
    tenantId: '',
    projectId: '',
  });
  const record = ref({
    id: 0,
    title: '',
    operateType: '',
    operatorType: '',
    username: '',
    deptName: '',
    ipAddress: '',
    createdDate: '',
    browser: '',
    os: '',
    method: '',
    requestUri: '',
    params: '',
    userAgent: '',
    time: 0,
  });
  const visible = ref(false);
  interface sysLogOperateTypeItem {
    value: string;
    label: string;
  }
  const deptId = ref('');
  interface tableItem {
    id: number;
    title: string;
    operateType: string;
    operatorType: string;
    username: string;
    deptName: string;
    ipAddress: string;
    createdDate: string;
    browser: string;
    os: string;
    method: string;
    requestUri: string;
    params: string;
    userAgent: string;
    time: number;
  }
  const useTenant = useTenantStore();

  const tagType = computed(() => (item: any) => {
    //计算属性传递参数
    if (item === 'GET') {
      return 'success';
    }
    if (item === 'UPDATE') {
      return '';
    }
    if (item === 'PUT') {
      return '';
    }
    if (item === 'POST') {
      return 'warning';
    }
    if (item === 'DELETE') {
      return 'danger';
    }
    if (item === 'INSERT') {
      return 'warning';
    }
  });
  watch(
    () => useTenant.getCurTenant,
    (value) => {
      searchHeaderForm.value.tenantId = value.id || '';
    },
    {
      immediate: true,
      deep: true,
    },
  );
  watch(
    () => useTenant.getCurProject,
    (value) => {
      searchHeaderForm.value.projectId = value.id || '';
      if (value.id) {
        logList();
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
  /**
   * 日志列表
   */
  function logList() {
    const { pageNum, pageSize } = page.value;
    const { userName, time, operateType } = searchRef?.value?.['searchValue'] || {};
    const data: LogListParams = {
      pageNum,
      pageSize,
      cascade: '',
      deptId: deptId.value,
      userName,
      operateType,
    };
    if (time) {
      data.startTime = dayjs(time[0]).format('YYYY-MM-DD') + ' ' + '00:00:00';
      data.endTime = dayjs(time[1]).format('YYYY-MM-DD') + ' ' + '23:59:59';
    }
    console.log();
    getLogList(data).then((res: LogListResult) => {
      const { list, total } = res;
      tableData.value = list;
      page.value = {
        total,
        pageNum,
        pageSize,
      };
    });
  }

  /**
   * 字典数据
   */
  const logDictData = () => {
    getLogDict({}).then((res: Array<string>): void => {
      const arr: Array<sysLogOperateTypeItem> = [];
      res.forEach((item) => {
        arr.push({ label: item, value: item });
      });
      //搜索条件
      searchArray.map((item) => {
        if (item.field == 'operateType') {
          item.componentProps.options = arr;
        }
        return item;
      });
    });
  };

  /**
   * 切换分页，每页显示数量
   */
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    logList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    logList();
  };

  /**
   * 列表操作类型
   */
  const viewClick = (row: tableItem) => {
    console.log('record', record);
    visible.value = true;
    record.value = row;
  };
  const init = () => {
    logDictData();
    logList();
  };
  init();

  const downLoad = () => {
    const { userName, time, operateType } = searchRef?.value?.['searchValue'] || {};
    const { pageNum, pageSize } = page.value;
    const params = {
      pageNum,
      pageSize,
      userName,
      operateType,
      startTime:
        time && time.length > 0
          ? dayjs(time[0]).format('YYYY-MM-DD') + ' ' + '00:00:00'
          : '',
      endTime:
        time && time.length > 0
          ? dayjs(time[1]).format('YYYY-MM-DD') + ' ' + '23:59:59'
          : '',
    };
    downLoadApi(params).then((res) => {
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
      });
      const fileName = '操作日志' + '.xlsx';
      const elink = document.createElement('a'); // 创建a标签
      elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click(); // 点击下载
      console.log(elink.href);
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink); // 释放标签
    });
  };
</script>
<route lang="yaml">
meta:
  auth: true
</route>
//
<style scoped lang="scss">
  // .dept-all {
  //   display: flex;
  //   height: calc(100vh - 81px);
  //   background: #f0f0f0;
  // }

  // .dept-left {
  //   width: 280px;
  //   margin: 5px;
  //   background: white;
  // }

  // .log-right {
  //   flex: 1;
  //   margin: 6px 10px;
  // }

  // .el-card :deep(.el-card__header) {
  //   padding: 5px 10px;
  //   text-indent: 12px;
  // }

  // .el-card :deep(.el-card__body) {
  //   padding: 5px 10px;
  // }

  // .text-xs {
  //   font-weight: 700;
  //   font-size: 16px;
  //   line-height: 40px;
  //   text-indent: 24px;
  // }
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    // display: flex;

    .mid {
      height: 100%;
      // box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }
  .search-div {
    border-radius: 6px;
    background: #fff;
    // padding: 10px 16px;
    .title {
      // border-bottom: 1px solid #e4e7ed;
      // line-height: 68px;

      font-size: 18px;
      padding: 20px 0 6px 20px;
      // background: #ffffff;
      font-weight: 700;
      color: #333333;
    }
  }
  .table-top-div {
    display: flex;
    justify-content: space-between;
    // line-height: 64px;
    margin-bottom: 20px;
    p {
      font-size: 18px;
      color: #333333;
      font-weight: 700;
      margin: 0;
      padding: 0;
      line-height: 32px;
    }
  }
  .table-content {
    margin-top: 16px;
    background: #ffffff;

    padding: 20px 20px 0 20px;
    // position: relative;
    overflow: auto;
    border-radius: 6px;
  }

  .view-log {
    overflow: auto;
  }

  .top-button {
    // position: absolute;
    // top: 10px;
    z-index: 9;
  }
</style>
