const phoneReg =
  /^((\+86|0086)?\s*)((134[0-8]\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\d{8})|(14(0|1|4)0\d{7})|(1740([0-5]|[6-9]|[10-12])\d{7}))$/;
const card =
  /^[1-9][0-9]{5}(19|20)[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}([0-9]|x|X)$/;
import { TableOptions } from '/@/components/sys/BasicTable/types';

const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
import { getDictStorage } from '/@/utils/storage/dict';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
import { FormOptions } from '/@/components/sys/BasicForm/types';
import { checkApi } from '../../api/user';
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
  },
  {
    label: '员工工号',
    prop: 'empCode',
    slot: 'empCode',
    width: 160,
    align: 'center',
    sortable: 'custom',
  },
  {
    label: '姓名',
    prop: 'empName',
    width: 100,
    align: 'center',
  },
  {
    label: '员工类型',
    prop: 'empType',
    width: 150,
    align: 'center',
  },
  {
    label: '员工状态',
    prop: 'status',
    width: 100,
    align: 'center',
  },

  {
    label: '所属板块',
    width: 120,
    prop: 'industryType',
    align: 'center',
    sortable: 'custom',
    slot: 'industryType',
  },
  {
    label: '性别',
    prop: 'gender',
    width: 80,
    align: 'center',
  },
  {
    label: '机构',
    prop: 'instName',
    minWidth: 200,
    align: 'center',
  },
  {
    label: '身份证号',
    prop: 'idCard',
    minWidth: 200,
    align: 'center',
  },

  {
    label: '邮箱',
    prop: 'email',
    minWidth: 200,
    align: 'center',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    width: 160,
    align: 'center',
  },
];

export const searchFormSchema: SearchOptions[] = [
  {
    field: 'empCode',
    label: '员工工号：',
    component: 'Input',
    placeholder: '请输入员工工号',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'empName',
    label: '员工姓名',
    component: 'Input',
    placeholder: '请输入员工姓名',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'industryType',
    label: '所属版块：',
    component: 'Select',
    placeholder: '请选择所属版块',
    componentProps: {
      clearable: true,
      options: getDictStorage()?.dic_industry_type
        ? getDictStorage().dic_industry_type
        : [],
    },
    span: 8,
  },

  {
    field: 'status',
    label: '员工状态',
    component: 'Select',
    placeholder: '请选择员工状态',
    span: 8,
    componentProps: {
      clearable: true,
      options: getDictStorage()?.biz_status ? getDictStorage().biz_status : [],
    },
  },
  {
    field: 'phone',
    label: '手机号',
    component: 'Input',
    placeholder: '请输入手机号',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'email',
    label: '电子邮箱',
    component: 'Input',
    placeholder: '请输入电子邮箱',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'sub',
    label: '查看本机构及下属机构员工',
    componentProps: {
      defaultValue: '1',
    },
    span: 8,
    labelWidth: 50,
    slot: 'sub',
  },
];

export const addAccountFormSchema: FormOptions[] = [
  // Text
  {
    field: 'deptName',
    label: '机构名称',
    component: 'Text',
  },
  {
    field: 'empName',
    label: '员工姓名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入姓名',
      disabled: false,
    },
    rules: [
      { max: 15, message: '超过长度限制，最多15字' },
      { min: 2, message: '少于长度限制，最少2字' },
    ],
    required: true,
  },

  {
    label: '手机号',
    field: 'phone',
    component: 'Input',
    componentProps: {
      placeholder: '请输入手机号',
    },
    rules: [
      {
        pattern: phoneReg,
        message: '请输入正确的手机号',
      },
      {
        validator: (rule, value) => {
          if (!value) {
            /* eslint-disable-next-line */
            return true;
          }
          return new Promise<void>((resolve, reject) => {
            const params = {
              type: 2,
              value: value,
            };
            checkApi(params).then((res) => {
              res ? resolve() : reject(res.message || '手机号码已存在');
            });
          });
        },
      },
    ],
    // required: true,
  },
  {
    label: '电子邮箱',
    field: 'email',
    component: 'Slot',
    slot: 'autocomplete',
    rules: [
      {
        trigger: 'blur',
        pattern: emailReg,
        message: '请输入正确的电子邮箱',
      },
      { max: 100, message: '超过长度限制，最多100字' },
      { min: 3, message: '少于长度限制，最少3字' },
      {
        validator: (rule, value) => {
          if (!value) {
            /* eslint-disable-next-line */
            return true;
          }
          return new Promise<void>((resolve, reject) => {
            const params = {
              type: 0,
              value: value,
            };
            checkApi(params).then((res) => {
              res ? resolve() : reject(res.message || '电子邮箱已存在');
            });
          });
        },
      },
    ],
    required: true,
  },
  {
    label: '所属板块',
    field: 'industryType',
    component: 'RadioGroup',
    componentProps: {
      options: getDictStorage()?.dic_industry_type
        ? getDictStorage().dic_industry_type
        : [],
    },
    // required: true,
  },
  {
    label: '性别',
    field: 'gender',
    component: 'RadioGroup',
    componentProps: {
      options: [
        {
          label: '男',
          value: 'M',
        },
        {
          label: '女',
          value: 'F',
        },
      ],
    },
    // required: true,
  },
  {
    label: '员工类型',
    field: 'empType',
    component: 'Select',
    componentProps: {
      options: getDictStorage()?.biz_emp_type ? getDictStorage().biz_emp_type : [],
      clearable: true,
    },
    required: false,
  },
  {
    label: '员工状态',
    field: 'status',
    component: 'RadioGroup',
    componentProps: {
      options: getDictStorage()?.biz_status ? getDictStorage().biz_status : [],
    },
    required: true,
  },
  {
    label: '环节',
    field: 'nodeCode',
    component: 'Select',
    componentProps: {
      options: getDictStorage()?.biz_node_code ? getDictStorage().biz_node_code : [],
      clearable: true,
    },
    required: false,
  },
  {
    label: '专业',
    field: 'serviceCode',
    component: 'Select',
    componentProps: {
      clearable: true,
      options: getDictStorage()?.biz_service_code
        ? getDictStorage().biz_service_code
        : [],
    },
    required: false,
  },
  {
    label: '员工职位',
    field: 'empPost',
    component: 'Select',
    componentProps: {
      clearable: true,
      options: getDictStorage()?.biz_emp_post ? getDictStorage().biz_emp_post : [],
    },
    required: false,
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
    // required: true,
    rules: [
      { pattern: card, message: '请输入正确的身份证号' },
      {
        validator: (rule, value) => {
          if (!value) {
            /* eslint-disable-next-line */
            return true;
          }
          return new Promise<void>((resolve, reject) => {
            const params = {
              type: 1,
              value: value,
            };
            checkApi(params).then((res) => {
              res ? resolve() : reject(res.message || '身份证号码已存在');
            });
          });
        },
      },
    ],
  },
];
