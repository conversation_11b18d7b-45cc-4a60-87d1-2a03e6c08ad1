import { ref, nextTick } from 'vue';

export default function useAccount(formData, accountOptions, accountListRef) {
  const visibleAccount = ref(false);
  const selectAccountData = ref<any>([]);

  const handleFocusAccountEvent = (e) => {
    visibleAccount.value = true;
    nextTick(() => {
      // accountSelect.value && accountSelect.value?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      if (selectAccountData.value.length > 0) {
        if (formData.value.userId && formData.value.userId.length > 0) {
          accountListRef.value &&
            accountListRef.value?.handleSetSelectData(selectAccountData.value);
        } else {
          selectAccountData.value = [];
          accountListRef.value && accountListRef.value?.handleSetSelectData(null);
        }
      }
    });
  };
  const handleChangeAccount = (data) => {
    if (data.length > 0) {
      accountListRef.value &&
        accountListRef.value?.handleSetSelectData(selectAccountData.value);
    }
  };

  const selectAccounts = (data) => {
    selectAccountData.value = data;
  };
  const saveAccount = () => {
    accountOptions.value = [];
    formData.value.userId = [];
    for (let i = 0; i < selectAccountData.value.length; i++) {
      const obj = {
        label: selectAccountData.value[i].userName,
        value: selectAccountData.value[i].id,
      };
      formData.value.userId.push(selectAccountData.value[i].id);
      accountOptions.value.push(obj);
    }

    visibleAccount.value = false;
  };
  const removeTagAccount = (val) => {
    selectAccountData.value = removeById(selectAccountData.value, val);
  };
  const removeById = (arr, id) => {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].id === id) {
        arr.splice(i, 1);
        break;
      }
    }
    return arr;
  };
  return {
    handleFocusAccountEvent,
    handleChangeAccount,
    selectAccounts,
    saveAccount,
    visibleAccount,
    selectAccountData,
    removeTagAccount,
  };
}
