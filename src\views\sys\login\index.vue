<template>
  <div>
    <div class="login-container">
      <p class="sys-logo"><img :src="logo" alt="logo" /></p>
      <div class="page-center">
        <div class="page-left"
          ><!--<img :src="logoLeft" alt="登录插图" />
          <span class="img-title">{{ VITE_APP_TITLE }}</span>-->
          <div class="text">
            <p class="welcome">欢迎使用{{ VITE_APP_TITLE }}</p>
            <p class="descript">实现{{ VITE_APP_TITLE }}</p>
          </div>
        </div>
        <div class="login-box">
          <div class="login-form">
            <!--          <div class="sys-title">欢迎回来</div>{{ VITE_APP_TITLE }}-->
            <p class="sys-title" v-motion :initial="initial()" :enter="enter(100)"> </p>
            <el-tabs
              v-model="loginType"
              :class="loginTypes.length <= 1 ? 'login-type-tabs' : ''"
              stretch
            >
              <el-tab-pane
                v-for="login in loginTypes"
                :label="loginTypeEnum[login].label"
                :name="login"
                :key="login"
              >
                <component
                  :is="loginTypeEnum[login].components"
                  :currentType="login"
                  :loginType="loginType"
                />
              </el-tab-pane>
            </el-tabs>
            <div id="slider-valid"></div>
          </div>
        </div>
      </div>
      <!-- <p class="fix-foot">Copyright © 2022-2033 中国邮政版权所有</p> -->
    </div>
  </div>
</template>

<script setup lang="ts" name="Login">
  // import { warpperEnv } from '@build/index';
  import { onMounted, ref } from 'vue';
  import logo from '/@/assets/images/login/logo-upms.svg';
  import { useAppStoreWithOut } from '/@/stores/modules/app';
  import { useAnimateData, QrCode, USER, MSG } from '/@/views/shares/components/login';
  import { markRaw } from 'vue';
  import { LoginEnum } from '/@/enums/appEnum';
  import { pageLogin } from '/@/hooks/web/usePageLogin';
  import { initSettingsStore } from '/@/settings/initSettings';
  import { warpperEnv } from '@build/index';
  const { VITE_APP_TITLE } = warpperEnv();
  onMounted(() => {
    initSettingsStore();
  });
  // import { getFID } from 'web-vitals';
  // getFID(console.log);

  const loginTypeEnum = {
    [LoginEnum.USER]: { components: markRaw(USER), label: '账号密码登录' },
    [LoginEnum.QrCode]: { components: markRaw(QrCode), label: '扫码登录' },
    [LoginEnum.MSG]: { components: markRaw(MSG), label: '手机号登录' },
  };

  const { initial, enter } = useAnimateData();

  const appStore = useAppStoreWithOut();
  // 使用统一认证的方法登录
  pageLogin(appStore);
  // 扫码登录
  const loginType = ref(appStore.projectConfig?.loginTypes[0]);
  const loginTypes = ref(appStore.projectConfig?.loginTypes);
  console.log(loginTypes.value);
  // const { VITE_APP_TITLE } = warpperEnv();

  defineExpose({ initial });
</script>

<style scoped lang="scss">
  @import url('/src/styles/login/login.css');
  .login-container {
    background: url('/src/assets/images/login/logo-left.png') center center / cover;
  }
  .page-center {
    width: 1180px;
    height: calc(100vh - 110px);
    display: flex;
    /* margin: calc((100vh - 520px) / 2 - 50px) auto calc((100vh - 520px) / 2); */
    margin: 10px auto 55px;
    // background-color: #ffffff;
    border-radius: 16px;
    .page-left {
      width: 720px;
      // height: 100%;
      height: calc(100vh - 840px);
      // background-color: #f6fffe;
      border-radius: 16px;
      // position: relative;
      // img {
      //   display: inline-block;
      //   width: 100%;
      //   height: 100%;
      // }
      // .img-title {
      //   display: block;
      //   width: 30px;
      //   position: absolute;
      //   top: 50%;
      //   margin-top: -126px;
      //   font-size: 28px;
      //   left: 50%;
      //   margin-left: -30px;
      //   writing-mode: vertical-rl; /* 从右到左 */
      //   text-orientation: upright; /* 保持正常显示 */
      //   // transform: ;
      // display: flex;
      // justify-content: center;
      /* align-items: center; */
      /* margin-top: 10%; */
      // margin-top: 10%;
      // }
      .text {
        width: 365px;
        margin: 100px 0 0 150px;
        // position: fixed;
        // top: 10rem;
        // left: 20rem;
        // margin-top: 13%;
        // margin-left: 5%;
      }
      .welcome {
        font-family: PingFangSC-Semibold;
        font-size: 32px;
        color: #006cff;
        letter-spacing: 0;
        line-height: 40px;
        font-weight: 600;
      }
      .descript {
        margin-top: 10px;
        font-family: PingFangSC-Regular;
        font-size: 14px;
        color: #333333;
        letter-spacing: 0;
        line-height: 20px;
        font-weight: 400;
      }
    }
    .login-form {
      width: 360px;
      height: 420px;
      background: #fff;
      padding: 50px 36px 80px 36px;
    }
  }
  .fix-foot {
    width: 100%;
    text-align: center;
    position: fixed;
    bottom: 20px;
    font-size: 12px;
    color: #000;
    letter-spacing: 1.66px;
    line-height: 12px;
    font-weight: 400;
  }

  .loign-input {
    height: 40px;
  }
  .login-type-tabs :deep(.el-tabs__item) {
    color: black;
  }
</style>
<route lang="yaml">
name: Login
meta:
  layout: BlankLayout
</route>
