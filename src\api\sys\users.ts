import { defHttp } from '/@/utils/axios';

// 用户管理相关接口
enum Api {
  UserPageList = '/system/sysUsers/pageByParams',
  UserList = '/system/sysUsers/list',
  UserDetail = '/system/sysUsers/get/{id}',
  UserSave = '/system/sysUsers/save',
  UserUpdate = '/system/sysUsers/updateById',
  UserDelete = '/system/sysUsers/delete/{ids}',
}

// 用户查询参数
export interface UserQueryParams {
  username?: string;
  name?: string;
  phone?: string;
  email?: string;
  sex?: string;
  enabled?: string;
  currentPage?: number;
  pageSize?: number;
}

// 用户信息
export interface UserInfo {
  id?: string;
  username: string;
  name: string;
  phone?: string;
  email?: string;
  sex?: string;
  enabled?: string;
  loginIp?: string;
  loginTime?: string;
  createTime?: string;
  updateTime?: string;
}

// 用户分页结果
export interface UserPageResult {
  currentPage: number;
  pageSize: number;
  total: number;
  data: UserInfo[];
}

// 角色信息
export interface RoleInfo {
  id: string;
  name: string;
  code?: string;
  description?: string;
}

// 用户角色授权参数
export interface UserAuthParams {
  userId: string;
  roleIds: string[];
}

/**
 * 分页查询用户列表
 */
export const getUserPageList = (params: UserQueryParams) =>
  defHttp.post<UserPageResult>({
    url: `${Api.UserPageList}?currentPage=${params.currentPage || 1}&pageSize=${params.pageSize || 10}`,
    params: {
      username: params.username,
      name: params.name,
      phone: params.phone,
      email: params.email,
      sex: params.sex,
      enabled: params.enabled,
    },
  });

/**
 * 查询所有用户列表
 */
export const getAllUsers = (params?: any) =>
  defHttp.post<UserInfo[]>({ url: Api.UserList, params });

/**
 * 根据ID查询用户详情
 */
export const getUserById = (id: string) =>
  defHttp.get<UserInfo>({ url: Api.UserDetail.replace('{id}', id) });

/**
 * 新增用户
 */
export const saveUser = (params: UserInfo) =>
  defHttp.post({ url: Api.UserSave, params });

/**
 * 更新用户
 */
export const updateUser = (params: UserInfo) =>
  defHttp.post({ url: Api.UserUpdate, params });

/**
 * 删除用户
 */
export const deleteUsers = (ids: string) =>
  defHttp.get({ url: Api.UserDelete.replace('{ids}', ids) });
