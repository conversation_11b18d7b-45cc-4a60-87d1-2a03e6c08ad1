<template>
  <el-select
    class="user-list"
    filterable
    remote
    reserve-keyword
    remote-show-suffix
    :remote-method="deptRemoteMethod"
    v-loading="deptloading"
    v-model="selectValue"
    v-bind="$attrs"
    :multiple="multiple"
    @change="change"
    value-key="value"
    :multiple-limit="multipleLimit"
    @blur="eventBlue"
  >
    <el-option
      v-for="item in selectLoadData"
      :key="item[optionValue]"
      :label="item.label"
      :value="item"
    />
    <el-button
      v-if="selectLoadData.length < deptPage.total"
      class="user-button"
      @click.stop="deptAddMore"
      >加载更多</el-button
    >
  </el-select>
</template>
<script lang="ts" setup>
  import { ref, onMounted, watch, PropType } from 'vue';
  const emits = defineEmits(['update:modelValue', 'change']);
  const selectValue = ref([]);
  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => [],
    },
    multipleLimit: {
      type: Number,
      default: 30,
    },
    multiple: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    getList: {
      type: Function,
      default: function () {
        return Promise.resolve();
      },
    },
    params: {
      type: Object,
      default: () => {},
    },
    watchParams: {
      type: Boolean,
      default: true,
    },
    searchKey: {
      type: String,
      default: 'instName',
    },
    optionLabel: {
      type: String,
      default: 'instName',
    },
    optionValue: {
      type: String,
      default: 'id',
    },
    idlist: {
      type: Array,
      default: () => [],
    },
    historyData: {
      type: Object,
      default: () => {},
    },
  });
  onMounted(() => {
    selectValue.value = props.modelValue;
  });
  // 机构分页信息
  const deptPage = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const selectLoadData = ref([]);
  const deptloading = ref(false);
  const deptSearchName = ref('');
  const deptRemoteMethod = (query: string) => {
    deptPage.value = {
      total: 0,
      pageSize: 10,
      pageNum: 1,
    };
    if (query) {
      deptloading.value = true;
      deptSearchName.value = query;
      DeptList(query, false);
    } else {
      // userData.value = [];
      deptSearchName.value = '';
      DeptList(query, false);
    }
  };

  /**
   * 机构列表
   */
  const DeptList = (instName = '', isAdd = false) => {
    const { pageNum, pageSize } = deptPage.value;
    props
      .getList({
        opType: 'system',
        [props.searchKey]: instName,
        ...props.params,
        pageNum,
        pageSize,
      })
      .then((res) => {
        deptloading.value = false;
        let { list, total } = res;
        deptPage.value.total = total;
        list = list.map((item) => {
          return {
            value: item[props.optionValue],
            label: item[props.optionLabel],
          };
        });
        isAdd ? selectLoadData.value.push(...list) : (selectLoadData.value = list);
      });
  };
  function deptAddMore() {
    deptPage.value.pageNum++;
    DeptList(deptSearchName.value, true);
  }
  function getDeptName(deptIds: string[]) {
    const nameList = [];
    deptIds.forEach((item) => {
      selectLoadData.value.forEach((i) => {
        if (item === i[props.optionValue]) {
          nameList.push(i[props.optionLabel]);
        }
      });
    });
    return nameList.length ? nameList.join('、') : '';
  }
  DeptList('', false);
  watch(
    () => props.params,
    () => {
      props.watchParams && DeptList('', false);
    },
    {
      immediate: false,
      deep: true,
    },
  );
  function change(val) {
    emits(
      'update:modelValue',
      val,
      // val.map(
      //   (item) =>
      //     item[props.optionLabel]
      // ),
    );
    emits(
      'change',
      val.map((item) => item.value),
    );
  }
  function eventBlue() {
    // console.log('失去了焦点', selectValue.value, deptSearchName.value);
    if (!selectLoadData.value.length) {
      deptSearchName.value = '';
      deptPage.value = {
        total: 0,
        pageSize: 10,
        pageNum: 1,
      };
      DeptList('', false);
    }
  }
  defineExpose({ getDeptName, selectLoadData, DeptList });
</script>
<route lang="yaml">
meta:
  auth: true
</route>
//
<style scoped>
  .dept-all {
    display: flex;
    height: 100%;
    background: #f0f0f0;
  }

  .dept-left {
    width: 280px;
    margin: 5px;
    background: white;
  }

  .dept-right {
    flex: 1;
    margin: 5px;
    background: white;
  }

  .search-all {
    margin: 5px;
  }

  .add-account {
    min-width: 600px;
    max-height: calc(100vh - 250px);
    overflow-y: auto;
  }

  .el-card :deep(.el-card__header) {
    padding: 5px 10px;
  }

  .el-card :deep(.el-card__body) {
    padding: 5px 10px;
  }

  :deep(.account-dialog) {
    max-height: calc(100vh - 80px);
    margin-top: 30px;
  }

  .user-list {
    width: 100%;
  }

  .user-button {
    margin: 5px 0;
    margin-left: calc(50% - 45px);
  }
</style>
