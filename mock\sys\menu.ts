/*
 * @Author: wangneng <EMAIL>
 * @Date: 2022-08-01 14:30:52
 * @LastEditors: wuxiaofan
 * @LastEditTime: 2022-09-16 14:11:20
 * @FilePath: \cpvf-pools\mock\sys\menu.ts
 * @Description: 模拟后端菜单的mock数据
 */
import { MockMethod } from 'vite-plugin-mock';
export enum Api {
  GetMenuList = '/sys/menu/user-menu',
}
export default [
  {
    url: Api.GetMenuList,
    method: 'get',
    response: () => {
      return {
        message: '成功',
        code: 1,
        result: [
          {
            id: '1565627326099849218',
            parentId: '0',
            children: [],
            label: '工作台',
            status: null,
            name: '工作台',
            path: '/dashboard',
            component: null,
            type: '0',
            sort: 0,
            meta: { icon: '', title: '工作台' },
          },
          {
            id: '1565627326099849215',
            parentId: '0',
            children: [],
            label: 'AI助手',
            status: null,
            name: 'AI助手',
            path: '/chatagent/chat',
            component: null,
            type: '0',
            sort: 0,
            meta: { icon: '', title: 'AI助手' },
          },
          // {
          //   id: '1565627326099849218',
          //   parentId: '0',
          //   children: [],
          //   label: 'postList',
          //   status: null,
          //   name: 'postList',
          //   path: '/upms/postList',
          //   component: null,
          //   type: '0',
          //   sort: 0,
          //   meta: { icon: '', title: 'postList' },
          // },
          {
            id: '1565627326099849218',
            parentId: '0',
            children: [],
            label: 'setting',
            status: null,
            name: 'setting',
            path: '/agent/setting',
            component: null,
            type: '0',
            sort: 0,
            meta: { icon: '', title: 'setting' },
          },
          {
            id: '1565627326099849218',
            parentId: '0',
            children: [],
            label: 'arrange',
            status: null,
            name: 'arrange',
            path: '/agent/arrange',
            component: null,
            type: '0',
            sort: 0,
            meta: { icon: '', title: 'arrange' },
          },

          {
            id: '3',
            parentId: '0',
            children: [
              {
                id: '5',
                parentId: '3',
                children: [],
                label: '用户管理',
                status: null,
                name: '用户管理',
                path: '/system/user',
                component: null,
                type: '0',
                sort: 2,
                meta: { icon: '', title: '用户管理' },
              },

              {
                id: '6',
                parentId: '3',
                children: [],
                label: '角色管理',
                status: null,
                name: '角色管理',
                path: '/system/role',
                component: null,
                type: '0',
                sort: 3,
                meta: { icon: '', title: '角色管理' },
              },
            ],
            label: '系统管理',
            status: null,
            name: '系统管理',
            path: '/system',
            component: null,
            type: '0',
            sort: 3,
            meta: { icon: '', title: '系统管理' },
          },

          // {
          //   id: '1565591616244383746',
          //   parentId: '0',
          //   children: [],
          //   label: '敏捷应用',
          //   status: null,
          //   name: '敏捷应用',
          //   path: '/micro/vite',
          //   component: null,
          //   type: '2',
          //   sort: 0,
          //   meta: {
          //     hideMenu: '0',
          //     icon: '',
          //     microType: 'vite',
          //     title: '敏捷应用',
          //     url: 'http://localhost:8085/child/vite/',
          //     microPath: '/upms/role',
          //     microName: 'upms',
          //   },
          // },
          // {
          //   id: '1560521273460367362',
          //   parentId: '0',
          //   children: [
          //     // {
          //     //   id: '15619106386741493712',
          //     //   parentId: '1560521273460367362',
          //     //   children: [],
          //     //   label: 'vben',
          //     //   status: null,
          //     //   name: 'vben',
          //     //   path: '/micro/vite',
          //     //   component: null,
          //     //   type: '2',
          //     //   sort: 1,
          //     //   meta: {
          //     //     hideMenu: '0',
          //     //     icon: '',
          //     //     microType: 'vite',
          //     //     title: 'vben',
          //     //     url: 'http://localhost:4173',
          //     //     microPath: '/micro/vite',
          //     //     microName: 'vben',
          //     //   },
          //     // },
          //     {
          //       id: '15619106386741493714',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'bpm打包',
          //       status: null,
          //       name: 'bpm打包',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 1,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'bpm打包',
          //         url: 'http://localhost:80/child/bpm/#/camunda/list',
          //         microPath: '/micro/vite',
          //         microName: 'bpmbuild',
          //       },
          //     },
          //     {
          //       id: '15619106386741493711',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'bpm本地',
          //       status: null,
          //       name: 'bpm本地',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 1,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'bpm本地',
          //         url: 'http://localhost:3100',
          //         microPath: '/micro/vite',
          //         microName: 'bpm',
          //       },
          //     },
          //     {
          //       id: '15619106386741493710',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'vben本地',
          //       status: null,
          //       name: 'vben本地',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 1,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'vben本地',
          //         url: 'http://localhost:4173',
          //         microPath: '/micro/vite',
          //         microName: 'vben',
          //       },
          //     },
          //     {
          //       id: '1561910638674149378',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'vite80菜单',
          //       status: null,
          //       name: 'vite80菜单',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 1,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'vite80菜单',
          //         url: 'https://localhost:80/child/vite',
          //         microPath: '/upms/menu',
          //         microName: 'upms',
          //       },
          //     },
          //     {
          //       id: '1567027487732629506',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'vite80用户',
          //       status: null,
          //       name: 'vite80用户',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 1,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'vite80用户',
          //         url: 'http://localhost:80/child/vite',
          //         microPath: '/upms/account',
          //         microName: 'upms',
          //       },
          //     },
          //     {
          //       id: '1562987462640500737',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'teamwork',
          //       status: null,
          //       name: 'teamwork',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 2,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'teamwork',
          //         url: 'http://localhost:8881/#/project/list/my',
          //         microPath: '/project/list',
          //         microName: 'teamwork',
          //       },
          //     },
          //     {
          //       id: '1567033490821840898',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'vite802字典',
          //       status: null,
          //       name: 'vite802字典',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 2,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'vite802字典',
          //         url: 'http://************:80/child/vite2',
          //         microPath: '/upms/dict',
          //         microName: 'upms5',
          //       },
          //     },
          //     {
          //       id: '1563044314266959873',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'vite菜单',
          //       status: null,
          //       name: 'vite菜单',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 3,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'vite菜单',
          //         url: 'http://localhost:4173/',
          //         microPath: '/upms/menu',
          //         microName: 'upms',
          //       },
          //     },
          //     {
          //       id: '1563086763517509634',
          //       parentId: '1560521273460367362',
          //       children: [],
          //       label: 'vite角色管理',
          //       status: null,
          //       name: 'vite角色管理',
          //       path: '/micro/vite',
          //       component: null,
          //       type: '2',
          //       sort: 4,
          //       meta: {
          //         hideMenu: '0',
          //         icon: '',
          //         microType: 'vite',
          //         title: 'vite角色管理',
          //         url: 'http://localhost:8085/child/vite/',
          //         microPath: '/upms/role',
          //         microName: 'upms2',
          //       },
          //     },
          //   ],
          //   label: '微应用',
          //   status: null,
          //   name: '微应用',
          //   path: '/microapp/vue3',
          //   component: null,
          //   type: '0',
          //   sort: 2,
          //   meta: {
          //     hideMenu: '0',
          //     icon: '',
          //     microType: 'vite',
          //     title: '微应用',
          //     url: 'http://localhost:80/child/vite/',
          //     microPath: '/microapp/vite',
          //     microName: 'upms',
          //   },
          // },
          // {
          //   id: '3',
          //   parentId: '0',
          //   children: [
          //     {
          //       id: '1565537157602574338',
          //       parentId: '3',
          //       children: [],
          //       label: 'demo',
          //       status: null,
          //       name: 'demo',
          //       path: '/upms/code-generation',
          //       component: null,
          //       type: '0',
          //       sort: 0,
          //       meta: { icon: '', title: 'demo' },
          //     },
          //     {
          //       id: '1565269523334135809',
          //       parentId: '3',
          //       children: [],
          //       label: '测试页面',
          //       status: null,
          //       name: '测试页面',
          //       path: '/upms/projecttest',
          //       component: null,
          //       type: '0',
          //       sort: 0,
          //       meta: { hideMenu: '0', icon: '', title: '测试页面' },
          //     },
          //     {
          //       id: '4',
          //       parentId: '3',
          //       children: [],
          //       label: '机构管理',
          //       status: null,
          //       name: '机构管理',
          //       path: '/upms/dept',
          //       component: null,
          //       type: '0',
          //       sort: 1,
          //       meta: { icon: '', title: '机构管理' },
          //     },
          //     {
          //       id: '5',
          //       parentId: '3',
          //       children: [],
          //       label: '用户管理',
          //       status: null,
          //       name: '用户管理',
          //       path: '/upms/account',
          //       component: null,
          //       type: '0',
          //       sort: 2,
          //       meta: { icon: '', title: '用户管理' },
          //     },
          //     {
          //       id: '7',
          //       parentId: '3',
          //       children: [],
          //       label: '菜单管理',
          //       status: null,
          //       name: '菜单管理',
          //       path: '/upms/menu',
          //       component: null,
          //       type: '0',
          //       sort: 3,
          //       meta: { icon: '', title: '菜单管理' },
          //     },
          //     {
          //       id: '6',
          //       parentId: '3',
          //       children: [],
          //       label: '角色管理',
          //       status: null,
          //       name: '角色管理',
          //       path: '/upms/role',
          //       component: null,
          //       type: '0',
          //       sort: 3,
          //       meta: { icon: '', title: '角色管理' },
          //     },
          //     {
          //       id: '17',
          //       parentId: '3',
          //       children: [],
          //       label: '字典管理',
          //       status: null,
          //       name: '字典管理',
          //       path: '/upms/dict',
          //       component: null,
          //       type: '0',
          //       sort: 4,
          //       meta: { icon: '', title: '字典管理' },
          //     },
          //     {
          //       id: '1565219232270221313',
          //       parentId: '3',
          //       children: [],
          //       label: '大客户管理',
          //       status: null,
          //       name: '大客户管理',
          //       path: '/upms/customers',
          //       component: null,
          //       type: '0',
          //       sort: 6,
          //       meta: { icon: '', title: '大客户管理' },
          //     },
          //     {
          //       id: '1565593237997191170',
          //       parentId: '3',
          //       children: [],
          //       label: '角色',
          //       status: null,
          //       name: '角色',
          //       path: '/upms/rolemanage',
          //       component: null,
          //       type: '0',
          //       sort: 18,
          //       meta: { icon: '', title: '角色' },
          //     },
          //   ],
          //   label: '平台管理',
          //   status: null,
          //   name: '平台管理',
          //   path: '/upms',
          //   component: null,
          //   type: '0',
          //   sort: 3,
          //   meta: { icon: '', title: '平台管理' },
          // },
          // {
          //   id: '8',
          //   parentId: '0',
          //   children: [
          //     {
          //       id: '25',
          //       parentId: '8',
          //       children: [],
          //       label: '机构管理',
          //       status: null,
          //       name: '机构管理',
          //       path: '/upms/tenant/dept',
          //       component: null,
          //       type: '0',
          //       sort: 1,
          //       meta: { hideMenu: '0', icon: '', title: '机构管理' },
          //     },
          //     {
          //       id: '9',
          //       parentId: '8',
          //       children: [],
          //       label: '租户管理',
          //       status: null,
          //       name: '租户管理',
          //       path: '/upms/tenant',
          //       component: null,
          //       type: '0',
          //       sort: 2,
          //       meta: { icon: '', title: '租户管理' },
          //     },
          //     {
          //       id: '10',
          //       parentId: '8',
          //       children: [],
          //       label: '成员管理',
          //       status: null,
          //       name: '成员管理',
          //       path: '/upms/tenant/member',
          //       component: null,
          //       type: '0',
          //       sort: 3,
          //       meta: { hideMenu: '1', icon: '', title: '成员管理' },
          //     },
          //     {
          //       id: '15',
          //       parentId: '8',
          //       children: [],
          //       label: '角色管理',
          //       status: null,
          //       name: '角色管理',
          //       path: '/upms/tenant/role',
          //       component: null,
          //       type: '0',
          //       sort: 4,
          //       meta: { icon: '', title: '角色管理' },
          //     },
          //     {
          //       id: '19',
          //       parentId: '8',
          //       children: [],
          //       label: '菜单管理',
          //       status: null,
          //       name: '菜单管理',
          //       path: '/upms/tenant/menu',
          //       component: null,
          //       type: '0',
          //       sort: 5,
          //       meta: { icon: '', title: '菜单管理' },
          //     },
          //     {
          //       id: '26',
          //       parentId: '8',
          //       children: [],
          //       label: '字典管理',
          //       status: null,
          //       name: '字典管理',
          //       path: '/upms/tenant/dict',
          //       component: null,
          //       type: '0',
          //       sort: 6,
          //       meta: { icon: '', title: '字典管理' },
          //     },
          //     {
          //       id: '11',
          //       parentId: '8',
          //       children: [],
          //       label: '应用管理',
          //       status: null,
          //       name: '应用管理',
          //       path: '/upms/tenant/project',
          //       component: null,
          //       type: '0',
          //       sort: 7,
          //       meta: { icon: '', title: '应用管理' },
          //     },
          //   ],
          //   label: '租户管理',
          //   status: null,
          //   name: '租户管理',
          //   path: '/upms/tenant',
          //   component: null,
          //   type: '0',
          //   sort: 4,
          //   meta: { icon: '', title: '租户管理' },
          // },
          // {
          //   id: '12',
          //   parentId: '0',
          //   children: [
          //     {
          //       id: '27',
          //       parentId: '12',
          //       children: [],
          //       label: '机构管理',
          //       status: null,
          //       name: '机构管理',
          //       path: '/upms/project/dept',
          //       component: null,
          //       type: '0',
          //       sort: 1,
          //       meta: { icon: '', title: '机构管理' },
          //     },
          //     {
          //       id: '13',
          //       parentId: '12',
          //       children: [],
          //       label: '成员原理',
          //       status: null,
          //       name: '成员原理',
          //       path: '/upms/project/member',
          //       component: null,
          //       type: '0',
          //       sort: 2,
          //       meta: { icon: '', title: '成员原理' },
          //     },
          //     {
          //       id: '16',
          //       parentId: '12',
          //       children: [],
          //       label: '角色管理',
          //       status: null,
          //       name: '角色管理',
          //       path: '/upms/project/role',
          //       component: null,
          //       type: '0',
          //       sort: 3,
          //       meta: { icon: '', title: '角色管理' },
          //     },
          //     {
          //       id: '18',
          //       parentId: '12',
          //       children: [],
          //       label: '菜单管理',
          //       status: null,
          //       name: '菜单管理',
          //       path: '/upms/project/menu',
          //       component: null,
          //       type: '0',
          //       sort: 4,
          //       meta: { icon: '', title: '菜单管理' },
          //     },
          //     {
          //       id: '28',
          //       parentId: '12',
          //       children: [],
          //       label: '字典管理',
          //       status: null,
          //       name: '字典管理',
          //       path: '/upms/project/dict',
          //       component: null,
          //       type: '0',
          //       sort: 5,
          //       meta: { icon: '', title: '字典管理' },
          //     },
          //   ],
          //   label: '应用管理',
          //   status: null,
          //   name: '应用管理',
          //   path: '/upms/project',
          //   component: null,
          //   type: '0',
          //   sort: 5,
          //   meta: { icon: '', title: '应用管理' },
          // },
          // {
          //   id: '24',
          //   parentId: '0',
          //   children: [
          //     {
          //       id: '14',
          //       parentId: '24',
          //       children: [],
          //       label: '审计日志',
          //       status: null,
          //       name: '审计日志',
          //       path: '/upms/audit-log',
          //       component: null,
          //       type: '0',
          //       sort: 1,
          //       meta: { icon: '', title: '审计日志' },
          //     },
          //     {
          //       id: '23',
          //       parentId: '24',
          //       children: [],
          //       label: '操作日志',
          //       status: null,
          //       name: '操作日志',
          //       path: '/upms/operate-log',
          //       component: null,
          //       type: '0',
          //       sort: 2,
          //       meta: { icon: '', title: '操作日志' },
          //     },
          //   ],
          //   label: '日志管理',
          //   status: null,
          //   name: '日志管理',
          //   path: '/upms/operate-log',
          //   component: null,
          //   type: '0',
          //   sort: 6,
          //   meta: { icon: '', title: '日志管理' },
          // },
        ],
      };
    },
  },
] as MockMethod[];
