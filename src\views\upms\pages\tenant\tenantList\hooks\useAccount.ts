import { ref, nextTick } from 'vue';

export default function useAccount(formData, userOptions, userSelect, userListRef) {
  const visibleAccount = ref(false);
  const selectAccountData = ref<any>([]);

  const handleFocusAccountEvent = (e) => {
    visibleAccount.value = true;
    nextTick(() => {
      //userSelect.value && userSelect.value?.blur();
      e.target.blur();
      if (selectAccountData.value.length > 0) {
        if (formData.value.adminIds) {
          userListRef.value &&
            userListRef.value?.handleSetSelectData(selectAccountData.value);
        } else {
          userListRef.value && userListRef.value?.handleSetSelectData(null);
        }
      }
    });
  };
  const handleChangeAccount = (data) => {
    if (data.length > 0) {
      userListRef.value &&
        userListRef.value?.handleSetSelectData(selectAccountData.value);
    }
  };

  const selectAccounts = (data) => {
    selectAccountData.value = data;
  };
  const saveAccount = () => {
    userOptions.value = [];
    formData.value.adminIds = [];
    for (let i = 0; i < selectAccountData.value.length; i++) {
      const obj = {
        label: selectAccountData.value[i].userName,
        value: selectAccountData.value[i].id,
      };
      formData.value.adminIds.push(selectAccountData.value[i].id);
      userOptions.value.push(obj);
    }
    visibleAccount.value = false;
  };
  const removeTagAccount = (val) => {
    selectAccountData.value = removeById(selectAccountData.value, val);
  };
  const removeById = (arr, id) => {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].id === id) {
        arr.splice(i, 1);
        break;
      }
    }
    return arr;
  };
  return {
    handleFocusAccountEvent,
    handleChangeAccount,
    selectAccounts,
    saveAccount,
    visibleAccount,
    selectAccountData,
    removeTagAccount,
  };
}
