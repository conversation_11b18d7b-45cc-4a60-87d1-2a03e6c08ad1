export interface NodeItem {
  children?: NodeItem[];
  id?: string;
  label?: string;
  parentId?: string;
  tenantName?: string;
  tenantAlias?: string;
  adminId?: string | string[];
  adminName?: string[] | string;
  description?: string;
  adminIds?: string[];
  version?: number | null;
  leaf?: string;
}

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace IgetTenantByIdSpace {
  export interface Data {
    children?: NodeItem[];
    id: string;
    parentId?: string;
    label: string;
    tenantName?: string;
    tenantAlias?: string;
    adminId?: string;
    description?: string;
  }
}
export interface options {
  label: string;
  value: string | number;
}
