<template>
  <div>
    <basic-tree
      v-bind="$attrs"
      ref="treeRef"
      :data="data"
      :tree-title="treeTitle"
      @node-click="nodeClick"
      :load="loadNode"
      lazy
    >
      <template #default="{ node }">
        <span class="tree-node__label" :title="node.label">{{ node.label }}</span>
      </template>
    </basic-tree>
  </div>
</template>
<script setup lang="ts">
  import { PropType, ref, unref } from 'vue';
  import { getDeptList } from '/@/views/upms/api/dept';
  import { BasicTree } from '/@/components/sys/BasicTree';
  import { TreeItem } from '/@/components/sys/BasicTree/types';
  import { DeptListParams } from '/@/views/upms/api/model/deptModel';

  const props = defineProps({
    treeTitle: {
      type: String as PropType<string>,
      default: () => '机构列表',
    },
    deptListParams: {
      type: Object as PropType<DeptListParams>,
      default: () => {
        return {
          isAll: true,
        };
      },
    },
  });
  let emits = defineEmits(['nodeClick', 'loadFinish']);
  const data = ref<TreeItem[]>([]);
  function init(params?: DeptListParams) {
    getDeptList(params).then((res) => {
      data.value = res;
    });
  }
  init(props.deptListParams);

  const treeRef = ref<InstanceType<typeof BasicTree>>();

  function nodeClick(treeNode: TreeItem) {
    emits('nodeClick', unref(treeNode));
  }
  function addNode(node: TreeItem) {
    console.log(node, 'node');
    if (node && node.parentId === '-1') {
      treeRef.value?.addTopNode(node);
    } else {
      if (node && node.parentId) {
        treeRef.value?.addLevelNode(node, node.parentId);
      }
    }
  }
  function setCurrentKey(key: Nullable<string | number>) {
    treeRef.value?.setCurrentKey(key);
  }
  function removeNode(node: TreeItem) {
    if (node) {
      treeRef.value?.removeNode(node);
    }
  }

  function setCheckedKeys(keys: string[] | number[]) {
    treeRef.value?.setCheckedKeys(keys);
  }
  function getNode(key: string) {
    return treeRef.value?.getNode(key);
  }
  const loadNode = (node, resolve: (data: any[]) => void) => {
    if (node.data.id == '17320508001') {
      node.level = 0;
      node.isLeaf = true;
    }
    if (node.level === 0) {
      return resolve([]);
    }
    getDeptList({ parentInstCode: node.data.instCode, opType: 'system' }).then(
      (res) => {
        resolve(res);
        emits('loadFinish');
      },
    );
  };
  defineExpose({
    addNode,
    setCurrentKey,
    removeNode,
    setCheckedKeys,
    init,
    treeData: data,
    getNode,
  });
</script>
<style scoped>
  .tree-title {
    margin-right: 9px;
    font-size: 18px;
  }

  .tree-header {
    padding-bottom: 5px;
    margin: 5px 10px;
    border-bottom: 1px solid rgba(228, 228, 231);
  }

  .tree-node__label {
    max-width: 260px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
