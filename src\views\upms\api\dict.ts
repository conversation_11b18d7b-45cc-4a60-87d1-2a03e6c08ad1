import {
  IPostDictSpace,
  IgetDictTreeSpace,
  IgetDictByIdSpace,
  getDictTreeParams,
} from './model/dictModel';
import { sysHttp, downloadHttp, uploadHttp } from '/@/views/upms/common/http';

enum Api {
  GetDictTree = '/dict/tree',
  dictListUrl = '/dict/',
  sortUrl = '/dict/change/sort',
  downloadUrl = '/dict/download',
  uploadUrl = '/dict/upload',
  refreshDictTree = '/dict/refresh',
}

/**
 * @description: Get user menu based on id
 */
// 获取menu树数据
export const getDictTree = (params?: getDictTreeParams) => {
  return sysHttp.get<IgetDictTreeSpace.Data[]>({
    url: Api.GetDictTree,
    params: params,
  });
};

export const refreshDictTreeApi = (params?: getDictTreeParams) => {
  return sysHttp.get<IgetDictTreeSpace.Data[]>({
    url: Api.refreshDictTree,
    params: params,
  });
};
// 根据id查详情
export const getDictDetailById = (id: string) => {
  return sysHttp.get<IgetDictByIdSpace.Data>({ url: `/dict/${id}` });
};
// 新增或者编辑
export const addOrEditDictDetail = (params: IPostDictSpace.RequestParams) => {
  return sysHttp.post({ url: `/dict/`, params });
};
// 删除
export const deleteDict = (id: string) => {
  return sysHttp.delete({ url: `/dict/${id}` });
};

export const dictListApi = () => {
  return sysHttp.get({ url: Api.dictListUrl, params: { pageNum: 1, pageSize: 10000 } });
};

export const getSortUrlApi = (params) =>
  sysHttp.get({ url: Api.sortUrl, params: params });

// // 删除
// export const deleteMenu = (ids) => {
//   return sysHttp.post({
//     url: `/menu`,
//     params: {
//       ids,
//     },
//   });
// };

export const downLoadApi = (params) =>
  downloadHttp.get({ url: Api.downloadUrl, params });
//

export const uploadApi = (params) => {
  return uploadHttp.post({
    url: Api.uploadUrl,
    params,
  });
};
