import { sysHttp, downloadHttp, uploadHttp } from '/@/views/upms/common/http';
import { ListParams, ListResultModel, SaveParams } from './model/microAppModel';
enum Api {
  microApp = '/micro/', // 微应用查询、保存、更新、删除
  //TODO
  downloadUrl = '/micro/download',
  uploadUrl = '/micro/upload',
}

// 微应用列表
export const MicroAppList = (params: ListParams) =>
  sysHttp.get<ListResultModel>({ url: Api.microApp, params });
// 保存微应用
export const SaveMicroApp = (params: SaveParams) =>
  sysHttp.post({ url: Api.microApp, params });
// 删除微应用
export const DelMicroApp = (ids: string) =>
  sysHttp.delete({ url: `${Api.microApp}${ids}` });
export const downLoadApi = (params) =>
  downloadHttp.post({ url: Api.downloadUrl, params });
//

export const uploadApi = (params) => {
  return uploadHttp.post({
    url: Api.uploadUrl,
    params,
  });
};
export const PutMicroApp = (id) => {
  return sysHttp.put({
    url: Api.microApp + '/' + id,
  });
};
