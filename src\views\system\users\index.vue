<template>
  <div class="user-container">
    <div class="user-header">
      <h3>用户管理</h3>
      <p>这是用户管理页面，用于管理系统用户信息。</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchParams" class="demo-form-inline">
        <el-form-item label="用户名">
          <el-input v-model="searchParams.username" placeholder="请输入用户名" clearable />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="searchParams.name" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus">
        新增用户
      </el-button>
      <el-button type="info" @click="handleRefresh" :icon="Refresh">
        刷新
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        style="width: 100%"
        height="400"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="username" label="用户名" width="120" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="phone" label="手机号" width="130" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column prop="sex" label="性别" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.sex === '0'" type="danger">女</el-tag>
            <el-tag v-else-if="row.sex === '1'" type="primary">男</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="enabled" label="状态" width="80">
          <template #default="{ row }">
            <el-tag v-if="row.enabled === '1'" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="primary" link @click="handleAssignRoles(row)">
              分配角色
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 用户编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="formData.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="性别" prop="sex">
          <el-select v-model="formData.sex" placeholder="请选择性别">
            <el-option label="女" value="0" />
            <el-option label="男" value="1" />
            <el-option label="未知" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-select v-model="formData.enabled" placeholder="请选择状态">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Refresh } from '@element-plus/icons-vue';
import {
  getUserPageList,
  saveUser,
  updateUser,
  deleteUsers,
  type UserInfo,
  type UserQueryParams
} from '/@/api/sys/users';

// 响应式数据
const tableRef = ref();
const formRef = ref();

const tableData = ref<UserInfo[]>([]);
const loading = ref(false);
const submitLoading = ref(false);

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('');
const isEdit = ref(false);

// 表单数据
const formData = ref<UserInfo>({
  username: '',
  name: '',
  phone: '',
  email: '',
  sex: '1',
  enabled: '1',
});

// 表单验证规则
const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  enabled: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 搜索参数
const searchParams = ref<UserQueryParams>({});

// 生命周期
onMounted(() => {
  loadUserList();
});

// 加载用户列表
const loadUserList = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchParams.value,
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    };

    const result = await getUserPageList(params);
    if (result && result.records) {
      tableData.value = result.records;
      pagination.total = result.total;
    }
  } catch (error) {
    ElMessage.error('加载用户列表失败');
    console.error('Load user list error:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  loadUserList();
};

// 重置搜索
const handleReset = () => {
  searchParams.value = {};
  pagination.current = 1;
  loadUserList();
};

// 刷新
const handleRefresh = () => {
  loadUserList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page;
  loadUserList();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.current = 1;
  loadUserList();
};

// 新增用户
const handleAdd = () => {
  dialogTitle.value = '新增用户';
  isEdit.value = false;
  formData.value = {
    username: '',
    name: '',
    phone: '',
    email: '',
    sex: '1',
    enabled: '1',
  };
  dialogVisible.value = true;
};

// 编辑用户
const handleEdit = (record: UserInfo) => {
  dialogTitle.value = '编辑用户';
  isEdit.value = true;
  formData.value = { ...record };
  dialogVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate();
    if (!valid) return;

    submitLoading.value = true;

    if (isEdit.value) {
      await updateUser(formData.value);
      ElMessage.success('更新用户成功');
    } else {
      await saveUser(formData.value);
      ElMessage.success('新增用户成功');
    }

    dialogVisible.value = false;
    loadUserList();
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新用户失败' : '新增用户失败');
    console.error('Submit user error:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 删除用户
const handleDelete = async (record: UserInfo) => {
  try {
    await ElMessageBox.confirm('确定要删除该用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await deleteUsers(record.id!);
    ElMessage.success('删除用户成功');
    loadUserList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除用户失败');
      console.error('Delete user error:', error);
    }
  }
};

// 分配角色 - 暂时只显示消息
const handleAssignRoles = (record: UserInfo) => {
  ElMessage.info(`分配角色功能开发中，用户：${record.name}`);
};

// 页面初始化
onMounted(() => {
  loadUserList();
});
</script>

<style scoped lang="scss">
.user-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  margin: 8px;

  .user-header {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e6e8ee;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .action-section {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
  }

  .table-section {
    .pagination-section {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
