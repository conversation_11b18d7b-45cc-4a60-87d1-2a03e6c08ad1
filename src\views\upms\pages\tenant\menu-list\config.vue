<template>
  <div class="dept-all">
    <basic-table
      :setting="false"
      :columns="columns"
      :data="tableData"
      :pagination="false"
    >
      <template #key="{ record }">
        <el-input v-model="record.key" placeholder="输入属性" clearable />
      </template>
      <template #value="{ record }">
        <el-input v-model="record.value" placeholder="输入值" clearable />
      </template>
      <template #action="{ record }">
        <el-icon
          :size="24"
          color="#FF4950"
          class="cursor-pointer"
          v-if="tableData.length > 1"
          @click="remove(record)"
        >
          <RemoveFilled />
        </el-icon>
        <el-icon
          v-if="record.index === tableData[tableData.length - 1].index"
          :size="24"
          color="#409EFC"
          class="ml-2 cursor-pointer"
          @click="add()"
        >
          <CirclePlusFilled />
        </el-icon>
      </template>
    </basic-table>
    <el-icon
      v-if="!tableData.length"
      title="新增行"
      :size="24"
      color="#409EFC"
      class="ml-2 cursor-pointer add-icon-meta"
      @click="add()"
    >
      <CirclePlusFilled />
    </el-icon>
  </div>
</template>
<script lang="ts" setup name="MenuConfig">
  import { ref, PropType, watch } from 'vue';
  import BasicTable from '/@/components/sys/BasicTable';
  import { columns } from '/@/views/upms/pages/menu-list/config.data';
  import { RemoveFilled, CirclePlusFilled } from '@element-plus/icons-vue';
  import { MetaConfigData } from '/@/views/upms/api/model/menuModel';
  const metaIndex = ref(0);
  const tableData = ref<MetaConfigData[]>([
    {
      key: '',
      value: '',
      index: metaIndex.value,
    },
  ]);
  const props = defineProps({
    meteData: {
      type: Array as PropType<MetaConfigData[]>,
      required: true,
    },
  });
  watch(
    () => {
      props.meteData;
    },
    () => {
      tableData.value = [...props.meteData];
    },
    {
      deep: true,
      immediate: true,
    },
  );
  // 添加配置
  function add() {
    metaIndex.value++;
    tableData.value.push({ key: '', value: '', index: metaIndex.value });
  }
  // 删除配置
  function remove(data) {
    tableData.value.forEach((item, i) => {
      if (data.index === item.index) {
        tableData.value.splice(i, 1);
      }
    });
  }
  defineExpose({ tableData });
</script>
<route lang="yaml">
meta:
  auth: true
</route>
<style scoped>
  .dept-all {
    position: relative;
  }

  .add-icon-meta {
    position: absolute;
    top: 7px;
    right: 42px;
    z-index: 2;
  }
</style>
