<template>
  <div>
    <div class="login-container">
      <p class="sys-logo"><img :src="logo" alt="logo" /></p>
      <div class="page-center">
        <el-steps :active="active" align-center>
          <el-step title="身份验证" description="验证账号信息" />
          <el-step title="重置密码" description="重新设置密码" />
          <el-step title="找回密码" description="设置成功" />
        </el-steps>
        <div class="form-div" v-if="active === 1">
          <div>
            <el-input
              type="text"
              class="loign-input mb-4"
              v-model="userInfo.userName"
              :prefix-icon="UserIcon"
              placeholder="请输入账号名/邮箱/身份证号/员工工号"
            />
          </div>
          <!--<div>
            <el-autocomplete
              class="loign-input mb-4"
              style="width: 100%"
              v-model="userInfo.email"
              :prefix-icon="Monitor"
              placeholder="请输入邮箱"
              :fetch-suggestions="querySearchEmail"
              :trigger-on-focus="false"
            />
          </div>-->
          <div class="flex">
            <el-input
              type="text"
              class="loign-input mb-4"
              v-model="code"
              :prefix-icon="Lock"
              placeholder="请输入验证码"
            />
            <el-button
              type="primary"
              style="margin-left: 10px"
              @click="handleCode"
              :disabled="!show"
            >
              <span v-if="show">获取验证码</span>
              <span v-else>重新发送（{{ count }}s）</span></el-button
            >
          </div>
          <div v-if="sendEmail">
            <p class="success-tip">邮件已发送至{{ sendEmail }}</p>
          </div>

          <div class="set-btn cursor-pointer next-btn">
            <el-button type="primary" @click="handleNext" :loading="loading1">
              下一步
            </el-button>
          </div>
        </div>
        <div class="form-div" v-if="active === 2">
          <div>
            <el-input
              type="password"
              class="loign-input mb-4"
              show-password
              v-model="pwd.newPass"
              :prefix-icon="Lock"
              placeholder="请输入新密码"
            />
          </div>
          <div>
            <el-input
              type="password"
              class="loign-input mb-4"
              show-password
              v-model="pwd.configNewPass"
              :prefix-icon="Lock"
              placeholder="请确认新密码"
            />
          </div>
          <div class="set-btn cursor-pointer next-btn">
            <el-button type="primary" @click="resPwd" :loading="loading2">
              下一步
            </el-button>
          </div>
        </div>
        <div class="form-div" v-if="active === 3">
          <div class="icon-div">
            <el-icon class="icon"><Select /></el-icon>
          </div>
          <p class="form-div-sup">密码重置成功后，请重新登录</p>
          <div class="set-btn cursor-pointer next-btn" @click="goLogin">
            <el-button type="primary"> 返回登录 </el-button>
          </div>
        </div>
      </div>

      <!-- <p class="fix-foot">Copyright © 2022-2033 中国邮政版权所有</p> -->
    </div>
  </div>
</template>

<script setup lang="ts" name="Login">
  import logo from '/@/assets/images/login/logo-upms.svg';
  import { User as UserIcon, Lock, Select } from '@element-plus/icons-vue';
  import { ref } from 'vue';
  import { getCodeApi, getfindPwdApi, getValCode, getDictPassword } from '/@/api/sys';
  import { ElMessage } from 'element-plus';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { useLogout } from '/@/hooks/web/useLogout';
  import { PwdCheck } from '/@/utils/operate/PwdCheckUtils';
  // import { getDictStorage } from '/@/utils/storage/dict';
  // import { getAuthStorage } from '/@/utils/storage/auth';
  // const passwordRegArr = getDictStorage().biz_password_reg;
  const passwordRegArr = ref<any>([]);
  const sensitiveWords = ref<any>([]);
  const { logout } = useLogout();
  // import { clearInterval } from 'timers';

  // getCodeApi

  const active = ref(1);
  const userInfo = ref({
    userName: '',
    // email: '',
  });
  const sendEmail = ref<string>('');
  const userId = ref('');
  const code = ref('');
  const randomStr = ref(new Date().getTime());
  const pwd = ref({ newPass: '', configNewPass: '' });

  const timer = ref<any>(null);
  const show = ref(true);
  const count = ref(0);

  const loading1 = ref(false);
  const loading2 = ref(false);

  // 邮箱填写过滤
  // function createFilter(queryString) {
  //   return (item) => {
  //     return item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
  //   };
  // }

  const handleCode = async () => {
    const params = {
      // email: userInfo.value.email,
      userName: userInfo.value.userName,
      randomStr: randomStr.value,
    };
    const res = await getCodeApi(params);
    console.log(res);

    if (res) {
      userId.value = res.userId;
      sendEmail.value = res.email;
      getCode();
    }
  };
  const getCode = () => {
    count.value = 300;
    if (!timer.value) {
      show.value = false;
      timer.value = setInterval(() => {
        if (count.value > 0 && count.value <= 300) {
          count.value--;
        } else {
          clearInterval(timer.value);
          timer.value = null;
          show.value = true;
        }
      }, 1000);
    }
  };

  const handleNext = async () => {
    if (!userInfo.value.userName) {
      ElMessage({
        message: '请输入账号名',
        grouping: true,
        type: 'warning',
      });
      return false;
    }
    // const pattern = new RegExp(sensitiveWords.value.join('|'), 'i');
    // console.log(pattern);
    // if (pattern.test(userInfo.value.userName)) {
    //   ElMessage({
    //     message: '用户名中不能包含敏感词汇！',
    //     grouping: true,
    //     type: 'warning',
    //   });
    //   return false;
    // }
    // if (!userInfo.value.email) {
    //   ElMessage({
    //     message: '请输入邮箱',
    //     grouping: true,
    //     type: 'warning',
    //   });
    //   return false;
    // }
    // if (!emailReg.test(userInfo.value.email)) {
    //   ElMessage({
    //     message: '邮箱格式不正确',
    //     grouping: true,
    //     type: 'warning',
    //   });
    //   return false;
    // }
    if (!code.value) {
      ElMessage({
        message: '请输入验证码',
        grouping: true,
        type: 'warning',
      });
      return false;
    }
    loading1.value = true;
    const params = {
      code: encryptedSM4(code.value),
      randomStr: randomStr.value,
    };
    const res = await getValCode(params).catch(() => {
      loading1.value = false;
    });
    if (res) {
      active.value = 2;
      loading1.value = false;
      getDictPasswordList();
    }
  };

  const resPwd = async () => {
    if (!pwd.value.newPass) {
      ElMessage({
        message: '请输入新密码',
        grouping: true,
        type: 'warning',
      });
      return false;
    } else if (!pwd.value.configNewPass) {
      ElMessage({
        message: '请确认密码',
        grouping: true,
        type: 'warning',
      });
      return false;
    } else if (pwd.value.newPass !== pwd.value.configNewPass) {
      ElMessage({
        message: '新密码和确认新密码不一致,请重新输入',
        grouping: true,
        type: 'warning',
      });
      return false;
    }
    console.log(passwordRegArr.value);
    const newPwdResult = PwdCheck(pwd.value.newPass);
    const configNewPwdResult = PwdCheck(pwd.value.configNewPass);
    if (newPwdResult || configNewPwdResult) {
      ElMessage({
        message: newPwdResult || configNewPwdResult || '密码规则不符合要求！',
        grouping: true,
        type: 'warning',
      });
      return false;
    }

    loading2.value = true;
    const params = {
      newPass: encryptedSM4(pwd.value.newPass),
      userId: encryptedSM4(userId.value),
    };
    const res = await getfindPwdApi(params);
    if (res) {
      active.value = 3;
      loading2.value = false;
    }
  };
  const goLogin = () => {
    logout();
  };

  const getDictPasswordList = async () => {
    const params = {
      code: 'cee4f60b2dbbe7e93cab75ca3bd6be716aee73af04ac3d103defd5ab2cd05e2a', // biz_password_reg
      url: '2499966da9a73adf6dc98f04536c917ad6f3a81a2758c2e6cefe0623541b6b3a',
    };
    const res = await getDictPassword(params);
    passwordRegArr.value = res.biz_password_reg;
  };

  // hkDO97RIWdrmHTm3ArsELC3BIe9h4BzphcaVtCyEFcI=
  const getUserNameList = async () => {
    const params = {
      code: '77d8e9be7e1a88a9f45617ee7d3d482e222e7155117ce6da02f1943617a81d06', // biz_sensitive_words
      url: '2499966da9a73adf6dc98f04536c917ad6f3a81a2758c2e6cefe0623541b6b3a',
    };
    const res = await getDictPassword(params);
    console.log(res.biz_sensitive_words);
    sensitiveWords.value =
      res.biz_sensitive_words.length > 0
        ? res.biz_sensitive_words.map((obj) => obj.value)
        : [];
  };

  getUserNameList();
</script>

<style scoped lang="scss">
  @import url('/src/styles/login/login.css');
  .page-center {
    // width: 1180px;
    width: 100%;
    height: calc(100vh - 110px);
    // display: flex;
    /* margin: calc((100vh - 520px) / 2 - 50px) auto calc((100vh - 520px) / 2); */
    margin: 50px auto 55px;
    background-color: #ffffff;
    border-radius: 16px;
    .form-div {
      width: 400px;
      margin: 50px auto 0;

      .next-btn {
        width: 100%;
        .el-button {
          width: 100%;
        }
      }
      .icon-div {
        .icon {
          font-size: 48px;
          text-align: center;
          margin: 24px auto 32px;
          svg {
            color: #409eff;
          }
        }
      }
      .form-div-sup {
        font-size: 24px;
        text-align: center;
        color: #409eff;
        margin-bottom: 64px;
      }
    }
  }
  .fix-foot {
    width: 100%;
    text-align: center;
    position: fixed;
    bottom: 20px;
    font-size: 12px;
    color: #000;
    letter-spacing: 1.66px;
    line-height: 12px;
    font-weight: 400;
  }
  .success-tip {
    margin: -10px 0 10px 10px;
    font-size: 12px;
    color: #409eff;
  }
</style>

<route lang="yaml">
name: Retrieve
meta:
  layout: BlankLayout
</route>
