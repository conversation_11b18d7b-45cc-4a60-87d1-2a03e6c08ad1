<template>
  <div class="content">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane min-size="30" size="30">
        <div class="left">
          <div class="top-button">
            <el-button type="primary" @click="addNode" v-auth="['platform_dict_add']" :icon="Plus"
              >新增</el-button
            >
            <el-button type="primary" @click="downLoad(false)" v-auth="['platform_dict_export']" :icon="Download" plain
              >导出</el-button
            >
            <el-button
              type="primary"
              @click="fileVisible = true"
              v-auth="['platform_dict_import']"
              :icon="Upload"
              plain
              >导入</el-button
            >
            <el-button type="danger" @click="deleteNode" v-auth="['platform_dict_delete']" :icon="Delete" plain
              >删除</el-button
            >
            <el-button type="primary" @click="refresh" v-auth="['platform_dict_refresh']" :icon="Refresh"
              >刷新</el-button
            >
          </div>
          <div class="tree-content">
            <div class="tree-input">
              <el-input placeholder="请输入字典名称" clearable v-model="value" />
            </div>
            <div class="tree-div">
              <z-tree
                :setting="setting"
                :nodes="treeDate"
                class="baseTree"
                :name="value"
                @onCreated="handleCreated"
              />
            </div> </div
        ></div>
      </Pane>
      <Pane class="mid" ref="mid">
        <!--右侧div内容-->
        <div class="right">
          <p class="title"> 字典信息 </p>
          <div class="form-div" v-if="formData.id || status === 'add'">
            <basic-form
              class="basic-form-div"
              labelWidth="140px"
              :form-list="dictionaryFormSchema"
              :form-data="formData"
              ref="formRef"
              :showSubmit="false"
              :check-strictly="false"
              :isCreate="isCreate"
            />

            <div class="bottom-btn-div">
              <el-button
                type="primary"
                :loading="loading"
                @click="saveForm"
                v-auth="['platform_dict_save']"
                >保存</el-button
              >
            </div>
          </div>
          <div v-else class="no-form"> 未选择任何字典 </div>
        </div>
      </Pane>
    </Splitpanes>

    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
      class="file-dialog"
    >
      <el-button type="primary" link @click="downLoad(true)" style="margin-bottom: 12px"
        >下载平台字典导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, markRaw } from 'vue';
  import ZTree from '@cpit-cpvf/tree';
  import '@cpit-cpvf/tree/dist/style.css';
  import type { FormInstance } from 'element-plus';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import {
    getDictDetailById,
    addOrEditDictDetail,
    deleteDict,
    getSortUrlApi,
    downLoadApi,
    uploadApi,
    getDictTree,
    refreshDictTreeApi,
  } from '../../api/dict';
  import useZtree from '/@/hooks/upms/dictTree/useZtree';
  import { dictionaryFormSchema } from './dictionary.data';
  import {
    CircleClose,
    Refresh,
    Plus,
    Download,
    Upload,
    Delete,
  } from '@element-plus/icons-vue';
  import { useDictStore } from '/@/stores/modules/dict';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import useUpload from '/@/hooks/upms/upload/useUpload';
  import type { UploadInstance } from 'element-plus';
  const upload = ref<UploadInstance>();
  const useDict = useDictStore();
  const formRef = ref<FormInstance | null>(null);
  const { setting, handleCreated, value, ztree, nodeId, nodeStatus, sortParams } =
    useZtree();
  const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const isCreate = ref(false);
  const treeDate = ref<any>([]);
  const formData = ref<any>({});
  const loading = ref(false);
  const uploadLoading = ref(false);
  const parentId = ref('');
  const status = ref('');
  const newNode = ref<any>({});

  const getDictList = async () => {
    const res = await getDictTree();
    treeDate.value = res;
  };

  const refresh = async () => {
    const res = await refreshDictTreeApi();
    treeDate.value = res;
  };

  const addNode = () => {
    const nodes =
      ztree.value && ztree.value.getSelectedNodes()
        ? ztree.value.getSelectedNodes()
        : [];

    if (nodes.length > 0) {
      status.value = 'add';
      newNode.value = {
        parentId: nodes.length > 0 ? nodes[0].id : '',
        name: '未命名',
        id: '',
      };
      parentId.value = nodes.length > 0 ? nodes[0].id : '';
      const newTreeNode =
        nodes.length > 0
          ? ztree.value.addNodes(nodes[0], [-1], newNode.value)
          : ztree.value.addNodes(null, [-1], newNode.value);

      ztree.value.selectNode(newTreeNode[0]);
      ztree.value.setting.callback.onClick(
        '',
        ztree.value.setting.treeId,
        newTreeNode[0],
      );
      isCreate.value = true;
      const ruleFormRef = formRef.value && formRef.value.ruleFormRef;
      if (ruleFormRef) {
        formRef.value?.resetForm(ruleFormRef);
      }
    } else {
      ElMessage({
        type: 'warning',
        message: '请先选择字典节点在进行新增',
        showClose: true,
      });
    }
  };
  getDictList();

  const saveForm = () => {
    loading.value = true;
    const getData = formRef.value && formRef.value?.submitForm;
    const ruleFormRef = formRef.value && formRef.value?.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status === 'success') {
        addOrEditDictDetail({ ...data, parentId: parentId.value })
          .then((res) => {
            nodeId.value = res;
            getDictList();
            ElMessage({
              type: 'success',
              showClose: true,
              message: formData.value.id ? '修改字典成功' : '新增字典成功',
            });
            status.value = '';
            loading.value = false;
            nodeStatus.value = true;
            useDict.reqDict();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const deleteNode = () => {
    if (nodeId.value && JSON.stringify(newNode.value) === '{}') {
      ElMessageBox.confirm('确认删除该字典数据？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'error',
        icon: markRaw(CircleClose),
        customStyle: {
          backgroundColor: 'rgba(255, 241, 240, 1)',
          border: '1px soild rgba(255, 163, 158, 1)',
        },
      })
        .then(() => {
          const node = ztree.value.getNodesByParam('id', nodeId.value, null)[0];
          const parentNode = node.getParentNode();
          deleteDict(nodeId.value).then(() => {
            ElMessage({
              message: '删除成功',
              grouping: true,
              type: 'success',
            });
            getDictList();
            useDict.reqDict();
            nodeId.value = parentNode.id;
          });
        })
        .catch(() => {});
    } else if (!nodeId.value && JSON.stringify(newNode.value) === '{}') {
      ElMessage({
        type: 'warning',
        showClose: true,
        message: '请先选择字典节点在进行删除操作！',
      });
    } else {
      const node = ztree.value.getSelectedNodes();
      ztree.value.removeNode(node[0]);
      getDictList();
      newNode.value = {};
      nodeId.value = '';
    }
  };
  const downLoad = (isTemplate) => {
    downLoadApi({ isTemplate }).then((res) => {
      console.log('res?.request?.responseURL', res);
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
      });
      const fileName = '平台字典管理' + '.xlsx';
      const elink = document.createElement('a'); // 创建a标签
      elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click(); // 点击下载
      console.log(elink.href);
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink); // 释放标签
    });
  };
  const uploadFile = () => {
    uploadLoading.value = true;
    let formData = new FormData();
    formData.append('file', fileData.value?.raw);
    uploadApi(formData)
      .then(() => {
        ElMessage({
          type: 'success',
          message: '导入成功',
        });
        fileVisible.value = false;
        getDictList();
        uploadLoading.value = false;
      })
      .catch(() => {
        uploadLoading.value = false;
      });
  };
  watch(
    () => nodeId.value,
    (val) => {
      if (val) {
        getDictDetailById(val).then((res) => {
          formData.value = res;
          parentId.value = res.parentId;
          nodeStatus.value = false;
          status.value = '';
        });
      } else {
        formData.value = {
          display: '1',
        };
      }
    },
  );

  watch(
    () => sortParams.value,
    (val) => {
      if(val.ifLevel) {
        let params = { ...val, opType: 'sys' };
        getSortUrlApi(params).then(() => {
          getDictList();
          nodeId.value = '';
        });
      }else {
        ElMessage({
          showClose: true,
          type: 'warning',
          message: '只允许在同一个父字典节点下排序！',
        });
      }
    },
  );
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  }
  /*左侧div样式*/
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: calc(100% - 60px);
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 48px;
      font-size: 18px;
      padding-left: 18px;
      background: #ffffff;
      font-weight: 700;
    }
  }
  .top-button {
    padding: 8px 18px 8px 18px;
    border-bottom: 1px solid #e4e7ed;
    .upload-btn {
      display: inline-block;
      margin: 0 12px;
    }
  }
  /*拖拽区div样式*/
  .resize {
    cursor: col-resize;
    float: left;
    position: relative;
    top: 45%;
    background-color: #d6d6d6;
    border-radius: 5px;
    margin-top: -10px;
    width: 10px;
    height: 50px;
    background-size: cover;
    background-position: center;
    font-size: 32px;
    color: white;
  }
  /*拖拽区鼠标悬停样式*/
  .resize:hover {
    color: #444444;
  }
  /*右侧div'样式*/
  .mid {
    float: left;
    width: 55%; /*右侧初始化宽度*/
    height: 100%;
    background: #fff;
    box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
  }
  .dept-tabs-xl {
    padding-top: 9px;
    padding-left: 1px;
    width: 100%;
    height: 100%;
    // color:#d1e237
    ::deep(.el-tabs__content) {
      height: calc(100% - 60px) !important;
    }
  }
  .table-content {
    background: #ffffff;
    margin-top: 20px;
  }

  .base-table-all {
    margin: 0 12px;
  }
  .inst-code {
    color: var(--el-color-primary);
    cursor: pointer;
  }
  .form-div {
    width: 100%;
    height: 100%;
    .basic-form-div {
      height: calc(100% - 110px);
      border-bottom: 1px solid #e4e7ed;
      overflow: auto;
    }
    .bottom-btn-div {
      display: flex;
      justify-content: flex-end;
      padding-right: 18px;
      padding-top: 14px;
    }
  }
  .no-form {
    width: 100%;
    height: 100%;
    line-height: 100%;
    padding-top: 120px;
    font-size: 24px;
    text-align: center;
  }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }

  .file-dialog .el-dialog__body {
    padding: 20px;
  }
</style>
