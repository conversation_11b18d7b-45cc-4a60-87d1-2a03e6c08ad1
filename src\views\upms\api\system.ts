import {
  AccountParams,
  DeptListItem,
  MenuParams,
  RoleParams,
  RolePageParams,
  MenuListGetResultModel,
  // DeptListGetResultModel,
  AccountListGetResultModel,
  RolePageListGetResultModel,
  RoleListGetResultModel,
} from './model/systemModel';
import { sysHttp } from '/@/views/upms/common/http';

enum Api {
  AccountList = '/user/',
  DeptList = '/dept/tree',
  setRoleStatus = '/system/setRoleStatus',
  MenuList = '/system/getMenuList',
  RolePageList = '/system/getRoleListByPage',
  GetAllRoleList = '/system/getAllRoleList',
  saveDept = '/dept/',
  getRole = '/role/combo-data',
  resetPsssword = '/user/resetPassword',
  userPermission = '/menu/user-permission',
}
// 获取账号列表
export const getAccountList = (params: AccountParams) =>
  sysHttp.get<AccountListGetResultModel>({ url: Api.AccountList, params });
// 获取部门列表
export const getDeptList = (params?: DeptListItem) =>
  sysHttp.get<[]>({ url: Api.DeptList, params });

export const getMenuList = (params?: MenuParams) =>
  sysHttp.get<MenuListGetResultModel>({ url: Api.MenuList, params });

export const getRoleListByPage = (params?: RolePageParams) =>
  sysHttp.get<RolePageListGetResultModel>({ url: Api.RolePageList, params });

export const getAllRoleList = (params?: RoleParams) =>
  sysHttp.get<RoleListGetResultModel>({ url: Api.GetAllRoleList, params });

export const setRoleStatus = (id: number, status: string) =>
  sysHttp.post({ url: Api.setRoleStatus, params: { id, status } });
// 修改部门详情
export const saveDept = (params: any) => sysHttp.post({ url: Api.saveDept, params });
// 获取部门详情
export const getDeptDetail = (id: string) =>
  sysHttp.get({ url: `${Api.saveDept}${id}` });
// 删除部门
export const delDep = (id: string) => sysHttp.delete({ url: `${Api.saveDept}${id}` });
// 获取角色
export const getRole = () => sysHttp.get({ url: Api.getRole });
// 修改用户信息
export const saveUser = (params: any) => sysHttp.post({ url: Api.AccountList, params });
// 锁定和激活
export const lockUser = (id: string) => sysHttp.put({ url: `${Api.AccountList}${id}` });
// 删除用户
export const delUser = (id: string) =>
  sysHttp.delete({ url: `${Api.AccountList}${id}` });
// 重置密码
export const resetPsssword = (params: any) =>
  sysHttp.post({ url: Api.resetPsssword, params });
// 更新权限编码
export const userPermission = () =>
  sysHttp.get({ url: Api.userPermission });
