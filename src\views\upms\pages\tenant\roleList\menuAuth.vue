<template>
  <div>
    <div class="top-checkbox">
      <el-checkbox v-model="openChecked" @change="handleOpenChecked"
        >全部展开</el-checkbox
      >
      <el-checkbox
        v-model="allChecked"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
        >全选</el-checkbox
      >
    </div>
    <z-tree
      :setting="setting"
      :nodes="treeData"
      class="baseTree"
      @onCreated="handleCreated"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, PropType } from 'vue';
  import { getRoleUserMenu } from '/@/views/upms/api/menu';
  import ZTree from '@cpit-cpvf/tree';
  import '@cpit-cpvf/tree/dist/style.css';
  const emits = defineEmits(['checkboxIds']);
  const props = defineProps({
    menuIds: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    roleId: {
      type: String as PropType<string>,
      default: '',
    },
  });
  const openChecked = ref(true);
  const allChecked = ref(true);
  const treeData = ref<any[]>([]);
  const ztree = ref<any>();
  const menuIdList = ref<string[]>([]);
  const isIndeterminate = ref(false);
  const zOnCheck = () => {
    var checkedNodes: any[] = ztree.value.getCheckedNodes();
    menuIdList.value = [];
    for (var i = 0; i < checkedNodes.length; i++) {
      menuIdList.value.push(checkedNodes[i].id);
    }

    if (
      menuIdList.value.length > 0 &&
      menuIdList.value.length < treeData.value.length
    ) {
      isIndeterminate.value = true;
    }
    if (menuIdList.value.length == treeData.value.length) {
      isIndeterminate.value = false;
    }
  };
  const setting = ref<any>({
    data: {
      simpleData: {
        enable: true,

        pIdKey: 'parentId',
      },
      key: {
        name: 'name',
      },
    },
    check: {
      enable: true, //显示复选框
      chkStyle: 'checkbox',
      chkboxType: { Y: 'ps', N: 'ps' },
    },
    view: {
      showIcon: false,
      dblClickExpand: false, // 双击不允许展开节点
    },
    callback: {
      onCheck: zOnCheck,
    },
  });
  const handleCreated = (ztreeObj) => {
    ztree.value = ztreeObj;

    if (!!openChecked.value) {
      ztree.value && ztree.value.expandAll(true);
    } else {
      ztree.value && ztree.value.expandAll(false);
    }

    if (menuIdList.value && menuIdList.value.length > 0) {
      for (let i = 0; i < menuIdList.value.length; i++) {
        var node =
          ztree.value &&
          ztree.value.getNodesByParam('id', menuIdList.value[i], null)[0]; //根据id获取节点

        if (node != null) {
          ztree.value && ztree.value.checkNode(node, true, false); //选中节点
        }
      }
    }
  };

  const getMenuTreeData = () => {
    getRoleUserMenu({ opType: 'tenant' }).then((res) => {
      for (let i = 0; i < res.length; i++) {
        if (res[i]?.menuType === 'system') {
          res[i].name = res[i].name + '--系统';
        }
        if (res[i]?.menuType === 'tenant') {
          res[i].name = res[i].name + '--租户';
        }
        if (res[i]?.menuType === 'project') {
          res[i].name = res[i].name + '--应用';
        }
      }
      treeData.value = res;
      if (menuIdList.value.length < treeData.value.length) {
        isIndeterminate.value = true;
      }
      if (menuIdList.value.length === treeData.value.length) {
        isIndeterminate.value = false;
      }
      if (menuIdList.value.length === 0) {
        isIndeterminate.value = false;
        allChecked.value = false;
      }
    });
  };
  // getMenuTreeData();

  const handleCheckAllChange = (val) => {
    console.log(val);
    allChecked.value = val;
    isIndeterminate.value = false;

    if (!!val) {
      ztree.value && ztree.value.checkAllNodes(true); //选中节点
    } else {
      ztree.value && ztree.value.checkAllNodes(false); //选中节点
    }
    zOnCheck();
  };

  const handleOpenChecked = (val) => {
    openChecked.value = val;
    if (!!openChecked.value) {
      ztree.value && ztree.value.expandAll(true);
    } else {
      ztree.value && ztree.value.expandAll(false);
    }
  };

  const getCheckboxIds = () => {
    emits('checkboxIds', menuIdList.value);
  };

  watch(
    () => props.menuIds,
    (value: string[]) => {
      menuIdList.value = value;
    },
    {
      immediate: true,
      deep: true,
    },
  );
  watch(
    () => props.roleId,
    () => {
      getMenuTreeData();
    },
  );

  defineExpose({ getCheckboxIds });
</script>

<style scoped>
  .top-checkbox {
    margin-bottom: 10px;
    margin-left: 20px;
  }
</style>
