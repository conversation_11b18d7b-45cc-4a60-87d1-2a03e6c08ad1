/**
 * 迁移示例：如何将 pages/index.vue 中的 fetch 调用替换为 axios 调用
 */

import { AxiosApiService } from './axios-api-service'

// ==================== 原来的 fetch 调用 ====================

// 1. 刷新历史记录
async function refreshHistoryOld() {
  const url = `/api/streaming-events/chat/sessions?page=0&size=50&userId=${encodeURIComponent(currentUserId.value)}`
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }
  const data = await response.json()
  return data
}

// 2. 搜索聊天历史
async function searchHistoryOld(keyword: string) {
  const url = `/api/streaming-events/chat/search?userId=${encodeURIComponent(currentUserId.value)}&keyword=${encodeURIComponent(keyword)}&page=0&size=50`
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }
  const data = await response.json()
  return data
}

// 3. 加载聊天历史
async function loadChatHistoryOld(chatId: string) {
  const url = `/api/streaming-events/chat/${encodeURIComponent(chatId)}/history`
  const response = await fetch(url)
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }
  const data = await response.json()
  return data
}

// 4. 删除聊天会话
async function deleteChatSessionOld(chatId: string) {
  const url = `/api/streaming-events/chat/session/${encodeURIComponent(chatId)}`
  const response = await fetch(url, {
    method: 'DELETE'
  })
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`)
  }
  return response
}

// ==================== 新的 axios 调用 ====================

// 1. 刷新历史记录
async function refreshHistoryNew(currentUserId: string) {
  try {
    const data = await AxiosApiService.getChatSessionsPaginated(currentUserId, 0, 50)
    return data
  } catch (error) {
    console.error('刷新历史记录失败:', error)
    throw error
  }
}

// 2. 搜索聊天历史
async function searchHistoryNew(currentUserId: string, keyword: string) {
  try {
    const data = await AxiosApiService.searchChatHistory(currentUserId, keyword, 0, 50)
    return data
  } catch (error) {
    console.error('搜索聊天历史失败:', error)
    throw error
  }
}

// 3. 加载聊天历史
async function loadChatHistoryNew(chatId: string) {
  try {
    const data = await AxiosApiService.getChatHistoryById(chatId)
    return data
  } catch (error) {
    console.error('加载聊天历史失败:', error)
    throw error
  }
}

// 4. 删除聊天会话
async function deleteChatSessionNew(chatId: string) {
  try {
    await AxiosApiService.deleteChatSession(chatId)
    console.log('聊天会话删除成功')
  } catch (error) {
    console.error('删除聊天会话失败:', error)
    throw error
  }
}

// ==================== 在 Vue 组件中的使用示例 ====================

/*
// 在 pages/index.vue 中的使用示例

<script setup lang="ts">
import { AxiosApiService } from '../api/axios-api-service'

// 刷新历史记录
const refreshHistory = async () => {
  historyLoading.value = true
  try {
    console.log('📚 加载聊天历史')
    
    const data = await AxiosApiService.getChatSessionsPaginated(currentUserId.value, 0, 50)
    
    if (data && data.content) {
      chatHistory.value = data.content.map((item: any) => ({
        id: item.id,
        title: item.title || '新对话',
        lastMessage: item.lastMessage || '',
        timestamp: item.updatedAt || item.createdAt,
        messageCount: item.messageCount || 0
      }))
    } else {
      chatHistory.value = []
    }
  } catch (error: any) {
    console.error('❌ 加载聊天历史失败:', error)
    ElMessage.error('加载聊天历史失败: ' + (error.message || '未知错误'))
    chatHistory.value = []
  } finally {
    historyLoading.value = false
  }
}

// 搜索聊天历史
const searchHistory = async (keyword: string) => {
  if (!keyword.trim()) {
    await refreshHistory()
    return
  }

  historyLoading.value = true
  try {
    console.log('🔍 搜索聊天历史:', keyword)
    
    const data = await AxiosApiService.searchChatHistory(currentUserId.value, keyword, 0, 50)
    
    if (data && data.content) {
      chatHistory.value = data.content.map((item: any) => ({
        id: item.id,
        title: item.title || '新对话',
        lastMessage: item.lastMessage || '',
        timestamp: item.updatedAt || item.createdAt,
        messageCount: item.messageCount || 0
      }))
    } else {
      chatHistory.value = []
    }
  } catch (error: any) {
    console.error('❌ 搜索聊天历史失败:', error)
    ElMessage.error('搜索失败: ' + (error.message || '未知错误'))
    chatHistory.value = []
  } finally {
    historyLoading.value = false
  }
}

// 加载聊天历史
const loadChatHistory = async (item: any) => {
  try {
    console.log('📖 加载聊天历史:', item.id)
    
    // 添加加载状态
    const loadingRound = {
      id: 'loading-' + Date.now(),
      type: 'loading',
      content: '正在加载历史消息...',
      timestamp: new Date().toISOString(),
      isUser: false
    }
    conversationRounds.value.push(loadingRound)

    const data = await AxiosApiService.getChatHistoryById(item.id)
    
    // 移除加载状态
    conversationRounds.value = conversationRounds.value.filter(round => round.id !== loadingRound.id)
    
    if (data && data.messages) {
      // 处理历史消息...
      conversationRounds.value = data.messages.map((msg: any) => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        timestamp: msg.timestamp,
        isUser: msg.isUser
      }))
    }
  } catch (error: any) {
    console.error('❌ 加载聊天历史失败:', error)
    ElMessage.error('加载聊天历史失败: ' + (error.message || '未知错误'))
  }
}

// 删除聊天会话
const deleteChatSession = async (item: any) => {
  try {
    console.log('🗑️ 删除聊天会话:', item.id)
    
    await AxiosApiService.deleteChatSession(item.id)
    
    console.log('✅ 聊天会话删除成功')
    ElMessage.success('聊天会话已删除')
    
    // 刷新历史记录
    await refreshHistory()
  } catch (error: any) {
    console.error('❌ 删除聊天会话失败:', error)
    ElMessage.error('删除失败: ' + (error.message || '未知错误'))
  }
}
</script>
*/

// ==================== 主要优势 ====================

/*
1. 错误处理简化：
   - 不需要手动检查 response.ok
   - 不需要手动调用 response.json()
   - axios 会自动处理 HTTP 错误

2. 类型安全：
   - 完整的 TypeScript 类型定义
   - 编译时类型检查

3. 统一配置：
   - 自动添加认证头
   - 统一的超时设置
   - 统一的错误处理

4. 更好的调试：
   - 统一的请求/响应日志
   - 更清晰的错误信息

5. 代码简洁：
   - 减少样板代码
   - 更易读的 API 调用
*/

export {
  refreshHistoryNew,
  searchHistoryNew,
  loadChatHistoryNew,
  deleteChatSessionNew
}
