<!-- eslint-disable vue/no-v-html -->
<template>
  <div class="chat-ai-container">
    <!-- 对话历史列表 -->
    <div class="chat-history">
      <div class="history-header">
        <h3>AI对话</h3>
        <el-button @click="handleAdd" type="primary" circle size="small">
          <el-icon>
            <Plus />
          </el-icon>
        </el-button>
      </div>
      <div class="history-list">
        <div v-for="(item, index) in historyList" :key="index">
          <div v-for="subItem in item.data" :class="{ active: historySessionId === subItem.chatId }"
            :key="subItem.chatId" @click="handleClick(subItem)" class="history-item">
            <!-- {{ subItem.chatName  }} -->
            <span class="history-title">{{ subItem.chatName }}</span>
          </div>
          <!-- <span class="history-title">{{ item.title || '新对话' }}</span>
          <el-icon class="delete-icon" @click.stop="deleteChat(item)"><Delete /></el-icon> -->
        </div>
      </div>
    </div>
    <div class="right-content">
      <div class="chat-container">
        <div class="chat-window">
          <div class="session-name-header" v-if="sessionName">{{ sessionName }}</div>
          <div class="chat-messages" ref="chatMessages" @scroll="handleScroll">
            <div v-if="messages.length > 0">
              <div v-for="(message, index) in messages" :key="message" class="message"
                :class="{ sent: message.messageType === 'USER' }">
                <div v-if="message.messageType === 'USER'"> {{ message.text }}</div>

                <div class="assistant-div" v-if="message.messageType !== 'USER'">

                  <el-collapse v-model="activeNames">
                    <el-collapse-item :name="index">
                      <template #title>
                        <el-icon style="margin-right: 10px" v-if="message.metadata">
                          <CircleCheck />
                        </el-icon>
                        <el-icon v-if="!message.metadata" class="icnIcon"
                          style="width: 16px; height: 16px; margin-right: 10px">
                          <Loading />
                        </el-icon>
                        {{
                          message.metadata
                            ? ' AI分析用户输入已完成'
                            : ' AI正在分析用户输入'
                        }}
                      </template>
                      <div v-html="renderMarkdown(message.text)"></div>
                      <!-- 渲染图表 -->
                      <div v-if="message.chartData && message.chartType" class="chart-container">
                        <!-- <h3 class="chart-title" v-if="message.chartType">{{ message.chartType === 'Line' ? '折线图' : message.chartType === 'Bar' ? '柱状图' : message.chartType === 'Pie' ? '饼图' : '图表' }}</h3> -->
                        <!-- 图表操作区域 -->
                        <div class="chart-title" v-if="message.chartType">
                          <div class="chart-title-inner">{{ message.chartType === 'Line' ? '数据趋势图' : message.chartType
                            === 'Bar' ? '数据对比图' :
                            message.chartType === 'Pie' ? '占比分析图' : 
                            message.chartType === 'Table' ? '数据表格' : '图表' }}</div>
                          <div class="chart-actions">
                            <el-tooltip content="固定到仪表盘" placement="top">
                              <el-icon @click="openDashboardSelector(index)">
                                <Paperclip />
                              </el-icon>
                            </el-tooltip>
                            <el-dropdown>
                              <span class="rotate-icon-90">
                                <el-icon>
                                  <MoreFilled />
                                </el-icon>
                              </span>
                              <template #dropdown>
                                <el-dropdown-menu>
                                  <el-dropdown-item @click="openChartSettings(index)">设置图表</el-dropdown-item>
                                  <el-dropdown-item @click="exportChartAsImage(index)">导出图片</el-dropdown-item>
                                </el-dropdown-menu>
                              </template>
                            </el-dropdown>
                          </div>
                        </div>
                        <div v-if="message.chartType !== 'Table'" :id="'chart-' + index" class="chart-wrapper"></div>
                       
                        <div class="vtable-container" v-show="message.records?.length > 0">
                        <!-- <div
                          :ref="'tableContainer' + index"
                          class="vtable-wrapper"
                        ></div> -->

                        <VTable :data="message.records" :columns="message.columns.map(col => col.prop)" height="250px" />
                      </div>
                      </div>
                      
                    </el-collapse-item>
                  </el-collapse>
                </div>
                <!-- {{ message.text }} -->
              </div>
            </div>
            <div v-else style="
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
              ">
              <el-empty description="我能帮你做什么呢？" :image-size="200" />
            </div>
          </div>
          <!-- <div v-else> 暂无数据 </div>   -->
          <!-- 聊天输入区域 - 移到条件渲染外部，使其始终显示 -->
          <div class="chat-input-area">
            <div class="select-area">
              <el-cascader v-model="selectedDataSource" :options="dataSources" placeholder="选择数据库" class="select-box"
                size="small" :props="{ expandTrigger: 'hover' }" clearable>
                <template #prefix>
                  <el-icon>
                    <DataLine />
                  </el-icon>
                </template>
              </el-cascader>
            </div>

            <div class="input-area">
              <el-input v-model="newMessage" type="textarea" :rows="3" @keydown.enter="sendMessage"
                placeholder="选中表进行数据库问答" resize="none" />
              <!-- <el-button type="primary" class="send-button" > -->
              <el-icon @click="sendMessage" class="send-button" v-if="!btnLoading">
                <Position />
              </el-icon>
              <el-icon v-else class="send-btn icnIcon" style="width: 20px">
                <Loading />
              </el-icon>
              <!-- </el-button> -->
            </div>

            <!-- AI大模型选择框 -->
            <div class="model-select-area">
              <el-select v-model="selectedModel" placeholder="选择模型" class="model-select" size="small">
                <el-option v-for="item in aiModels" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <p style="font-size: 12px;margin:0 auto;color:#999;text-align:center">内容由AI生成，请仔细甄别</p>
          </div>
        </div>
      </div>
    </div>
    <!-- <div ref="tableContainer" class="vtable-wrapper"></div> -->
  </div>
  <!-- 仪表盘选择器组件 -->
  <DashboardSelector v-model:visible="dashboardSelectorVisible" :chart-data="currentChartForDashboard"
    @select="handleDashboardSelect" @create="handleDashboardCreate" />

  <!-- 图表设置对话框 -->
  <div>
    <el-dialog v-model="chartSettingsVisible" title="图表设置" width="80%" :close-on-click-modal="false" destroy-on-close
      class="chart-settings-dialog">
      <template #header>
        <div class="dialog-header">
          <el-button v-if="!sqlView" type="primary" size="small" @click="switchToSqlView">
            <el-icon>
              <Setting />
            </el-icon>
            <span>数据配置</span>
          </el-button>
          <el-button v-if="sqlView" type="primary" size="small" @click="switchToSqlView">
            <el-icon>
              <Setting />
            </el-icon>
            返回图表
          </el-button>
          <!-- <el-icon class="close-icon" @click="handleClose">
              <Close />
            </el-icon> -->
        </div>
      </template>
      <ChartSettings v-if="!sqlView" :chartData="ChartDatas" />
      <SqlEditor v-if="sqlView" :value="currentSql" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="chartSettingsVisible = !chartSettingsVisible">取消</el-button>
          <el-button type="primary" @click="saveSettings">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>

<script setup lang="ts">
import { ref, nextTick, createApp, markRaw, computed, onMounted, onBeforeUnmount } from 'vue';
import hljs from 'highlight.js';
import { marked } from 'marked';
import 'highlight.js/styles/xcode.css';
import SqlCodeBlock from './SqlCodeBlock.vue';
import ChartSettings from './ChartSettings.vue';
import SqlEditor from './SqlEditor.vue';
import * as echarts from 'echarts';
import VTable from './VTable.vue';
import { buildUUID } from '../utils/uuid';
import { dataSources, aiModels, chatList } from '../utils/data'
import {
  getSessionsAPI,
  getMessagesAPI,
  getListApi,
} from '../api';

// import icnIcon from '/@/assets/icons/icn_loading.svg';
// import sendIcon from '/@/assets/icons/send.svg';
import { CircleClose, Loading, Position, Plus, CircleCheck, Delete, MoreFilled, Paperclip } from '@element-plus/icons-vue';
import DashboardSelector from './DashboardSelector.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { loadEnv, warpperEnv } from '../../build/index';
import { useTheme } from '@chat/themechange';

// 使用主题composable
const { defaultColor, changeTheme, initTheme } = useTheme()
console.log('localStorage.getItem()', defaultColor.value)
const { VITE_APP_BASE_URL_PREFIX } = warpperEnv(loadEnv());
const sysPreFix = VITE_APP_BASE_URL_PREFIX + '/api';

const app = ref<any>(null);
const historyListLoading = ref(false);
const selectedDataSource = ref('');
const selectedModel = ref('');
const historyList = ref<any>([]);
const historySessionId = ref('');
const initSessionId = ref('');
const sqlView = ref(false);
// 存储当前SQL语句，用于传递给SqlEditor组件
const currentSql = ref('');
// 仪表盘选择器相关
const dashboardSelectorVisible = ref(false);
const currentChartForDashboard = ref<any>(null);
const dashboardList = ref<any>([]);

// 图表设置相关
const chartSettingsVisible = ref(false);
const currentChartSettings = ref<any>(null);
// 初始化主题
initTheme()
const ChartDatas = computed(() => {
  if (!currentChartSettings.value) return {};

  const index = currentChartSettings.value.messageIndex;
  const message = messages.value[index];

  return {
    chartSchema: {
      chartType: message.chartType,
      summary: currentChartSettings.value.title,
      xField: message.xField,
      yField: message.yField
    },
    metaData: {
      headerList: message.chartData?.length > 0 ?
        Object.keys(message.chartData[0]).map(key => ({ name: key })) : [],
      dataList: message.chartData?.map(item =>
        Object.values(item)
      ) || []
    }
  };
});

// 保存图表设置
const saveSettings = () => {
  if (!currentChartSettings.value) return;

  const index = currentChartSettings.value.messageIndex;
  const message = messages.value[index];

  // 更新图表类型
  message.chartType = ChartDatas.value.chartSchema?.chartType;
  message.xField = ChartDatas.value.chartSchema?.xField;
  message.yField = ChartDatas.value.chartSchema?.yField;

  // 重新初始化图表
  nextTick(() => {
    initChart(index);
  });

  chartSettingsVisible.value = false;
  ElMessage.success('图表设置已应用');
};

// 聊天历史列表数据
const chatHistoryList = ref([...chatList.data.data]);
const currentChatIndex = ref(0);

// const activeNames = ref<number[]>([1, 3, 5, 7, 9, 11, 13, 15, 17, 19, 21]);
const activeNames = ref<number[]>([]);

const sessionName = ref('');

const popoverRef = ref(null);

const loading = ref(false);
const btnLoading = ref(false);
const hasMore = ref(true);

// const observer = ref<any>(null);

const page = ref(0);

// 引用聊天消息容器
const chatMessages = ref(null);
const newMessage = ref('');
// 聊天记录
const messages = ref<any>([]);
// 定义事件源的引用，用于实时通信
const eventSource = ref<any>(null);

const dialogVisible = ref(false);
// 获取主应用传递的 props
const wujieProps = ref(window?.$wujie?.props || {});
console.log("初始数据来自主应用的setting：", window?.$wujie, wujieProps.value);
const primaryColor = ref(wujieProps.value.primaryColor || '');
// const dataList = ref<any>([]);
// const tableContainer = ref<any>(null);
// const tableInstance = ref<any>(null);
// const instance = getCurrentInstance();

// 初始化图表
const initChart = (index: number|string) => {
  const message = messages.value[index];
  // 如果是表格类型，处理表格数据并返回
  if (message.chartType === 'Table') {
    // 处理表格数据
    if (message.chartData && message.chartData.length > 0) {
      // 从第一条数据中提取列信息
      const firstRow = message.chartData[0];
      const columns = Object.keys(firstRow).map(key => ({
        prop: key,
        label: key,
        width: ''
      }));
      
      // 设置表格数据
      message.columns = columns;
      message.records = message.chartData;
    }
    return; // 表格类型不需要初始化echarts
  }
  const chartContainer = document.getElementById(`chart-${index}`);
  if (!chartContainer) return;

  // 获取当前消息的图表数据
  
  console.log('initChart', message);
  if (!message.chartData || message.chartData.length === 0) return;

  

  // 初始化echarts实例
  const chartInstance = echarts.init(chartContainer);

  // 准备数据
  const xData = message.chartData.map(item => item[message.xField]);
  const yData = message.chartData.map(item => item[message.yField]);
  console.log('xData', xData, message);
  console.log('yData', yData);
  // 配置图表选项
  let option = {};

  if (message.chartType === 'Line') {
    option = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: xData
        // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: message.xField,
        type: 'line',
        data: yData
        // data: [150, 230, 224, 218, 135, 147, 260],
      }]
    };
  } else if (message.chartType === 'Bar') {
    option = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: xData
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        name: message.xField,
        type: 'bar',
        data: yData
      }]
    };
  } else if (message.chartType === 'Pie') {
    const pieData = xData.map((item, index) => ({
      name: item,
      value: yData[index]
    }));

    option = {
      title: {
        text: ''
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: message.legend,
        type: 'pie',
        radius: '60%',
        data: pieData
      }]
    };
  }

  // 渲染图表
  chartInstance.setOption(option);

  // 响应窗口大小变化
  window.addEventListener('resize', () => {
    chartInstance.resize();
  });
};

onMounted(() => {
  window?.$wujie?.bus.$on('wujieTheme', (color: string) => {
    console.log('收到主应用的主题颜色：', color);
    primaryColor.value = color;
    changeTheme(color);

  });
  console.log('onMounted', primaryColor.value)
});
/**
 * 切换到SQL视图或返回图表视图
 * 当切换到SQL视图时，确保currentSql有值
 */
const switchToSqlView = () => {
  sqlView.value = !sqlView.value;

  // 如果切换到SQL视图，确保当前SQL有值
  if (sqlView.value && currentChartSettings.value) {
    const index = currentChartSettings.value.messageIndex;
    const message = messages.value[index];

    // 如果当前消息中有SQL数据，则使用它
    if (message && message.sql) {
      currentSql.value = message.sql;
    }
  }
};
/**
 * 打开仪表盘选择器
 * @param index 消息索引
 */
const openDashboardSelector = (index) => {
  const message = messages.value[index];
  if (!message.chartData || !message.chartType) {
    ElMessage.warning('图表数据不完整');
    return;
  }
  
  // 如果是表格类型，提示不支持添加到仪表盘
  if (message.chartType === 'Table') {
    ElMessage.info('表格类型暂不支持添加到仪表盘');
    return;
  }

  currentChartForDashboard.value = {
    chartType: message.chartType,
    xField: message.xField,
    yField: message.yField,
    data: message.chartData,
    messageIndex: index
  };
  dashboardSelectorVisible.value = true;
};

/**
 * 选择仪表盘后的处理
 */
const handleDashboardSelect = (dashboardId) => {
  ElMessage.success(`已将图表固定到仪表盘 ID: ${dashboardId}`);
  // 这里可以添加将图表添加到仪表盘的逻辑
};

/**
 * 创建新仪表盘
 */
const handleDashboardCreate = (dashboardInfo) => {
  // 这里可以添加创建新仪表盘的逻辑
  dashboardList.value.push({
    id: Date.now().toString(), // 临时ID
    name: dashboardInfo.name
  });
  ElMessage.success(`已创建新仪表盘: ${dashboardInfo.name}`);
};

/**
 * 打开图表设置
 * @param index 消息索引
 */
const openChartSettings = (index) => {
  const message = messages.value[index];
  if (!message.chartData || !message.chartType) {
    ElMessage.warning('图表数据不完整');
    return;
  }

  currentChartSettings.value = {
    chartType: message.chartType,
    title: message.chartType === 'Line' ? '数据趋势图' :
      message.chartType === 'Bar' ? '数据对比图' :
        message.chartType === 'Pie' ? '占比分析图' :
          message.chartType === 'Table' ? '数据表格' : '图表',
    messageIndex: index
  };
  chartSettingsVisible.value = true;
};

/**
 * 应用图表设置
 */
const applyChartSettings = () => {
  if (!currentChartSettings.value) return;

  const index = currentChartSettings.value.messageIndex;
  const message = messages.value[index];

  // 更新图表类型
  message.chartType = currentChartSettings.value.chartType;

  // 重新初始化图表
  nextTick(() => {
    initChart(index);
  });

  chartSettingsVisible.value = false;
  ElMessage.success('图表设置已应用');
};

/**
 * 导出图表为图片
 * @param index 消息索引
 */
const exportChartAsImage = (index) => {
  // 获取当前消息
  const message = messages.value[index];
  
  // 如果是表格类型，提示不支持导出
  if (message.chartType === 'Table') {
    ElMessage.info('表格类型不支持导出为图片');
    return;
  }
  
  // 获取图表DOM元素
  const chartId = `chart-${index}`;
  const chartDom = document.getElementById(chartId);

  if (!chartDom) {
    ElMessage.error('未找到图表元素');
    return;
  }

  // 获取图表实例
  const chartInstance = echarts.getInstanceByDom(chartDom);
  if (!chartInstance) {
    ElMessage.error('未找到图表实例');
    return;
  }

  try {
    // 获取图表的base64编码
    const dataURL = chartInstance.getDataURL({
      type: 'png',
      pixelRatio: 2, // 提高导出图片的清晰度
      backgroundColor: '#fff'
    });

    // 创建下载链接
    const link = document.createElement('a');
    link.href = dataURL;

    // 获取图表标题作为文件名
    const chartTitle = message.chartType === 'Line' ? '数据趋势图' :
      message.chartType === 'Bar' ? '数据对比图' :
        message.chartType === 'Pie' ? '占比分析图' : '图表';
    link.download = `${chartTitle}-${new Date().getTime()}.png`;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success('图表导出成功');
  } catch (error) {
    console.error('导出图表失败:', error);
    ElMessage.error('导出图表失败');
  }
};

// 发送消息
const sendMessage = (event) => {
  if (event.key === 'Enter' && btnLoading.value === true) {
    event.preventDefault();
    return false;
  }
  const value = newMessage.value;

  // btnLoading.value = false;
  if (!value) {
    return ElMessage.warning('请输入问题');
  }

  if (eventSource.value != null) {
    eventSource.value.close();
  }
  // 将用户输入的消息添加到消息列表中，并设置消息类型为用户发送
  messages.value.push({
    text: newMessage.value,
    messageType: 'USER',
  });
  messages.value.push({
    text: '',
    messageType: 'ASSISTANT',
    columns: [],
    records: [],
    chartData: [],
    chartType: '',
    yField: [],
    xField: []
  });

  activeNames.value.push(messages.value.length - 1);
  newMessage.value = '';
  let messageOrigin = '';
  const apiBaseUrl = sysPreFix + '/chat/agent/dml-stream-chat';
  const encodedValue = encodeURIComponent(value);
  let sessionId = '';
  let isNewSession = false;
  if (initSessionId.value.length === 0) {
    sessionId = buildUUID();
    initSessionId.value = sessionId;
    isNewSession = true;
  } else {
    sessionId = initSessionId.value;
    isNewSession = false;
  }
  historySessionId.value = sessionId;
  eventSource.value = new EventSource(
    `${apiBaseUrl}?message=${encodedValue}&chatId=${sessionId}&isNewChat=${isNewSession}&workFlowName=dml-chart-workflow`,
  );
  btnLoading.value = true;
  eventSource.value.onmessage = function (event) {

    try {
      let substring = event.data.replaceAll('data:', '');
      let parse = JSON.parse(substring);
      messageOrigin += parse.result?.output?.text;
      // if (parse.metadata?.status === 'stop' || parse.metadata?.status === 'STOP') {
      // messageOrigin = messageOrigin
      //   .replace('<think>', "<div class='think'>")
      //   .replace('</think>', '</div>');

      messages.value[messages.value.length - 1].metadata = parse.result?.metadata;
      btnLoading.value = false;
      // getSessions(false);
      // console.log('parse.result.metadata', parse.metadata);
      // 处理图表数据
      if (parse.result?.output?.data) {
        messages.value[messages.value.length - 1].chartData = parse.result.output.data?.selectData || [];
        messages.value[messages.value.length - 1].chartType = parse.result.output.data?.chartType || 'Line';
        messages.value[messages.value.length - 1].yField = parse.result.output.data?.yField || [];
        messages.value[messages.value.length - 1].xField = parse.result.output.data?.xField || [];

        // 存储SQL语句，用于传递给SqlEditor组件
        if (parse.result.output.data?.sql) {
          currentSql.value = parse.result.output.data.sql;
          // 同时将SQL存储到消息对象中，以便在切换视图时使用
          messages.value[messages.value.length - 1].sql = parse.result.output.data.sql;
        }

        // 在下一个渲染周期初始化图表
        nextTick(() => {
          initChart(messages.value.length - 1);
        });
        eventSource.value?.close();
      }

      // getList(parse.metadata?.sql);

      // console.log('messages.value');
      // console.log(messages.value);
      // }

      messages.value[messages.value.length - 1].text = messageOrigin;

      // 调用滚动方法
      scrollToBottom();
    } catch (error) {
      console.error('消息异常:', error);
    }
  };
  eventSource.value.onerror = function () {
    eventSource.value.close();
  };
  eventSource.value.onclose = function (event) {
    console.log('事件关闭:', event);
  };

  setTimeout(() => {
    console.log(messages.value);
  }, 50000);
};
const getList = async (code) => {
  const res = await getListApi({ sql: code }).catch(() => {
    ElMessage({
      showClose: true,
      type: 'error',
      message: '执行错误',
    });
  });
  console.log(res);
  messages.value[messages.value.length - 1].columns = getColumnConfig(res.columns);
  messages.value[messages.value.length - 1].records = res.records;
  // columns, records
  // dataList.value = res.records;
  // nextTick(() => {
  //   initTable(res.columns, res.records, index);
  // });
};
// 自动滚动到底部
const scrollToBottom = async () => {
  await nextTick(() => {
    if (chatMessages.value) {
      // console.log(chatMessages.value.scrollHeight);
      chatMessages.value.scrollTo({
        top: chatMessages.value.scrollHeight,
        behavior: 'smooth',
      });
    }
  });
};

// 切换对话
const switchChat = (chat) => {
  // 找到选中对话的索引
  const index = chatHistoryList.value.findIndex(item => item.id === chat.id);
  if (index !== -1) {
    currentChatIndex.value = index;

    // 更新会话ID
    initSessionId.value = chat.id;
    historySessionId.value = chat.id;

    // 更新会话名称
    sessionName.value = chat.title || '新对话';

    // 加载对话消息
    messages.value = chat.messages || [];

    // 清空输入框
    newMessage.value = '';
  }
};

// 删除对话
const deleteChat = (chat) => {
  // 确认删除
  ElMessageBox.confirm('确定要删除这个对话吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 找到要删除的对话索引
    const index = chatHistoryList.value.findIndex(item => item.id === chat.id);
    if (index !== -1) {
      // 删除对话
      chatHistoryList.value.splice(index, 1);

      // 如果删除的是当前对话，则切换到第一个对话
      if (currentChatIndex.value === index) {
        if (chatHistoryList.value.length > 0) {
          switchChat(chatHistoryList.value[0]);
        } else {
          // 如果没有对话了，则清空消息
          messages.value = [];
          initSessionId.value = '';
          historySessionId.value = '';
          sessionName.value = '';
        }
      } else if (currentChatIndex.value > index) {
        // 如果删除的是当前对话之前的对话，则当前索引减1
        currentChatIndex.value--;
      }

      ElMessage.success('删除成功');
    }
  }).catch(() => {
    // 取消删除
  });
};

// const scrollToBottom = async () => {
//   await nextTick();
//   if (chatMessages.value) {
//     const lastMessage =
//       chatMessages.value?.children[chatMessages.value.children.length - 1];
//     if (lastMessage) {
//       lastMessage.scrollIntoView({ behavior: 'smooth', block: 'end' });
//     }
//   } else {
//     console.error('聊天框不可用');
//   }
// };

const renderMarkdown = (text: string) => {
  marked.setOptions({
    highlight: function (code, lang) {
      if (lang && hljs.getLanguage(lang)) {
        return hljs.highlight(code, { language: lang }).value;
      }
      return hljs.highlightAuto(code).value;
    },
    breaks: true,
  });

  // 使用自定义渲染器处理代码块
  const renderer = new marked.Renderer();

  // 保存原始的code渲染方法
  const originalCodeRenderer = renderer.code.bind(renderer);

  // 重写code渲染方法，为SQL代码块添加复制和执行按钮
  renderer.code = (code, language, isEscaped) => {
    const originalHtml = originalCodeRenderer(code, language, isEscaped);
    if (language && language.toLowerCase() === 'sql') {
      const id = `sql-placeholder-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
      nextTick(() => {
        const placeholder = document.getElementById(id);
        if (placeholder) {
          app.value = createApp(SqlCodeBlock, {
            code: code,
            codeHtml: originalHtml,
            language: language,
            onExecuteSuccess: executeSuccessEvent,
          });
          app.value.mount(placeholder);
        }
      });
      return `<div id="${id}" class="sql-code-placeholder"></div>`;
    }

    // 非SQL代码块保持原样
    return originalHtml;
  };

  marked.setOptions({ renderer });
  return marked(text);
};

const executeSuccessEvent = async () => {
  dialogVisible.value = true;
  // nextTick(() => {
  //   initTable(data.columns, data.records);
  // });
};

const getColumnConfig = (columns) => {
  return columns.map((column) => ({
    prop: column,
    label: column,
    width: 'auto',
    sort: true,
    filter: true,
  }));
};

const getSessions = async (init) => {
  // loading.value = true
  sessionName.value = '';
  const res = await getSessionsAPI();
  console.log(res);
  if (JSON.stringify(res.result) !== '{}') {
    historyList.value = Object.keys(res.result)
      .map((key) => ({
        name: key,
        data: res.result[key],
      }))
      .sort((a, b) => new Date(b.name) - new Date(a.name)); // 按日期倒序
    console.log(historyList.value);

    if (!!init) {
      initSessionId.value = historyList.value[0]?.data[0].chatId;
      historySessionId.value = historyList.value[0]?.data[0].chatId;
      sessionName.value = historyList.value[0]?.data[0].chatName;
      getMessages(historyList.value[0]?.data[0].chatId, true);
    }
  }
  historyListLoading.value = false
};

const getMessages = async (value, isSrcoll = false) => {
  if (loading.value || !hasMore.value) return;
  loading.value = true;
  const res = await getMessagesAPI({
    chatId: value,
    size: '40',
    page: page.value,
  }).catch(() => {
    loading.value = false;
  });
  // console.log(res[1].text);
  hasMore.value = res.result.hasMore;
  // messages.value = [res[1]];
  // messages.value = res.messages;
  const newItems = res.result.messages;
  console.log(newItems);
  page.value++;
  messages.value = [...newItems, ...messages.value];
  console.log(messages.value);
  for (let i = 0; i < messages.value.length; i++) {
    if (messages.value[i].messageType === 'ASSISTANT') {
      activeNames.value.push(i);
    }
  }
  loading.value = false;
  if (isSrcoll) {
    setTimeout(() => {
      scrollToBottom();
    });
  }
};
getSessions(true);

const handleClick = (data) => {
  console.log(data);
  sessionName.value = data.chatName;
  historySessionId.value = data.chatId;
  loading.value = false;
  hasMore.value = true;
  messages.value = [];
  // const loading = ref(false);
  // const hasMore = ref(true);
  page.value = 0;
  getMessages(historySessionId.value, true);
  // popoverRef.value && popoverRef.value?.hide();
};

const handleAdd = () => {
  messages.value = [];
  sessionName.value = '';
  initSessionId.value = '';
};

const handleDelete = (item) => {
  // type 1 批量删除  0 单独删除
  ElMessageBox.confirm('确认删除该历史数据？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'error',
    icon: markRaw(CircleClose),
    customStyle: {
      backgroundColor: 'rgba(255, 241, 240, 1)',
      border: '1px soild rgba(255, 163, 158, 1)',
    },
  })
    .then(() => {
      delChatApi(item.chatId).then(() => {
        ElMessage({
          showClose: true,
          type: 'success',
          message: '删除成功',
        });
      });
      setTimeout(() => {
        getSessions(true);
      }, 2000);
    })
    .catch(() => { });
};

const handleScroll = async () => {
  setTimeout(() => {
    if (chatMessages.value.scrollTop < 50 && hasMore.value) {
      getMessages(historySessionId.value);
    }
  }, 100);
};

</script>

<style scoped lang="scss">
.chat-ai-container {
  /* width: calc(100vh); */
  height: calc(100vh);
  overflow: hidden;
  /* width: calc(100% - 280px); */

  display: flex;
}

.session-name-header {
  background-color: #fff;
  padding: 10px 16px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chart-container {
  margin: 15px 0;
  padding: 10px;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.chart-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
  color: #333;
}

.chat-history {
  width: 240px;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;
}

.history-header {
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e6e6e6;
}

.history-header h3 {
  margin: 0;
  font-size: 16px;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.history-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-item:hover {
  background-color: #f5f7fa;
}

.history-item.active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.chart-wrapper {
  width: 100%;
  height: 300px;
  border-radius: 4px;
}

.right-content {
  height: 100%;
  width: 100%;
  background: #f7f7f7;
}


/* 代码高亮样式 */
pre {
  background-color: #F0F0F0;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  position: relative;
}

code {
  font-size: 12px;
  border-radius: 0.25em;
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace, "SF Pro Display", "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", "Apple Color Emoji", "Twemoji Mozilla", "Noto Color Emoji", "Android Emoji" !important;
}

/* SQL代码块按钮样式 */
.code-block-wrapper {
  position: relative;
  margin-bottom: 16px;
}

.code-block-buttons {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 8px;
  z-index: 10;
}

.attr-handle {
  background: green;
  padding: 3px;
}

.icon-click {
  padding: 6px 8px;
  border-right: 1px solid #f1f1f1;
  line-height: 100%;
}

.chat-container {
  height: 100%;
  width: 100%;
  display: flex;
  // background: #dddddd;
}

.chat-window {
  width: 100%;
  display: flex;
  flex-direction: column;

  .chat-top {
    display: flex;
    justify-content: space-between;
    padding: 8px 16px;
    border-bottom: 1px solid #ddd;
    font-weight: 700;
  }
}

.group-name {
  line-height: 24px;
  font-size: 16px;
  margin-bottom: 5px;
}

.group-li:hover .action-button {
  display: block;
}

.group-li:hover {
  background: #dddddd;
  border-radius: 3px;
}

.group-li.action-li {
  background: #e1f3d8;

  border-radius: 3px;
}

.group-li.action-li {
  .group-li-title {
    color: #67c23a;
  }
}

.action-button {
  display: none;
}

.group-li {
  line-height: 28px;
  cursor: pointer;
  font-size: 14px;
  text-indent: 8px;
  // color: #409eff;
  // margin-top: 5px;
  display: flex;
  justify-content: space-between;

  .group-li-title {
    display: inline-block;
    width: 300px;
    white-space: nowrap;
    /* 防止文本换行 */
    overflow: hidden;
    /* 隐藏溢出的内容 */
    text-overflow: ellipsis;
    /* 显示省略符号来代表被修剪的文本 */
  }

  .group-li-icon {
    font-size: 16px;
    line-height: 28px;
    // display: inline-block;
    // padding: 6px 6px 0 0;
    margin-top: 5px;
    margin-right: 5px;
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;

  // background-color: #fff;
  .message {
    margin-bottom: 10px;
    //   width: 381px;
    background-color: #fff;
    border-radius: 3px;

    .assistant-div {
      padding: 0 12px;
    }

    pre {
      background-color: #f9f9f9;
      padding: 10px;
      border-radius: 5px;
      white-space: pre-wrap;
    }

    :deep(.think) {
      display: inline-block;
      padding: 0 10px;
      color: #999999;
      font-size: 13px;
      background-color: #efecec;
      border-radius: 5px;
    }

    &.sent {
      // text-align: right;
      background: #ffffff;
      padding: 8px 12px;
      border-radius: 3px;

      pre {
        background-color: #e1ffc7;
      }
    }
  }
}

.icon-top {
  cursor: pointer;
}

.chat-input {
  display: flex;
  padding: 10px;
  position: relative;

  textarea {
    flex: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    resize: none;
  }

  .send-btn {
    // margin-left: 10px;
    // padding: 10px 20px;
    border: none;
    color: #fff;
    // border-radius: 5px;
    cursor: pointer;
    position: absolute;
    right: 20px;
    bottom: 20px;
    // padding: 8px 5px;
    width: 28px;
    z-index: 99;

    &:hover {
      background-color: #dddddd;
    }
  }
}

:deep(.el-collapse-item__header) {
  background: none !important;
}

:deep(.el-collapse-item__wrap) {
  background: none !important;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}

// .icnIcon {

// }
.icnIcon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.vtable-container {
  width: 100%;
  margin-top: 24px;

  .vtable-container-title {
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 700;
  }
}

.vtable-wrapper {
  width: 100%;
  min-height: 200px;
  // min-height: 400px;
  overflow: auto;
}

/* 图表标题和操作区域样式 */
.chart-title {
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border-bottom: 1px solid #e6e6e6;
  background-color: #f5f7fa;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-title-inner {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chart-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-actions .el-icon {
  font-size: 16px;
  cursor: pointer;
  color: #606266;
}

.chart-actions .el-icon:hover {
  color: #409EFF;
}

.rotate-icon-90 {
  display: inline-flex;
  transform: rotate(90deg);
}

.chart-settings-dialog {
  overflow: hidden;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 聊天输入区域样式 */
.chat-input-area {
  border-top: 1px solid #e6e6e6;
  padding: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  margin: 0 30px 10px 15px;
  position: relative;
  z-index: 10;
}

.stream-component-container {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
}

.select-area {
  width: 100%;
  max-width: 126px;
  display: flex;
  margin-bottom: 12px;
  gap: 1px;
}

.select-box {
  width: 100%;
  max-width: 100px;
}



/* 模型选择区域样式 */
.model-select-area {
  margin-top: 8px;
  display: flex;
  align-items: center;
  width: fit-content;
  border: none;
}

.hint-text {
  color: #909399;
  font-size: 12px;
  margin-left: auto;
  white-space: nowrap;
}

.input-area {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-end;
  // background-color: #f5f7fa;
  border-radius: 6px;
  // padding: 8px 12px;
}

.input-area .el-textarea {
  flex: 1;
  margin-right: 12px;
}

.input-area .el-textarea :deep(.el-textarea__inner) {
  background-color: transparent;
  border: none;
  box-shadow: none;
  padding: 0;
}

.send-button {
  font-size: 28px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 0 16px;
  cursor: pointer;
}
</style>
<style lang="scss">
pre code {
  font-size: 12px;
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace, "SF Pro Display", "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", "Apple Color Emoji", "Twemoji Mozilla", "Noto Color Emoji", "Android Emoji" !important;
}

/* 代码高亮样式 */
pre {
  background-color: #F0F0F0;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  position: relative;
}

code {
  font-size: 12px;
  border-radius: 0.25em;
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace, "SF Pro Display", "PingFang SC", "Helvetica Neue", Helvetica, Arial, sans-serif, "Segoe UI Emoji", "Segoe UI Symbol", "Apple Color Emoji", "Twemoji Mozilla", "Noto Color Emoji", "Android Emoji" !important;
}
</style>
