export const dictionaryFormSchema: any[] = [
  {
    field: 'parentName',
    component: 'Input',
    label: '上级字典名称',
    componentProps: { disabled: true },
    colProps: {
      span: 24,
    },
    ifShow: false,
  },
  {
    field: 'parentCode',
    component: 'Input',
    componentProps: { disabled: true },
    label: '上级字典编码',
    colProps: {
      span: 24,
    },
    ifShow: false,
  },
  {
    field: 'name',
    component: 'Input',
    label: '字典名称',
    colProps: {
      span: 24,
    },
    required: true,
    rules: [{ max: 100, message: '超过长度限制，最多100字', trigger: 'blur' }],
  },
  {
    field: 'code',
    component: 'Input',
    label: '字典编码',
    colProps: {
      span: 24,
    },
    required: true,
    rules: [{ max: 100, message: '超过长度限制，最多100字', trigger: 'blur' }],
  },
  {
    field: 'val',
    component: 'Input',
    label: '字典取值',
    colProps: {
      span: 24,
    },
    required: true,
    rules: [{ max: 200, message: '超过长度限制，最多200字', trigger: 'blur' }],
  },
  {
    field: 'display',
    component: 'RadioGroup',
    label: '状态',
    colProps: {
      span: 24,
    },
    required: true,
    componentProps: {
      options: [
        {
          label: '启用',
          value: '1',
        },
        {
          label: '禁用',
          value: '0',
        },
      ],
    },
  },
  {
    field: 'description',
    component: 'Input',
    label: '描述',
    colProps: {
      span: 24,
    },
    rules: [{ max: 100, message: '超过长度限制，最多100字', trigger: 'blur' }],
  },
  {
    field: 'remark',
    component: 'Input',
    label: '备注',
    colProps: {
      span: 24,
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字', trigger: 'blur' }],
  },
];
