<template>
  <div class="content">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane min-size="30" size="30">
        <div class="left">
          <div class="top-button">
            <el-button
              type="primary"
              @click="addNode"
              :disabled="addDisabled || formData?.display === 0"
              v-auth="['project_menu_add']"
              :icon="Plus"
              >新增</el-button
            >
            <el-button
              type="primary"
              @click="downLoad"
              v-auth="['project_menu_export']"
              :icon="Download"
              plain
              >导出</el-button
            >
            <el-button
              type="primary"
              @click="handlefileVisible"
              v-auth="['project_menu_import']"
              :icon="Upload"
              plain
              >导入</el-button
            >
            <el-button
              type="danger"
              @click="deleteNode"
              v-auth="['project_menu_delete']"
              :icon="Delete"
              plain
              >删除</el-button
            >
          </div>
          <div class="tree-content">
            <div class="tree-input">
              <el-input placeholder="请输入菜单名称" clearable v-model="value" />
            </div>
            <div class="tree-div">
              <z-tree
                :setting="setting"
                :nodes="treeDate"
                class="baseTree"
                :name="value"
                @onCreated="handleCreated"
              />
            </div> </div
        ></div>
      </Pane>
      <Pane class="mid" ref="mid">
        <!--右侧div内容-->
        <div class="right">
          <p class="title"> 菜单信息 </p>
          <div class="form-div" v-if="formData.id || status === 'add'">
            <basic-form
              class="basic-form-div"
              labelWidth="140px"
              :form-list="menuFormSchema"
              :form-data="formData"
              ref="formRef"
              :showSubmit="false"
              :check-strictly="false"
              :isCreate="isCreate"
            >
              <template #icon="props">
                <IconPicker @select="selectIcon" :name="props.value" />
              </template>
              <template #meta>
                <Config ref="metaConfig" :meteData="meteData" />
              </template>
            </basic-form>

            <!-- instCode -->
            <div class="bottom-btn-div">
              <el-button
                type="primary"
                :loading="loading"
                @click="saveForm"
                v-auth="['project_menu_save']"
                >保存</el-button
              >
            </div>
          </div>
          <div v-else class="no-form"> 未选择任何菜单 </div>
        </div>
      </Pane>
    </Splitpanes>

    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
    >
      <el-button type="primary" link @click="downLoad" style="margin-bottom: 12px"
        >下载项目菜单导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, markRaw } from 'vue';
  import ZTree from '@cpit-cpvf/tree';
  import '@cpit-cpvf/tree/dist/style.css';
  import type { FormInstance } from 'element-plus';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, Download, Upload, Delete } from '@element-plus/icons-vue';
  import { Splitpanes, Pane } from 'splitpanes';
  import Config from '/@/views/upms/pages/menu-list/config.vue';
  import 'splitpanes/dist/splitpanes.css';
  import { IgetMenuByIdSpace } from '/@/views/upms/api/model/menuModel';
  import {
    getUserMenu,
    getMenuDetailById,
    addOrEditMenuDetail,
    deleteMenu,
    downLoadApi,
    uploadApi,
    getSortUrlApi,
  } from '/@/views/upms/api/menu';
  import useZtree from '/@/hooks/upms/dictTree/useZtree';
  import { menuFormSchema } from './menu.data';
  import { CircleClose } from '@element-plus/icons-vue';
  import { MicroAppList } from '/@/views/upms/api/microApp';
  import { MetaConfigData } from '/@/views/upms/api/model/menuModel';
  import { ListParams, ListResultModel } from '/@/views/upms/api/model/microAppModel';
  import useUpload from '/@/hooks/upms/upload/useUpload';
  import type { UploadInstance } from 'element-plus';
  import { getAuthStorage } from '/@/utils/storage/auth';
  const userInfo = getAuthStorage();
  const upload = ref<UploadInstance>();
  interface MicroOptions {
    value: string;
    label: string;
    path: string;
  }
  const formRef = ref<FormInstance | null>(null);
  const {
    setting,
    handleCreated,
    value,
    ztree,
    nodeId,
    nodeStatus,
    sortParams,
    addDisabled,
  } = useZtree();
  const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const isCreate = ref(false);
  const treeDate = ref<any>([]);
  const formData = ref<IgetMenuByIdSpace.Data>({
    component: '',
    createdBy: '',
    createdDate: '',
    creatorName: '',
    delFlag: '',
    description: '',
    icon: '',
    id: '',
    keepAlive: '',
    lastModifiedBy: '',
    lastModifiedDate: '',
    lastModifierName: '',
    leaf: true,
    name: '',
    pageNum: 0,
    pageSize: 0,
    parentId: '',
    parentIds: '',
    parentName: '',
    path: '',
    permission: '',
    display: 1,
    sort: 0,
    type: '0',
    version: 0,
    meta: '',
    microId: '',
    hideMenu: '0',
  });
  const loading = ref(false);
  const uploadLoading = ref(false);
  const parentId = ref('');
  const status = ref('');
  const newNode = ref<any>({});
  let meteData = ref<MetaConfigData[]>([
    {
      key: '',
      value: '',
      index: 0,
    },
  ]);
  const metaConfig = ref();
  const microData = ref<MicroOptions[]>([]);
  // const addDisabled = ref(false);
  let icon = ref('');
  const getMenuListData = async () => {
    const res = await getUserMenu({ opType: 'project' });
    console.log(res);
    treeDate.value = res;
  };

  const addNode = () => {
    if (!!userInfo.projectId) {
      const nodes =
        ztree.value && ztree.value.getSelectedNodes()
          ? ztree.value.getSelectedNodes()
          : [];
      console.log(nodes);

      status.value = 'add';
      newNode.value = {
        parentId: nodes.length > 0 ? nodes[0].id : '0',
        name: '未命名',
        id: '',
      };
      parentId.value = nodes.length > 0 ? nodes[0].id : '0';
      const newTreeNode =
        nodes.length > 0
          ? ztree.value.addNodes(nodes[0], [-1], newNode.value)
          : ztree.value.addNodes(null, [-1], newNode.value);

      ztree.value.selectNode(newTreeNode[0]);
      ztree.value.setting.callback.onClick(
        '',
        ztree.value.setting.treeId,
        newTreeNode[0],
      );
      isCreate.value = false;
      const ruleFormRef = formRef.value && formRef.value.ruleFormRef;
      if (ruleFormRef) {
        formRef.value?.resetForm(ruleFormRef);
      }
      meteData.value = [
        {
          key: '',
          value: '',
          index: 0,
        },
      ];
      addDisabled.value = true;
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择项目',
      });
    }
  };
  getMenuListData();
  function getMicroAppList(
    params: ListParams = { pageNum: 1, pageSize: 1000, status: '1' },
  ) {
    MicroAppList(params).then((res: ListResultModel) => {
      console.log(res);
      const data: MicroOptions[] = [];
      res.list.forEach((item) => {
        data.push({ label: item.name, value: item.id, path: item.path });
      });
      menuFormSchema.value.forEach((item) => {
        if (item.field === 'microId') {
          if (item.componentProps) {
            item.componentProps.options = data;
          }
        }
      });
      microData.value = data;
    });
  }
  getMicroAppList();
  const saveForm = () => {
    loading.value = true;
    const getData = formRef.value && formRef.value?.submitForm;
    const ruleFormRef = formRef.value && formRef.value?.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      // data.parentId = '0';
      data.parentId = parentId.value;
      data.opType = 'project';
      data.icon = icon.value;
      // console.log({ ...data, parentId: parentId.value });
      if (data.type == '2') {
        const { name, microPath, hideMenu, microId } = data;
        data.meta = JSON.stringify({
          title: name,
          icon,
          microPath,
          microId,
          metaConfig: metaConfig?.value?.tableData || [],
          // microName,
          // url,
          // microType,
          hideMenu,
        });
      } else {
        const { hideMenu } = data;
        data.meta = JSON.stringify({
          hideMenu,
          metaConfig: metaConfig?.value?.tableData || [],
        });
      }
      if (status === 'success') {
        addOrEditMenuDetail(data)
          .then((res) => {
            console.log(res);
            nodeId.value = res.id;
            formData.value.version = res.version;
            getMenuListData();
            ElMessage({
              type: 'success',
              showClose: true,
              message: formData.value.id ? '修改菜单成功' : '新增菜单成功',
            });
            addDisabled.value = false;
            status.value = '';
            // node.value = ztree.value && ztree.value.getNodesByParam('id', res, null)[0]; //根据id获取节点
            loading.value = false;
            nodeStatus.value = true;
            // useDict.reqDict();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const deleteNode = () => {
    if (nodeId.value && JSON.stringify(newNode.value) === '{}') {
      ElMessageBox.confirm('确认删除该菜单数据？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'error',
        icon: markRaw(CircleClose),
        customStyle: {
          backgroundColor: 'rgba(255, 241, 240, 1)',
          border: '1px soild rgba(255, 163, 158, 1)',
        },
      })
        .then(() => {
          deleteMenu({
            ids: nodeId.value,
            opType: 'system',
          }).then(() => {
            ElMessage({
              message: '删除成功',
              grouping: true,
              type: 'success',
            });
            getMenuListData();
            // useDict.reqDict();
            nodeId.value = '';
          });
        })
        .catch(() => {});
    } else if (!nodeId.value && JSON.stringify(newNode.value) === '{}') {
      ElMessage({
        type: 'warning',
        showClose: true,
        message: '请先选择菜单节点在进行删除操作！',
      });
    } else {
      const node = ztree.value.getSelectedNodes();
      ztree.value.removeNode(node[0]);
      getMenuListData();
      addDisabled.value = false;
      newNode.value = {};
      nodeId.value = '';
    }
  };
  function selectIcon(val) {
    icon.value = val;
  }
  const downLoad = (isTemplate) => {
    if (!!userInfo.projectId) {
      downLoadApi({
        opType: 'project',
        isTemplate,
        targetId: isTemplate ? '' : formData.value.id,
      }).then((res) => {
        console.log('res?.request?.responseURL', res);
        // window.location.href = res?.request?.responseURL;
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
        });
        const fileName = '项目菜单管理' + '.xlsx';
        const elink = document.createElement('a'); // 创建a标签
        elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
        elink.style.display = 'none';
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click(); // 点击下载
        console.log(elink.href);
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink); // 释放标签
      });
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择项目',
      });
    }
  };
  const handlefileVisible = () => {
    if (!!userInfo.projectId) {
      fileVisible.value = true;
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择项目',
      });
    }
  };
  const uploadFile = () => {
    uploadLoading.value = true;
    let formData = new FormData();
    formData.append('file', fileData.value?.raw);
    formData.append('opType', 'project');
    // formData.append('code', props.formBaseCode);
    uploadApi(formData)
      .then(() => {
        ElMessage({
          type: 'success',
          message: '导入成功',
        });
        fileVisible.value = false;
        getMenuListData();
        // reload();
        uploadLoading.value = false;
      })
      .catch(() => {
        uploadLoading.value = false;
      });
  };
  watch(
    () => {
      formData.value.microId;
    },
    () => {
      console.log(11, formData.value.microId);
      microData.value.forEach((item) => {
        if (item.value === formData.value.microId) {
          formData.value.path = item.path;
        }
      });
    },
    {
      deep: true,
    },
  );
  watch(
    () => nodeId.value,
    (val) => {
      if (val) {
        getMenuDetailById(val).then((res) => {
          const detail = res;
          // formData.value = detail as unknown as NodeItem;
          icon.value = detail?.icon || '';
          parentId.value = res.parentId;
          nodeStatus.value = false;
          status.value = '';
          const meta = transformResMeta(detail);
          if (detail.type != '1' && detail.type != '3') {
            formData.value = {
              ...detail,
              ...meta,
              hideMenu: meta.hideMenu,
            };
          } else {
            formData.value = {
              ...detail,
              hideMenu: meta.hideMenu,
            };
          }
        });
      } else {
        formData.value = {
          component: '',
          createdBy: '',
          createdDate: '',
          creatorName: '',
          delFlag: '',
          description: '',
          icon: '',
          id: '',
          keepAlive: '',
          lastModifiedBy: '',
          lastModifiedDate: '',
          lastModifierName: '',
          leaf: true,
          name: '',
          pageNum: 0,
          pageSize: 0,
          parentId: '',
          parentIds: '',
          parentName: '',
          path: '',
          permission: '',
          display: 1,
          sort: 0,
          type: '0',
          version: 0,
          meta: '',
          microId: '',
          hideMenu: '0',
        };

        meteData.value = [
          {
            key: '',
            value: '',
            index: 0,
          },
        ];
      }
    },
    // {
    //   immediate: true,
    //   deep: true,
    // },
  );
  // 将后端返回的数据转换为 指定格式
  function transformResMeta(data) {
    if (!data) {
      meteData.value = [
        {
          key: '',
          value: '',
          index: 0,
        },
      ];
      return {};
    }
    const meta = JSON.parse(data.meta);
    meteData.value = meta.metaConfig || [
      {
        key: '',
        value: '',
        index: 0,
      },
    ];
    return meta;
  }
  watch(
    () => sortParams.value,
    (val) => {
      if (val.ifLevel) {
        getSortUrlApi(val).then(() => {
          getMenuListData();
          nodeId.value = '';
        });
      } else {
        ElMessage({
          showClose: true,
          type: 'warning',
          message: '只允许在同一个父菜单节点下排序！',
        });
      }
    },
  );
</script>

<style scoped lang="scss">
  /* 拖拽相关样式 */
  /*包围div样式*/

  .content {
    display: flex;
    height: calc(100vh - 94px);
    /* height: 500px; */
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  }
  /*左侧div样式*/
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    // float: left;
    // overflow-y: auto;
    // overflow-x: hidden;
    border-radius: 6px;
    .tree-content {
      // padding: 12px;
      height: calc(100% - 60px);
      // overflow-y: auto;
      // overflow-x: hidden;
    }
    .tree-input {
      /* height: 40px; */
      /* margin: 8px 0 8px 12px; */
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        // margin: 8px 12px;
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 48px;
      font-size: 18px;
      padding-left: 18px;
      background: #ffffff;
      font-weight: 700;
    }
  }
  // .left-border {
  //   padding: 12px;
  //   /* border-radius: 30px; */
  //   .top-button {
  //     .el-button {
  //       margin: 8px 12px 0;
  //     }
  //   }
  // }
  .top-button {
    padding: 8px 18px 8px 18px;
    border-bottom: 1px solid #e4e7ed;
    .upload-btn {
      display: inline-block;
      margin: 0 12px;
    }
  }
  /*拖拽区div样式*/
  .resize {
    cursor: col-resize;
    float: left;
    position: relative;
    top: 45%;
    background-color: #d6d6d6;
    border-radius: 5px;
    margin-top: -10px;
    width: 10px;
    height: 50px;
    background-size: cover;
    background-position: center;
    /*z-index: 99999;*/
    font-size: 32px;
    color: white;
  }
  /*拖拽区鼠标悬停样式*/
  .resize:hover {
    color: #444444;
  }
  /*右侧div'样式*/
  .mid {
    float: left;
    width: 55%; /*右侧初始化宽度*/
    height: 100%;
    background: #fff;
    box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
  }
  .dept-tabs-xl {
    padding-top: 9px;
    padding-left: 1px;
    width: 100%;
    height: 100%;
    // color:#d1e237
    ::deep(.el-tabs__content) {
      height: calc(100% - 60px) !important;
    }
  }
  .table-content {
    background: #ffffff;
    margin-top: 20px;
    /* padding-top: 18px; */
  }

  .base-table-all {
    margin: 0 12px;
  }
  .inst-code {
    color: var(--el-color-primary);
    cursor: pointer;
  }
  .form-div {
    width: 100%;
    height: 100%;
    .basic-form-div {
      height: calc(100% - 110px);
      border-bottom: 1px solid #e4e7ed;
      overflow: auto;
    }
    .bottom-btn-div {
      display: flex;
      justify-content: flex-end;
      // justify-content: end;
      padding-right: 18px;
      padding-top: 14px;
    }
  }
  .no-form {
    width: 100%;
    height: 100%;
    line-height: 100%;
    padding-top: 120px;
    font-size: 24px;
    text-align: center;
  }
  // ::deep(.el-tabs--card > .el-tabs__header) {
  //   background: #ffffff;
  // }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }
  /* .dept-tabs-xl .el-tabs__header {
    background: #ffffff;
  } */
</style>
