<!-- eslint-disable vue/no-mutating-props -->
<template>
  <ul>
    <li v-for="item in descriptionLabel" :key="item.field">
      <span class="title">{{ item.label }}</span
      ><span
        class="content"
        v-if="item.field === 'methodParam' || item.field === 'methodResult'"
      >
        <JsonEditorVue
          class="editor"
          v-model="record[item.field]"
          mode="text"
          :readOnly="true"
          :mainMenuBar="false"
          :expanded-on-start="true"
      /></span>
      <span class="content" v-else>{{ record[item.field] }}</span>
    </li>
  </ul>
</template>
<script lang="ts" setup name="ViewOperateLog">
  import { PropType } from 'vue';
  import { descriptionLabel } from './log.data';
  import JsonEditorVue from 'json-editor-vue';
  interface recordType {
    id: number;
    title: string;
    operateType: string;
    operatorType: string;
    username: string;
    deptName: string;
    ipAddress: string;
    createdDate: string;
    browser: string;
    os: string;
    method: string;
    requestUri: string;
    params: string;
    userAgent: string;
    time: number;
  }
  defineProps({
    record: {
      type: Object as PropType<recordType>,
      default: () => {},
    },
    descriptionLabel,
  });
</script>
<style scoped>
  li {
    width: 100%;
    display: flex;
  }

  .title {
    width: 150px;
    background: #f5f7fa;
    font-weight: 700;
    color: #606266;
    text-align: right;
  }

  span {
    border: 1px solid #ebeef5;
    padding: 8px 11px;
  }

  .content {
    flex: 1;
  }
</style>
