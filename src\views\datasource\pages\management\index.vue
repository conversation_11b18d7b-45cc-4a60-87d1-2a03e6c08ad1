<template>
  <div class="agent-container">
    <div class="agent-header"> 数据源配置 </div>
    <div class="main">
      <div class="sider">
        <div class="sider-title"> 连接 </div>
        <div class="sider-center">
          <p
            v-for="(item, index) in dataSourceLists"
            :key="item.id"
            @click="selectItem(index)"
            :class="{ active: selectedIndex === index }"
          >
            <span
              class="annotation"
              :style="{ background: item.environment.color.toLocaleLowerCase() }"
            >
            </span>
            <Iconfont
              style="margin-right: 6px"
              :name="getIconByLabel(item.type.toLowerCase())"
              :color="item.environment.color.toLocaleLowerCase()"
            />
            <span>{{ item.alias }}</span>
          </p>
        </div>
        <div class="sider-bottom">
          <el-button :icon="Plus">添加</el-button>
        </div>
      </div>
      <div class="content">
        <el-row :gutter="20">
          <el-col
            v-for="(item, index) in databaseTypeList"
            :key="index"
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
          >
            <div class="grid-content">
              <!-- 你的内容 -->
              <!-- {{ item }} -->

              <div class="dataBaseList">
                <div class="databaseItem">
                  <div class="databaseItemMain">
                    <div class="databaseItemLeft"
                      ><Iconfont style="margin-right: 8px" :name="item.icon" />
                      {{ item.label }}
                    </div>
                    <div class="databaseItemRight">
                      <el-icon><Plus /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { Plus } from '@element-plus/icons-vue';
  //   import { cloneDeep } from 'lodash-es';  InfoFilled
  import '/@/assets/font/iconfont.css'; // 本地文件
  import Iconfont from '/@/components/Iconfont.vue';
  const dataSourceLists = ref([
    {
      id: 5,
      alias: '可用',
      url: '***************************/',
      user: 'root',
      password: '123456',
      authenticationType: null,
      type: 'MYSQL',
      envType: null,
      host: 'localhost',
      port: '3306',
      ssh: {
        use: false,
        hostName: '',
        port: '22',
        userName: 'xlnocodb',
        localPort: '',
        authenticationType: 'password',
        password: 'xlnocodb',
        keyFile: null,
        passphrase: null,
        rhost: null,
        rport: null,
      },
      sid: null,
      driver: null,
      jdbc: null,
      extendInfo: [
        {
          key: 'zeroDateTimeBehavior',
          value: 'convertToNull',
          required: false,
          choices: null,
        },
        {
          key: 'useInformationSchema',
          value: 'true',
          required: false,
          choices: null,
        },
        {
          key: 'tinyInt1isBit',
          value: 'false',
          required: false,
          choices: null,
        },
      ],
      driverConfig: {
        url: null,
        jdbcDriver: 'mysql-connector-java-8.0.30.jar',
        jdbcDriverClass: 'com.mysql.cj.jdbc.Driver',
        downloadJdbcDriverUrls: null,
        dbType: null,
        custom: false,
        extendInfo: null,
        defaultDriver: false,
      },
      environmentId: 2,
      environment: {
        id: 2,
        name: 'Test Environment',
        shortName: 'TEST',
        color: 'GREEN',
      },
      kind: 'PRIVATE',
      serviceName: null,
      serviceType: null,
      supportDatabase: true,
      supportSchema: false,
    },
    {
      id: 6,
      alias: '@localhost',
      url: '***************************/',
      user: 'root',
      password: '123456',
      authenticationType: null,
      type: 'MYSQL',
      envType: null,
      host: 'localhost',
      port: '3306',
      ssh: {
        use: false,
        hostName: '',
        port: '22',
        userName: '',
        localPort: '',
        authenticationType: 'password',
        password: '',
        keyFile: null,
        passphrase: null,
        rhost: null,
        rport: null,
      },
      sid: null,
      driver: null,
      jdbc: null,
      extendInfo: [
        {
          key: 'zeroDateTimeBehavior',
          value: 'convertToNull',
          required: false,
          choices: null,
        },
        {
          key: 'useInformationSchema',
          value: 'true',
          required: false,
          choices: null,
        },
        {
          key: 'tinyInt1isBit',
          value: 'false',
          required: false,
          choices: null,
        },
      ],
      driverConfig: {
        url: null,
        jdbcDriver: 'mysql-connector-java-8.0.30.jar',
        jdbcDriverClass: 'com.mysql.cj.jdbc.Driver',
        downloadJdbcDriverUrls: null,
        dbType: null,
        custom: false,
        extendInfo: null,
        defaultDriver: false,
      },
      environmentId: 1,
      environment: {
        id: 1,
        name: 'Release Environment',
        shortName: 'RELEASE',
        color: 'RED',
      },
      kind: 'PRIVATE',
      serviceName: null,
      serviceType: null,
      supportDatabase: true,
      supportSchema: false,
    },
    {
      id: 7,
      alias: '@localhostCopy',
      url: '***************************',
      user: 'root',
      password: '',
      authenticationType: null,
      type: 'MYSQL',
      envType: null,
      host: 'localhost',
      port: '3306',
      ssh: {
        use: false,
        hostName: '',
        port: '22',
        userName: 'xlnocodb',
        localPort: '',
        authenticationType: 'password',
        password: 'xlnocodb',
        keyFile: null,
        passphrase: null,
        rhost: null,
        rport: null,
      },
      sid: null,
      driver: null,
      jdbc: null,
      extendInfo: [
        {
          key: 'zeroDateTimeBehavior',
          value: 'convertToNull',
          required: false,
          choices: null,
        },
        {
          key: 'useInformationSchema',
          value: 'true',
          required: false,
          choices: null,
        },
        {
          key: 'tinyInt1isBit',
          value: 'false',
          required: false,
          choices: null,
        },
      ],
      driverConfig: {
        url: null,
        jdbcDriver: null,
        jdbcDriverClass: null,
        downloadJdbcDriverUrls: null,
        dbType: null,
        custom: false,
        extendInfo: null,
        defaultDriver: false,
      },
      environmentId: 1,
      environment: {
        id: 1,
        name: 'Release Environment',
        shortName: 'RELEASE',
        color: 'RED',
      },
      kind: 'PRIVATE',
      serviceName: null,
      serviceType: null,
      supportDatabase: true,
      supportSchema: false,
    },
    {
      id: 8,
      alias: '@localhost',
      url: '***************************',
      user: 'root',
      password: '',
      authenticationType: null,
      type: 'MYSQL',
      envType: null,
      host: 'localhost',
      port: '3306',
      ssh: {
        use: false,
        hostName: '',
        port: '22',
        userName: '',
        localPort: '',
        authenticationType: 'password',
        password: '',
        keyFile: null,
        passphrase: null,
        rhost: null,
        rport: null,
      },
      sid: null,
      driver: null,
      jdbc: null,
      extendInfo: [
        {
          key: 'zeroDateTimeBehavior',
          value: 'convertToNull',
          required: false,
          choices: null,
        },
        {
          key: 'useInformationSchema',
          value: 'true',
          required: false,
          choices: null,
        },
        {
          key: 'tinyInt1isBit',
          value: 'false',
          required: false,
          choices: null,
        },
      ],
      driverConfig: {
        url: null,
        jdbcDriver: 'mysql-connector-java-8.0.30.jar',
        jdbcDriverClass: 'com.mysql.cj.jdbc.Driver',
        downloadJdbcDriverUrls: null,
        dbType: null,
        custom: false,
        extendInfo: null,
        defaultDriver: false,
      },
      environmentId: 1,
      environment: {
        id: 1,
        name: 'Release Environment',
        shortName: 'RELEASE',
        color: 'RED',
      },
      kind: 'PRIVATE',
      serviceName: null,
      serviceType: null,
      supportDatabase: true,
      supportSchema: false,
    },
  ]);
  const selectedIndex = ref(0);

  const databaseTypeList = ref([
    {
      label: 'MySQL',
      value: 'mysql',
      icon: 'mysql',
    },
    {
      label: 'H2',
      value: 'h2',
      icon: 'h2',
    },
    {
      label: 'Oracle',
      value: 'oracle',
      icon: 'oracle',
    },
    {
      label: 'PostgreSql',
      value: 'postgresql',
      icon: 'postgresql',
    },
    {
      label: 'SQLServer',
      icon: 'sqlserver',
      value: 'sqlserver',
    },
    {
      label: 'SQLite',
      icon: 'sqlite',
      value: 'sqlite',
    },
    {
      label: 'Mariadb',
      value: 'mariadb',
      icon: 'rds_mariadb',
    },
    {
      label: 'ClickHouse',
      value: 'clickhouse',
      icon: 'clickhouse-yunshujukuClickHouse',
    },
    {
      label: 'DM',
      value: 'dm',
      icon: 'dameng1',
    },
    {
      label: 'Presto',
      value: 'presto',
      icon: 'presto_sql',
    },
    {
      label: 'DB2',
      value: 'db2',
      icon: 'shujukuleixingtubiao-kuozhan-',
    },
    {
      label: 'OceanBase',
      icon: 'oceanbase',
      value: 'oceanbase',
    },
    {
      label: 'Hive',
      icon: 'HIVE',
      value: 'hive',
    },
    {
      label: 'KingBase',
      icon: 'Kingbase',
      value: 'kingbase',
    },
    {
      label: 'MongoDB',
      icon: 'mongodb',
      value: 'mongodb',
    },

    {
      label: 'Timeplus',
      value: 'timeplus',
      icon: 'clickhouse-yunshujukuClickHouse',
    },
  ]);

  const getIconByLabel = (label: string): string | undefined => {
    const foundItem = databaseTypeList.value.find((item) => item.value === label);
    return foundItem ? foundItem.icon : undefined;
  };
  //   const items = [1, 2, 3, 4, 5, 6, 7, 8, 9]; // 示例数据
  const selectItem = (index) => {
    selectedIndex.value = index; // 更新选中的索引
  };
</script>

<style scoped lang="scss">
  .agent-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .agent-header {
      border-bottom: 1px solid #e6e8ee;
      background: #fff;
      background-color: #ffffff;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      padding: 16px 20px;
      display: flex;
      position: relative;
    }
    .main {
      display: flex;
      flex: 1;
      .sider {
        flex: 0 0 320px;
        border-right: 1px solid #e6e8ee;
        padding: 12px;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 120px);
        .sider-title {
          line-height: 36px;
          margin-bottom: 12px;
          font-size: 16px;
          height: 36px;
        }
        .sider-center {
          flex: 1;
          overflow-y: auto;
          p.active {
            background: rgba(205, 208, 220, 0.8);
          }
          p {
            padding: 10px;
            cursor: pointer;
            border-radius: 4px;
            // display: flex;
            // justify-content: space-between;
            .el-icon {
              font-size: 16px;
              margin-top: 3px;
            }
            .annotation {
              width: 9px;
              height: 9px;
              border-radius: 50%;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              vertical-align: middle;
              margin-right: 6px;
            }
          }
        }
        .sider-bottom {
          height: 30px;
          .el-button {
            width: 100%;
          }
        }
      }
      .content {
        flex: 1;
        overflow: auto;
        padding: 24px;
        height: calc(100vh - 120px);

        .model-p {
          margin-bottom: 12px;
        }
        .el-row {
          //   display: flex;
          //   justify-content: center;
          //   align-items: center;
          //   display: flex;
          //   flex-direction: column; /* 垂直排列 */
          margin-top: 80px;
          .dataBaseList {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            max-width: 800px;
            .databaseItem {
              flex-grow: 1;
              height: 50px;
              width: 210px;
              margin: 10px 20px;
              padding: 0px 16px;
              border-radius: 8px;
              overflow: hidden;
              box-sizing: border-box;
              border: 1px solid rgba(211, 211, 212, 0.4);

              .databaseItemMain {
                display: flex;
                justify-content: space-between;
                align-items: center;
                height: 50px;
                border-radius: 8px;
                .databaseItemLeft {
                  display: flex;
                  align-items: center;
                }
                // .databaseItemRight {
                // }

                .databaseItemRight {
                  display: none;

                  i {
                    font-size: 16px;
                  }
                }
              }
              &:hover {
                // background-color: var(--color-bg-medium);
                color: #409eff;
                border: 1px solid #409eff;
                cursor: pointer;

                .databaseItemRight {
                  display: block;

                  i {
                    color: var(--color-primary);
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .grid-content {
    border-radius: 4px;
    min-height: 36px;
  }
  .ep-bg-purple-light {
    background: #e5e9f2;
  }
  .ep-bg-purple {
    background: #d3dce6;
  }
</style>
