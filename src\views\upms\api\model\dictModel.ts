// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace IPostDictSpace {
  export class RequestParams {
    code: string | undefined;
    name: string | undefined;
    parentId: string | undefined | null;
    show: number | undefined;
    sort: number | undefined;
    val: string | undefined;
    opType: string | undefined;
    constructor() {
      this.code = '';
      this.name = '';
      this.parentId = '';
      this.show = 0;
      this.sort = 0;
      this.val = '';
      this.opType = '';
    }
  }
}

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace IgetDictTreeSpace {
  export interface children {
    id: string;
    label: string;
    parentId: string;
  }
  export interface Data {
    children: children[];
    id: string;
    label: string;
    parentId: string;
  }
}

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace IgetDictByIdSpace {
  export interface Data {
    code: string;
    createdBy: string;
    createdDate: string;
    creatorName: string;
    delFlag: string;
    description: string | null;
    id: string;
    lastModifiedBy: string;
    lastModifiedDate: string;
    lastModifierName: string;
    leaf: boolean;
    name: string;
    pageNum: number;
    pageSize: number;
    parentCode: string | null;
    parentId: string;
    parentIds: string | null;
    parentName: string | null;
    remark: string | null;
    show: number;
    showText: string;
    sort: number;
    val: string;
    version: number;
  }
}

export interface FormHeaderType {
  tenantId?: string;
  projectId?: string;
}

export interface getDictTreeParams {
  opType?: string;
}

/**
 * 字典树单向
 */
export interface DictItem {
  id?: string;
  parentName?: string;
  parentCode?: string;
  name?: string;
  code?: string;
  sort?: string;
  show?: string;
  description?: string;
  remark?: string;
}
