-<template>
  <div>
    <div class="top-checkbox">
      <span>数据范围</span>
      <el-select v-model="dataType" placeholder="请选择" @change="dataScopeChange">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="tree-content">
      <VTree
        ref="tree"
        :checkable="true"
        v-show="dataType === '5'"
        v-model="deptIdList"
        titleField="instName"
        :load="getDeptData"
        @click="handleTreeClick"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, PropType } from 'vue';
  import VTree from '@wsfe/vue-tree';
  import { deptAsyn } from '/@/views/upms/api/dept';
  const tree = ref<InstanceType<typeof VTree> | null>(null);
  const emits = defineEmits(['dataIdsAndType']);
  const props = defineProps({
    deptIds: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    dataScope: {
      type: String,
      default: () => '',
    },
  });
  const options = [
    {
      value: '1',
      label: '全部',
    },
    {
      value: '2',
      label: '所在机构及以下数据',
    },
    {
      value: '3',
      label: '所在机构数据',
    },
    {
      value: '4',
      label: '仅本人数据',
    },
    {
      value: '5',
      label: '按明细分配',
    },
  ];
  const dataType = ref<string>('');
  const deptIdList = ref<string[]>([]);
  const nodeId = ref('');
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
    }).then((res) => {
      console.log('resolve', resolve, res);
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      // currentIsLeaf.value = e.isLeaf;
    }
  };

  const getData = () => {
    console.log('deptIdList-tenant', deptIdList.value);
    const data = {
      ids: deptIdList.value,
      dataScope: dataType.value,
    };
    emits('dataIdsAndType', data);
  };

  const dataScopeChange = (data) => {
    dataType.value = data;
  };

  watch(
    () => props.deptIds,
    (value: string[]) => {
      deptIdList.value = value || [];
    },
  );
  watch(
    () => props.dataScope,
    (value: string) => {
      if (!!value) {
        dataType.value = value;
      }
    },
    {
      immediate: true,
    },
  );

  defineExpose({ getData });
</script>

<style scoped lang="scss">
  .top-checkbox {
    margin-bottom: 10px;
    margin-left: 20px;
    span {
      margin-right: 20px;
    }
  }
  .tree-content {
    height: 100vh;
  }
</style>
