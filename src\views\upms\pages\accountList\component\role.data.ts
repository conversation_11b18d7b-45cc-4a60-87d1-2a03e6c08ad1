import { TableOptions } from '/@/components/sys/BasicTable/types';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
  },
  {
    label: '角色编码',
    prop: 'code',
    align: 'center',
  },
  {
    label: '角色名称',
    prop: 'name',
    ellipsis: true,
    align: 'center',
  },
  {
    label: '类型',
    prop: 'builtIn',
    slot: 'builtIn',
    width: 100,
    align: 'center',
  },
];
export const searchFormSchema: SearchOptions[] = [
  {
    field: 'code',
    label: '角色编码',
    component: 'Input',
    span: 6,
    placeholder: '请输入角色编码',
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'name',
    label: '角色名称',
    component: 'Input',
    span: 6,
    placeholder: '请输入角色名称',
    componentProps: {
      clearable: true,
    },
  },
  // {
  //   field: 'available',
  //   label: '角色状态',
  //   component: 'Select',
  //   span: 6,
  //   placeholder: '请选择角色状态',
  //   componentProps: {
  //     clearable: true,
  //     options: [
  //       { label: '禁用', value: '0' },
  //       { label: '启用', value: '1' },
  //     ],
  //   },
  // },
];
