// import { BasicFetchResult } from '/@/api/model/baseModel';

export interface DeptItem {
  id: string;
  label: string;
  parentId: string;
  tenantId?: string;
  projectId?: string;
  parentInstCode?: string;
  instCode?: string;
  children: DeptItem[];
  isLeaf?: boolean;
  level?: number;
}
export interface DeptListParams {
  tenantId?: string;
  projectId?: string;
  isAll?: Boolean;
  opType?: string;
  parentInstCode?: string;
}

export interface DeptDetail {
  addr: string;
  contacts: string;
  createdBy: string;
  createdDate: string;
  creatorName: string;
  creditCode: string;
  delFlag: string;
  description: string;
  id: string;
  lastModifiedBy: string;
  lastModifiedDate: string;
  lastModifierName: string;
  leaf: boolean;
  deptName: string;
  pageNum: number;
  pageSize: number;
  parentId: string;
  parentIds: string;
  parentName: string;
  phone: string;
  sort: number;
  instStatus: string;
  telephone: string;
  version: number;
  tenantId?: string;
  projectId?: string;
  parentInstCode?: string;
}

export interface EditDetParams {
  addr?: string;
  contacts?: string;
  creditCode?: string;
  description?: string;
  id?: string;
  label: string;
  parentId: string;
  parentName?: string;
  phone?: string;
  instStatus?: string;
  telephone?: string;
  version?: string;
  instName?: string;
  deptName?: string;
}

/**
 * @description: Request list return value
 */
export type DeptResultModel = DeptItem[];
// 导出/模板导出请求参数
export interface DownLoadParmas {
  id: string;
  isTemplate: boolean;
}
