import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface NoticeItem {
  id: string;
  title: string;
  level: string;
  type?: string;
  content?: string;
  version?: string;
  description?: string;
  start_time: string;
  end_time: string;
  publisher?: string;
  status: string;
}
export type noticeListParams = BasicPageParams & {
  title?: string;
}
export type NoticeListResultModel = BasicFetchResult<NoticeItem> & {
  list: [];
  pageNum: number;
  pageSize: number;
  total: number;
};
export interface AddNoticeParams {
  id: string;
  title: string;
  level: string;
  type?: string;
  content?: string;
  version?: string;
  description?: string;
  start_time: string;
  end_time: string;
}