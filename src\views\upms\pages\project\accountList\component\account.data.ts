import { TableOptions } from '/@/components/sys/BasicTable/types';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
  },
  {
    label: '账号名',
    prop: 'userName',
    align: 'center',
    sortable: 'custom',
  },
  {
    label: '员工姓名',
    prop: 'employeeName',
    align: 'center',
  },
];

export const searchFormSchema: SearchOptions[] = [
  {
    field: 'userName',
    label: '账号名:',
    component: 'Input',
    placeholder: '请输入账号名',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'employeeName',
    label: '员工姓名',
    component: 'Input',
    placeholder: '请输入员工姓名',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'sub',
    label: '查看本机构及下属机构员工',
    componentProps: {
      defaultValue: '1',
    },
    span: 8,
    labelWidth: 50,
    slot: 'sub',
  },
];
