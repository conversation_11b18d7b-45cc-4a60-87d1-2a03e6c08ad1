import { getDictStorage } from '/@/utils/storage/dict';
import { TableOptions } from '/@/components/sys/BasicTable/types';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
export const columns: any[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
    selectable: function (row) {
      if (row.status == '2') {
        return false;
      } else {
        return true;
      }
    },
  },
  {
    label: '标题',
    prop: 'title',
    width: 100,
    align: 'center',
  },
  {
    label: '消息类型',
    prop: 'type',
    align: 'center',
    width: 100,
    slot: 'type',
  },
  {
    label: '发布人',
    prop: 'publisher',
    align: 'center',
  },
  {
    label: '优先级',
    prop: 'level',
    width: 80,
    align: 'center',
    slot: 'level',
  },
  {
    label: '通告对象',
    prop: 'postStationName',
    width: 100,
    align: 'center',
    slot: 'noticeTarget',
  },
  {
    label: '发布状态',
    prop: 'postStationCode',
    width: 100,
    align: 'center',
    slot: 'status',
  },
  {
    label: '发布时间',
    prop: 'publisherTime',
    width: 170,
    align: 'center',
  },
  {
    label: '撤销时间',
    prop: 'revokeTime',
    width: 170,
    align: 'center',
  },
  {
    label: '创建时间',
    prop: 'startTime',
    width: 140,
    align: 'center',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    width: 280,
    align: 'center',
  },
];

export const searchFormSchema: any[] = [
  {
    field: 'title',
    label: '标题:',
    component: 'Input',
    span: 8,
    labelWidth: 80,
    placeholder: '请输入标题',
    componentProps: {
      clearable: true,
    },
  },
];

export const formSchema: any[] = [
  {
    field: 'type',
    label: '消息类型',
    component: 'RadioGroup',
    componentProps: {
      options: getDictStorage()?.biz_notice_type
        ? getDictStorage().biz_notice_type
        : [],
      // options: [
      //   {
      //     label: '系统消息',
      //     value: 'A',
      //   },
      //   {
      //     label: '通知公告',
      //     value: 'B',
      //   },
      // ],
    },
    required: true,
  },
  {
    field: 'title',
    label: '标题',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标题',
    },
    required: true,
    rules: [{ max: 30, message: '超过长度限制，最多30字' }],
  },
  {
    field: 'description',
    label: '摘要',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入摘要',
    },
    required: true,
    rules: [{ max: 200, message: '超过长度限制，最多200字' }],
  },
  {
    field: 'noticeTarget',
    label: '接收用户',
    component: 'RadioGroup',
    slot: 'noticeTarget',
    // componentProps: {
    //     options: [
    //       {
    //         label: '全体员工',
    //         value: '0',
    //       },
    //       {
    //         label: '指定员工',
    //         value: '1',
    //       },
    //     ],
    //   },
    required: true,
  },
  {
    field: 'level',
    label: '优先级',
    component: 'RadioGroup',
    componentProps: {
      options: getDictStorage()?.biz_notice_level
        ? getDictStorage().biz_notice_level
        : [],
      // options: [
      //   {
      //     label: '高',
      //     value: 'A',
      //   },
      //   {
      //     label: '中',
      //     value: 'B',
      //   },
      //   {
      //     label: '低',
      //     value: 'B',
      //   },
      // ],
    },
    required: true,
  },
  {
    field: 'templateID',
    label: '消息模板',
    component: 'Slot',
    slot: 'template',
  },
  {
    field: 'time',
    label: '生效时间',
    component: 'Slot',
    slot: 'time',
    required: true,
  },
  {
    field: 'content',
    label: '内容',
    component: 'Slot',
    slot: 'content',
    required: true,
  },
];
export const accountColumns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
  },
  {
    label: '员工工号',
    prop: 'empCode',
    slot: 'empCode',
    width: 200,
    align: 'center',
    sortable: 'custom',
  },
  {
    label: '姓名',
    prop: 'empName',
    slot: 'empName',
    width: 100,
    align: 'center',
  },
  {
    label: '员工账号',
    prop: 'userName',
    width: 90,
    align: 'left',
  },
  {
    label: '机构名称',
    prop: 'deptName',
    minWidth: 120,
    align: 'center',
    hideColumn: () => {
      const sysUserDeptShow = getDictStorage().sys_user_dept
        ? getDictStorage().sys_user_dept[0].value
        : '0';
      return sysUserDeptShow == '0';
    },
  },
  {
    label: '邮箱',
    prop: 'email',
    slot: 'email',
    width: 210,
    align: 'center',
  },
  {
    label: '员工电话',
    prop: 'available',
    slot: 'phone',
    width: 120,
    align: 'center',
  },
];

export const accountSearchFormSchema: SearchOptions[] = [
  {
    field: 'empName',
    label: '姓名',
    component: 'Input',
    placeholder: '请输入姓名',
    span: 5,
    componentProps: {
      clearable: true,
    },
  },

  {
    field: 'phone',
    label: '员工电话',
    component: 'Input',
    placeholder: '请输入员工电话',
    span: 5,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    placeholder: '请输入邮箱',
    span: 5,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'userName',
    label: '用户名',
    component: 'Input',
    placeholder: '请输入用户名',
    span: 5,
    componentProps: {
      clearable: true,
    },
  },
];
export const viewFormSchema = [
  {
    name: '消息类型',
    field: 'type',
  },
  {
    name: '标题',
    field: 'title',
  },
  {
    name: '摘要',
    field: 'description',
  },
  {
    name: '发布人',
    field: 'publisher',
  },
  {
    name: '优先级',
    field: 'level',
  },
  {
    name: '通告对象',
    field: 'noticeTarget',
  },
  {
    name: '发布状态',
    field: 'status',
  },
  {
    name: '发布时间',
    field: 'publisherTime',
  },
  {
    name: '撤销时间',
    field: 'revokeTime',
  },
];
