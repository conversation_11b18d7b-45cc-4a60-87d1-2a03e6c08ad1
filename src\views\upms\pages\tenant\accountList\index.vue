<template>
  <div class="content">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane class="pane-left" size="20" min-size="20">
        <div class="left">
          <div class="tree-content">
            <div class="tree-div">
              <VTreeSearch
                ref="tree"
                selectable
                :load="getDeptData"
                @click="handleTreeClick"
              >
                <template #actions>
                  <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
                </template>
                <template #search-input>
                  <el-input
                    class="search-input"
                    placeholder="至少3个字才能触发自动搜索"
                    v-model="searchName"
                    @input="handleSearch(0)"
                  />
                </template>
                <template #node="{ node }">
                  <span
                    :class="node.id == nodeId ? 'active' : ''"
                    :style="{
                      color:
                        name && node.instName.indexOf(name) > -1
                          ? 'rgb(166, 0, 0)'
                          : '',
                      fontWeight:
                        name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                    }"
                    >{{ node.instName }}</span
                  >
                </template>
              </VTreeSearch>
            </div>
          </div></div
        >
      </Pane>
      <Pane class="mid" size="80">
        <!--右侧div内容-->
        <div class="right">
          <div class="search-div">
            <p class="title"> 租户账号搜索 </p>
            <basic-search
              :searchArray="searchFormSchema"
              class="search-all"
              ref="searchRef"
              :labelWidth="80"
              :labelShow="false"
              @reset="accountList"
              @onSearch="accountList"
            >
              <template #sub>
                <el-checkbox
                  v-model="sub"
                  label="查看本机构及下属机构账号"
                  @change="handleCheckBox"
                />
              </template>
            </basic-search>
          </div>
          <div class="table-content">
            <div class="table-top-div">
              <p>租户账号列表</p>
              <div class="top-button">
                <el-button
                  type="primary"
                  @click="handleAdd"
                  v-auth="['tenant_user_add']"
                  :icon="Plus"
                  >新增</el-button
                >
                <el-button
                  type="primary"
                  @click="downLoad(false)"
                  v-auth="['tenant_user_export']"
                  :icon="Download"
                  plain
                  >导出</el-button
                >
                <el-button
                  type="primary"
                  @click="handlefileVisible"
                  v-auth="['tenant_user_import']"
                  :icon="Upload"
                  plain
                  >导入</el-button
                >
                <el-button
                  type="primary"
                  @click="handleInviteUser"
                  v-auth="['tenant_member_add']"
                  :icon="Plus"
                  >邀请成员</el-button
                >
                <el-button
                  type="danger"
                  @click="batchDeletion"
                  :disabled="selectionIds.length > 0 ? false : true"
                  v-auth="['tenant_user_delete']"
                  :icon="Delete"
                  plain
                  >批量删除/移除</el-button
                >
              </div>
            </div>
            <basic-table
              ref="tableRef"
              :columns="columns"
              :data="tableData"
              :total="page.total"
              :page-size="page.pageSize"
              :current-page="page.pageNum"
              @page-change="pageChange"
              @size-change="sizeChange"
              @selectionChange="handleCurrentChange"
              @sortChange="sortChange"
              :downSetting="true"
              height="calc(100vh - 436px)"
            >
              <template #available="{ record }">
                <el-tag v-if="record.available === '1'">已激活</el-tag>
                <el-tag v-else-if="record.available === '0'" type="danger"
                  >已禁用</el-tag
                >
                <el-tag v-else type="info">已锁定</el-tag>
              </template>
              <template #roleIdList="{ record }">
                <div v-if="record.roleIdList.length > 0">
                  <el-tag
                    v-for="item in record.roleNames.split(',')"
                    :key="item"
                    style="margin-right: 5px; margin-bottom: 3px"
                    >{{ item }}</el-tag
                  >
                </div>
              </template>

              <template #opType="{ record }">
                <el-tag v-if="record.opType === 'tenant'">租户</el-tag>
                <el-tag v-else-if="record.opType === 'system'" type="success"
                  >平台</el-tag
                >
              </template>

              <template #action="{ record }">
                <el-button
                  type="primary"
                  link
                  @click="handleDelete(record, 0)"
                  v-auth="['tenant_user_delete']"
                  >{{ record.opType === 'system' ? '移除' : '删除' }}</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="handleEditOrDetail(record, 'edit')"
                  v-show="record.available === '2' || record.available === '1'"
                  v-auth="['tenant_user_edit']"
                  >编辑</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="handleEditOrDetail(record, 'detail')"
                  v-show="record.available === '2' || record.available === '1'"
                  v-auth="['tenant_user_details']"
                  >查看</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="resetPwdClick(record)"
                  v-show="record.available === '1' && record.opType !== 'system'"
                  v-auth="['tenant_user_reset_password']"
                  >重置密码</el-button
                >
                <el-button
                  type="primary"
                  link
                  v-show="record.available === '2' && record.opType !== 'system'"
                  @click="handleUnlock(record)"
                  v-auth="['tenant_user_unlock_lock']"
                  >解锁</el-button
                >

                <el-button
                  type="primary"
                  link
                  v-show="record.available === '1' && record.opType !== 'system'"
                  v-auth="['tenant_user_enable_disable']"
                  @click="handleDisable(record)"
                  >禁用</el-button
                >
                <el-button
                  type="primary"
                  link
                  v-show="record.available === '0' && record.opType !== 'system'"
                  @click="handleActivation(record)"
                  v-auth="['tenant_user_enable_disable']"
                  >激活</el-button
                >
              </template>
            </basic-table>
          </div>
        </div>
      </Pane>
    </Splitpanes>
    <!-- 新增  编辑  查看弹窗 -->
    <el-drawer v-model="drawer" direction="rtl" size="600px" :destroy-on-close="true">
      <template #header>
        <h4>{{ title }}</h4>
      </template>
      <template #default>
        <basic-form
          class="add-account"
          :formList="accountFormSchema"
          :isCreate="false"
          :formData="formData"
          :showSubmit="false"
          :check-strictly="true"
          ref="formAccount"
          :disabled="!isEdit"
        >
          <template #userName>
            <el-input
              v-model="formData.userName"
              placeholder="请输入账号名"
              @input="userNameChange"
              :disabled="userNameDisabled"
            />
          </template>
          <template #avatar>
            <el-upload
              class="avatar-uploader"
              action="/sys/oss/upload "
              :show-file-list="false"
              :headers="{
                Authorization: getAuthToken(),
              }"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="avatarPath" :src="avatarPath" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </template>
          <template #user="{ record }">
            <el-select
              style="width: 100%"
              v-model="formData.employeeId"
              clearable
              @focus="handleFocusUserEvent"
              @change="handleChangeUser"
              ref="userSelect"
              :disabled="record.opType === 'system' ? true : false"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            /></el-select>
          </template>

          <template #postStationIdList="{ record }">
            <el-select
              style="width: 100%"
              v-model="formData.postStationIdList"
              clearable
              @focus="handleFocusPostEvent"
              @change="handleChangePost"
              ref="postSelect"
              :disabled="record.opType === 'system' ? true : false"
            >
              <el-option
                v-for="item in postOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            /></el-select>
          </template>

          <template #roleIdList>
            <el-select
              style="width: 100%"
              v-model="formData.roleIdList"
              clearable
              multiple
              @focus="handleFocusRoleEvent"
              @change="handleChangeRole"
              ref="roleSelect"
              @remove-tag="removeTag"
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            /></el-select>
          </template>
        </basic-form>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmClick"
            v-if="isEdit"
            :loading="loading"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>
    <!-- 选择员工 -->
    <el-dialog
      v-model="visibleUser"
      title="选择员工"
      width="60%"
      :destroy-on-close="true"
    >
      <user-list
        @currentRowSelect="currentRowSelect"
        ref="userListRef"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleUser = false">取消</el-button>
        <el-button type="primary" @click="saveUserInfo">确定</el-button>
      </template>
    </el-dialog>
    <!-- 选择岗位 -->
    <el-dialog
      v-model="visiblePost"
      title="选择岗位"
      width="60%"
      :destroy-on-close="true"
    >
      <post-list
        ref="postListRef"
        @currentRowSelect="currentRowPostSelect"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visiblePost = false">取消</el-button>
        <el-button type="primary" @click="savePost">确定</el-button>
      </template>
    </el-dialog>
    <!-- 选择角色 -->
    <el-dialog
      v-model="visibleRole"
      title="选择角色"
      width="60%"
      :destroy-on-close="true"
    >
      <role-list
        ref="roleListRef"
        @selectRoles="selectRoles"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleRole = false">取消</el-button>
        <el-button type="primary" @click="saveRole">确定</el-button>
      </template>
    </el-dialog>
    <!-- 重置密码 -->
    <el-dialog v-model="visibleResetPwd" title="重置密码" width="400px">
      <basic-form
        class="reset-password"
        :formList="passwordFormSchema"
        :isCreate="false"
        :formData="formDataResetPwd"
        :showSubmit="false"
        ref="formResetPws"
      />
      <template #footer>
        <el-button @click="visibleResetPwd = false">取消</el-button>
        <el-button type="primary" @click="resetPwd" :loading="passwordLoading"
          >确定</el-button
        >
      </template>
    </el-dialog>
    <!-- 邀请员工 -->
    <el-drawer
      v-model="drawerAccount"
      direction="rtl"
      size="600px"
      :destroy-on-close="true"
    >
      <template #header>
        <h4>邀请员工</h4>
      </template>
      <basic-form
        :formList="inviteFormSchema"
        :isCreate="false"
        :formData="inviteFormData"
        :showSubmit="false"
        :check-strictly="true"
        ref="formInvite"
      >
        <template #userId>
          <el-select
            style="width: 100%"
            v-model="inviteFormData.userId"
            clearable
            multiple
            @focus="handleFocusAccountEvent"
            @change="handleChangeAccount"
            @remove-tag="removeTagAccount"
            ref="accountSelect"
          >
            <el-option
              v-for="item in accountOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          /></el-select>
        </template>

        <template #roleIds>
          <el-select
            style="width: 100%"
            v-model="inviteFormData.roleIds"
            clearable
            multiple
            @focus="handleFocusRoleInviteEvent"
            @change="handleChangeInviteRole"
            ref="roleSelect"
            @remove-tag="removeTag"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          /></el-select>
        </template>
      </basic-form>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawerAccount = false">取消</el-button>
          <el-button type="primary" @click="inviteConfirmClick" :loading="inviteLoading"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>
    <!-- 选择账号 -->
    <el-dialog
      v-model="visibleAccount"
      title="选择账号"
      width="60%"
      :destroy-on-close="true"
    >
      <acc-list
        @selectAccounts="selectAccounts"
        ref="accountListRef"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleAccount = false">取消</el-button>
        <el-button type="primary" @click="saveAccount">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
    >
      <el-button type="primary" link @click="downLoad(true)" style="margin-bottom: 12px"
        >下载租户账号导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="batchConfirmationVisible"
      title="删除警告"
      width="400px"
      class="batch-Confirmation-dialog"
      :destroy-on-close="true"
    >
      <BatchConfirmation @getPassword="getPassword" />
      <template #footer>
        <el-button @click="batchConfirmationVisible = false">取消</el-button>
        <el-button
          type="info"
          @click="batchConfirmationSave"
          :loading="batchConfirmationLoading"
          >删除</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, markRaw, nextTick } from 'vue';
  // import { ALLOrgList } from '/@/views/upms/api/dept';
  import { getAuthToken } from '/@/utils/storage/auth';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import {
    AccountListGetResultModel,
    tableItemMain,
  } from '/@/views/upms/api/model/accountModel';
  import BasicForm from '/@/components/sys/BasicForm';
  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { CircleClose, Plus, Download, Upload, Delete } from '@element-plus/icons-vue';
  import {
    columns,
    searchFormSchema,
    accountFormSchema,
    passwordFormSchema,
    inviteFormSchema,
    formData,
    formDataResetPwd,
  } from './account.data';
  import { Option } from '/@/components/sys/BasicForm/types';
  import {
    saveUser,
    resetPsssword,
    disabledUserApi,
    activationUserApi,
    tenantUploadApi,
  } from '/@/views/upms/api/account';
  import {
    getAccountList,
    inviteAccountApi,
    tenantAccountDelApi,
  } from '/@/views/upms/api/tenant';
  import { deptAsyn } from '/@/views/upms/api/dept';
  import { tenantUserdownloadUrlApi } from '/@/views/upms/api/user';
  import userList from './component/user.vue';
  import postList from './component/post.vue';
  import roleList from './component/role.vue';
  import accList from './component/account.vue';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { cloneDeep } from 'lodash-es';
  import useUser from './hooks/useUser';
  import usePost from './hooks/usePost';
  import useAccount from './hooks/useAccount';
  import { useTenantStore } from '/@/stores/modules/tenant';
  import useUpload from '/@/hooks/upms/upload/useUpload';
  import { getDictStorage } from '/@/utils/storage/dict';
  import type { UploadInstance } from 'element-plus';
  import { getAuthStorage, setAuthStorage } from '/@/utils/storage/auth';
  import BatchConfirmation from '/@/views/upms/components/BatchConfirmation.vue';
  import userBatchConfirmation from '/@/views/upms/common/hooks/userBatchConfirmation';
  import { userPermission } from '/@/views/upms/api/system';
  const avatarPath = ref('');
  const tree = ref<InstanceType<typeof VTree> | null>(null);
  const userInfo = getAuthStorage();
  console.log(userInfo);
  const upload = ref<UploadInstance>();
  const useTenant = useTenantStore();
  const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');

  const deptId = ref('');
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const searchRef = ref({});
  const sub = ref(true);
  const tableData = ref<any>([]);
  // const formData = ref<tableItem>({
  //   id: '',
  //   employeeId: '',
  //   available: '',
  //   userPasswd: '',
  //   roleIdList: [],
  //   userName: '',
  //   postStationIdList: [],
  //   deptName: '',
  //   deptId: [],
  //   opType: 'tenant',
  // });
  const drawer = ref(false);
  const isEdit = ref(true);
  const loading = ref(false);
  const formAccount = ref();
  // const deptCode = ref('');
  const title = ref('');
  const selectionIds = ref<string[]>([]);
  const userId = ref('');
  const userOptions = ref<Option[]>([]);

  const postOptions = ref<Option[]>([]);
  const roleOptions = ref<Option[]>([]);
  const postSelect = ref<HTMLDivElement | null>(null);
  const postListRef = ref<HTMLDivElement | null>(null);
  const roleSelect = ref<HTMLDivElement | null>(null);
  const roleListRef = ref<HTMLDivElement | null>(null);
  const userSelect = ref<HTMLDivElement | null>(null);
  const userListRef = ref<HTMLDivElement | null>(null);
  const visibleRole = ref(false);
  // const selectPostData = ref<any>({});
  const selectRoleData = ref<any>([]);
  const listItem = ref<any>({});
  const formResetPws = ref<HTMLDivElement | null>(null);
  const visibleResetPwd = ref(false);
  const passwordLoading = ref(false);
  const userNameDisabled = ref(false);
  // const formDataResetPwd = ref<resetPwdType>({
  //   id: '',
  //   userName: '',
  //   sendEmail: [],
  //   sendSMS: [],
  //   userPasswd: '',
  // });
  const initPass = ref('');
  const roleType = ref('1');

  const accountSelect = ref<HTMLDivElement | null>(null);
  const accountListRef = ref<HTMLDivElement | null>(null);
  const drawerAccount = ref(false);
  const accountOptions = ref<Option[]>([]);
  const inviteFormData = ref<any>({
    userId: [],
    tenantId: useTenant.getCurTenant.id,
    roleIds: [],
  });
  const selectRoleInviteData = ref<any>([]);
  const inviteLoading = ref(false);
  const uploadLoading = ref(false);
  const formInvite = ref();
  // formInvite
  const {
    handleChangeUser,
    handleFocusUserEvent,
    visibleUser,
    selectUserData,
    currentRowSelect,
    saveUserInfo,
  } = useUser(formData, userOptions, userSelect, userListRef);

  const {
    handleFocusPostEvent,
    handleChangePost,
    visiblePost,
    currentRowPostSelect,
    savePost,
    selectPostData,
  } = usePost(formData, postOptions, postSelect, postListRef);

  const {
    handleFocusAccountEvent,
    handleChangeAccount,
    removeTagAccount,
    selectAccounts,
    saveAccount,
    visibleAccount,
  } = useAccount(inviteFormData, accountOptions, accountSelect, accountListRef);
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      // currentIsLeaf.value = e.isLeaf;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  const handleCheckBox = (data) => {
    sub.value = data;
    accountList();
  };
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    accountList();
  };
  const accountList = () => {
    const { pageNum, pageSize } = page.value;
    const { available, employeeName, userName } =
      searchRef?.value?.['searchValue'] || {};
    getAccountList({
      pageNum,
      pageSize,
      // instCode: deptCode.value,
      deptId: deptId.value,
      available,
      userName,
      employeeName,
      opType: 'tenant',
      sub: sub.value ? '1' : '0',
      ...sortData.value,
      selfDeptId: userInfo.sysEmployee.deptId,
    }).then((res: AccountListGetResultModel) => {
      const { list, total, currentPage, pageSize } = res;
      tableData.value = list;
      page.value = {
        total,
        pageNum: currentPage,
        pageSize,
      };
    });
  };
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    accountList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    accountList();
  };
  const getuserPermission = () => {
    userPermission().then((res) => {
      userInfo.permissionMap = res;
      setAuthStorage(userInfo);
    });
  };
  // const cancelClick = () => {};
  const confirmClick = () => {
    loading.value = true;
    const getData = formAccount.value.submitForm;
    const ruleFormRef = formAccount.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        // data.sysUserRoleVoList = [];
        // if (data.userPasswd) {
        //   initPass.value = cloneDeep(data.userPasswd);
        //   data.userPasswd = encryption.pwdEncryptByAES(data.userPasswd);
        // } else {
        //   delete data.userPasswd;
        // }
        // data.version = formData.value.version;
        // if (Array.isArray(data.postStationIdList)) {
        //   data.postStationIdList = data.postStationIdList;
        // } else if (!data.postStationIdList) {
        //   data.postStationIdList = [];
        // } else {
        //   data.postStationIdList = [data.postStationIdList];
        // }
        // data.sysUserRoleVoList[0] = {
        //   deptType: '0',
        //   deptId: data.deptId[0],
        //   roleIds: data.roleIdList.join(','),
        // };
        // data.opType = 'tenant';
        const params: any = { ...data };
        params.sysUserRoleVoList = [];
        if (data.userPasswd) {
          initPass.value = cloneDeep(data.userPasswd);
          params.userPasswd = encryptedSM4(data.userPasswd);
        } else {
          delete data.userPasswd;
        }
        params.version = formData.value.version;
        // params.postStationIdList = [data.postStationIdList];
        if (Array.isArray(data.postStationIdList)) {
          params.postStationIdList = data.postStationIdList;
        } else if (!data.postStationIdList) {
          params.postStationIdList = [];
        } else {
          params.postStationIdList = [data.postStationIdList];
        }
        params.sysUserRoleVoList[0] = {
          deptType: '0',
          deptId: data.deptId[0],
          roleIds: data.roleIdList.join(','),
        };
        params.opType = 'tenant';
        params.avatarPath = avatarPath.value;
        saveUser(params)
          .then(() => {
            drawer.value = false;
            ElMessage({
              type: 'success',
              showClose: true,
              message: formData.value.id ? '修改账号成功' : '新增账号成功',
            });
            accountList();
            loading.value = false;
            // 如果编辑的是本账号，需要更新接口权限码
            if (formData.value.id && formData.value.id == userInfo.id) {
              getuserPermission();
            }
          })
          .catch(() => {
            formData.value.userPasswd = initPass.value;
            if (Array.isArray(data.postStationIdList)) {
              formData.value.postStationIdList = data.postStationIdList[0];
            } else if (!data.postStationIdList) {
              formData.value.postStationIdList = '';
            } else {
              formData.value.postStationIdList = data.postStationIdList;
            }
            formData.value.postStationIdList = data.postStationIdList;
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const inviteConfirmClick = () => {
    inviteLoading.value = true;
    const getData = formInvite.value.submitForm;
    const ruleFormRef = formInvite.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        inviteAccountApi(data)
          .then(() => {
            drawerAccount.value = false;
            ElMessage({
              type: 'success',
              showClose: true,
              message: '邀请账号成功',
            });
            accountList();
            inviteLoading.value = false;
          })
          .catch(() => {
            inviteLoading.value = false;
          });
      } else {
        inviteLoading.value = false;
      }
    });
  };

  const handleAdd = () => {
    console.log(userInfo);

    if (!!userInfo.tenantId) {
      drawer.value = true;
      title.value = '新增账号';
      isEdit.value = true;
      userNameDisabled.value = false;
      userId.value = '';
      formData.value = {
        id: '',
        employeeId: '',
        available: '1',
        userPasswd: getDictStorage().biz_ori_pass[0].value,
        roleIdList: [],
        userName: '',
        deptId: [],
        deptName: '',
        postStationIdList: [],
      };
      accountFormSchema.forEach((item: any) => {
        if (item.field == 'userPasswd') {
          item.ifShow = true;
        } else if (item.field == 'userName') {
          item.componentProps.disabled = false;
        } else if (item.field == 'available') {
          item.componentProps.disabled = false;
        }
      });
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择租户',
      });
    }
  };

  const handleEditOrDetail = (item, type) => {
    drawer.value = true;
    if (type === 'edit') {
      title.value = '编辑账号';
      isEdit.value = true;
      userNameDisabled.value = true;
      accountFormSchema.forEach((it: any) => {
        if (it.field == 'userPasswd') {
          it.ifShow = false;
        } else if (it.field == 'userName') {
          it.componentProps.disabled = true;
        } else if (it.field == 'available') {
          if (item.opType === 'system') {
            it.componentProps.disabled = true;
          } else {
            it.componentProps.disabled = false;
          }
        }
      });
      console.log(accountFormSchema);
    } else {
      title.value = '查看账号详情';
      isEdit.value = false;
    }
    avatarPath.value = item.avatarPath;
    item.userPasswd = undefined;
    userId.value = item.id;
    listItem.value = cloneDeep(item);
    formData.value = {
      id: listItem.value.id,
      employeeId: listItem.value.employeeId,
      available: listItem.value.available,
      roleIdList: listItem.value.roleIdList,
      userName: listItem.value.userName,
      postStationIdList:
        listItem.value.postStationIdList && listItem.value.postStationIdList.length > 0
          ? listItem.value.postStationIdList[0]
          : '',
      userPasswd: undefined,
      deptId: listItem.value.deptId,
      deptName: listItem.value.deptName,
      version: listItem.value.version,
      opType: listItem.value.opType,
    };
    userOptions.value = [];
    const obj: Option = {
      label:
        listItem.value.sysEmployee.empName + '/' + listItem.value.sysEmployee.empCode,
      value: listItem.value.sysEmployee.id,
    };
    selectUserData.value = listItem.value.sysEmployee;
    userOptions.value.push(obj);

    postOptions.value = [];
    const postObj: Option = {
      label: listItem.value.postStationNames,
      value:
        listItem.value.postStationIdList && listItem.value.postStationIdList.length > 0
          ? listItem.value.postStationIdList[0]
          : '',
    };
    selectPostData.value = {
      id:
        listItem.value.postStationIdList && listItem.value.postStationIdList.length > 0
          ? listItem.value.postStationIdList[0]
          : '',
      postStationName: listItem.value.postStationNames,
    };
    postOptions.value.push(postObj);

    roleOptions.value = [];
    const roleNames = listItem.value.roleNames.split(',');
    selectRoleData.value = [];
    for (let i = 0; i < listItem.value.roleIdList.length; i++) {
      let roleObj = {
        label: roleNames[i],
        value: listItem.value.roleIdList[i],
      };
      roleOptions.value.push(roleObj);
      let selectRoleObj = {
        name: roleNames[i],
        id: listItem.value.roleIdList[i],
        label: roleNames[i],
      };
      selectRoleData.value.push(selectRoleObj);
    }
  };

  const handleCurrentChange = (val) => {
    selectionIds.value = [];
    for (let i = 0; i < val.length; i++) {
      console.log(val[i].id);
      selectionIds.value.push(val[i].id);
    }
  };
  const handleDelete = (item, type) => {
    // type 1 批量删除  0 单独删除
    let title = '';
    if (type === 1) {
      title = `确认删除当前所选中${selectionIds.value.length}条账号数据？`;
    } else {
      title = '确认删除该账号？';
    }
    ElMessageBox.confirm(title, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        let params = {};
        if (item === null) {
          params = selectionIds.value;
        } else {
          params = [item.id];
        }
        tenantAccountDelApi({ userIds: params }).then(() => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '账号删除成功',
          });
          if (type === '1') {
            selectionIds.value = [];
            tableRef.value!.clearSelection();
          }
          accountList();
        });
      })
      .catch(() => {});
  };

  const callDelete = (params) => {
    tenantAccountDelApi({ userIds: params }).then(() => {
      ElMessage({
        showClose: true,
        type: 'success',
        message: '账号删除成功',
      });
      selectionIds.value = [];
      tableRef.value!.clearSelection();
      accountList();
    });
  };
  const {
    batchConfirmationVisible,
    batchDeletion,
    batchConfirmationSave,
    getPassword,
    batchConfirmationLoading,
  } = userBatchConfirmation(selectionIds, callDelete);

  const handleFocusRoleEvent = (e) => {
    visibleRole.value = true;
    roleType.value = '1';
    nextTick(() => {
      // roleSelect.value && roleSelect.value?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      if (selectRoleData.value.length > 0) {
        if (formData.value.roleIdList && formData.value.roleIdList.length > 0) {
          roleListRef.value &&
            roleListRef.value?.handleSetSelectData(selectRoleData.value);
        } else {
          roleListRef.value && roleListRef.value?.handleSetSelectData(null);
        }
      }
    });
  };
  const handleFocusRoleInviteEvent = (e) => {
    visibleRole.value = true;
    roleType.value = '2';
    nextTick(() => {
      // roleSelect.value && roleSelect.value?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      if (selectRoleInviteData.value.length > 0) {
        if (inviteFormData.value.roleIds && inviteFormData.value.roleIds.length > 0) {
          roleListRef.value &&
            roleListRef.value?.handleSetSelectData(selectRoleInviteData.value);
        } else {
          roleListRef.value && roleListRef.value?.handleSetSelectData(null);
          selectRoleInviteData.value = [];
        }
      }
    });
  };

  const handleChangeRole = (data) => {
    roleType.value = '1';
    if (data.length > 0) {
      roleListRef.value && roleListRef.value?.handleSetSelectData(selectRoleData.value);
    }
  };
  const handleChangeInviteRole = (data) => {
    roleType.value = '2';
    if (data.length > 0) {
      roleListRef.value &&
        roleListRef.value?.handleSetSelectData(selectRoleInviteData.value);
    }
  };

  const selectRoles = (data) => {
    if (roleType.value === '1') {
      selectRoleData.value = data;
    } else {
      selectRoleInviteData.value = data;
    }
  };
  const removeTag = (val) => {
    if (roleType.value === '1') {
      selectRoleData.value = removeById(selectRoleData.value, val);
    } else {
      selectRoleInviteData.value = removeById(selectRoleInviteData.value, val);
    }
  };
  const removeById = (arr, id) => {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].id === id) {
        arr.splice(i, 1);
        break;
      }
    }
    return arr;
  };

  const saveRole = () => {
    roleOptions.value = [];
    if (roleType.value === '1') {
      formData.value.roleIdList = [];
      for (let i = 0; i < selectRoleData.value.length; i++) {
        const obj = {
          label: selectRoleData.value[i].name,
          value: selectRoleData.value[i].id,
        };

        formData.value.roleIdList.push(selectRoleData.value[i].id);
        roleOptions.value.push(obj);
      }
    } else {
      inviteFormData.value.roleIds = [];
      for (let i = 0; i < selectRoleInviteData.value.length; i++) {
        const obj = {
          label: selectRoleInviteData.value[i].name,
          value: selectRoleInviteData.value[i].id,
        };

        inviteFormData.value.roleIds.push(selectRoleInviteData.value[i].id);
        roleOptions.value.push(obj);
      }
    }
    visibleRole.value = false;
  };

  const resetPwdClick = (row: tableItemMain) => {
    visibleResetPwd.value = true;
    const { id, userName } = row;
    formDataResetPwd.value = {
      id,
      userName: userName ? userName : '',
      sendEmail: [],
      sendSMS: [],
      userPasswd: getDictStorage().biz_ori_pass[0].value,
    };
  };
  /**
   * 重置密码
   */
  const resetPwd = () => {
    passwordLoading.value = true;
    const getData = formResetPws.value?.submitForm;
    const ruleFormRef = formResetPws.value?.ruleFormRef;
    getData(ruleFormRef, (res, data) => {
      const { id, userPasswd } = formDataResetPwd.value;
      const { userName, sendEmail, sendSMS } = data;
      const params = {
        id,
        userName,
        sendEmail: sendEmail.length > 0 ? true : false,
        sendSMS: sendSMS.length > 0 ? true : false,
        userPasswd: encryptedSM4(userPasswd),
      };
      if (res == 'success') {
        resetPsssword(params)
          .then(() => {
            visibleResetPwd.value = false;
            accountList();
            ElMessage({
              type: 'success',
              message: '重置密码成功',
            });
            passwordLoading.value = false;
          })
          .catch(() => {
            passwordLoading.value = false;
          });
      } else {
        passwordLoading.value = false;
      }
    });
  };
  const handleDisable = (row) => {
    ElMessageBox.confirm('是否要禁用该账号', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        disabledUserApi(row.id).then(() => {
          ElMessage({
            type: 'success',
            message: '禁用成功',
          });

          accountList();
        });
      })
      .catch(() => {});
  };

  const handleActivation = (row) => {
    ElMessageBox.confirm('是否要激活该账号', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'success',
    })
      .then(() => {
        activationUserApi(row.id).then(() => {
          ElMessage({
            type: 'success',
            message: '激活成功',
          });
          accountList();
        });
      })
      .catch(() => {});
  };

  const handleUnlock = (row) => {
    ElMessageBox.confirm('是否要解锁该账号', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'success',
    })
      .then(() => {
        activationUserApi(row.id).then(() => {
          ElMessage({
            type: 'success',
            message: '解锁成功',
          });
          accountList();
        });
      })
      .catch(() => {});
  };
  function downLoad(isTemplate) {
    if (!!userInfo.tenantId) {
      const { available, empName, userName } = searchRef?.value?.['searchValue'] || {};
      const params = {
        employeeName: empName,
        available,
        userName,
        opType: '',
        deptId: deptId.value,
        sub: sub.value ? '1' : '0',
        ...sortData.value,
        inlet: '1',
        isTemplate,
      };

      tenantUserdownloadUrlApi(params).then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
        });
        const fileName = '账号管理' + '.xlsx';
        const elink = document.createElement('a'); // 创建a标签
        elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
        elink.style.display = 'none';
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click(); // 点击下载
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink); // 释放标签
      });
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择租户',
      });
    }
  }
  const handlefileVisible = () => {
    if (!!userInfo.tenantId) {
      fileVisible.value = true;
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择租户',
      });
    }
  };
  const uploadFile = () => {
    uploadLoading.value = true;
    let formData = new FormData();
    formData.append('file', fileData.value?.raw);
    formData.append('tenantId', useTenant.getCurTenant.id);
    tenantUploadApi(formData)
      .then(() => {
        ElMessage({
          type: 'success',
          message: '导入成功',
        });
        fileVisible.value = false;
        accountList();
        uploadLoading.value = false;
      })
      .catch(() => {
        uploadLoading.value = false;
      });
  };

  const handleInviteUser = () => {
    if (!!userInfo.tenantId) {
      drawerAccount.value = true;
      inviteFormData.value = {
        userId: [],
        tenantId: useTenant.getCurTenant.id,
        roleIds: [],
      };
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择租户',
      });
    }
  };
  const userNameChange = (value) => {
    if (formData.value.userPasswd.match(value) !== null) {
      formAccount.value && formAccount.value.validateField('userPasswd');
    } else {
      formAccount.value && formAccount.value.clearValidate('userPasswd');
    }
  };
  const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
    avatarPath.value = uploadFile.response.result;
  };

  const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (
      rawFile.type !== 'image/jpeg' &&
      rawFile.type !== 'image/png' &&
      rawFile.type !== 'image/jpg'
    ) {
      ElMessage.error('Avatar picture must be JPG format!');
      return false;
    } else if (rawFile.size / 1024 / 1024 > 2) {
      ElMessage.error('Avatar picture size can not exceed 2MB!');
      return false;
    }
    return true;
  };
  watch(
    () => nodeId.value,
    (val) => {
      if (!!val) {
        deptId.value = val;
        accountList();
      } else {
        deptId.value = '';
        accountList();
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);

    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;

    .mid {
      height: 100%;

      box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }
  /*左侧div样式*/
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: calc(100vh - 30px);
      .active {
        background-color: #c9e9f7;
      }
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    border-radius: 6px;

    // .title {
    //   border-bottom: 1px solid #e4e7ed;
    //   line-height: 68px;
    //   font-size: 18px;
    //   padding-left: 18px;
    //   background: #ffffff;
    //   font-weight: 700;
    // }
    // .search-all {
    //   background: #ffffff;
    // }
    .search-div {
      border-radius: 6px;
      background: #fff;
      // padding: 10px 16px;
      .title {
        // border-bottom: 1px solid #e4e7ed;
        // line-height: 68px;

        font-size: 18px;
        padding: 20px 0 6px 20px;
        // background: #ffffff;
        font-weight: 700;
        color: #333333;
      }
    }
    .table-top-div {
      display: flex;
      justify-content: space-between;
      // line-height: 64px;
      margin-bottom: 20px;
      p {
        font-size: 18px;
        color: #333333;
        font-weight: 700;
        margin: 0;
        padding: 0;
        line-height: 32px;
      }
    }
    .table-content {
      margin-top: 20px;
      background: #ffffff;
      // height: calc(100% - 200px);
      padding: 20px 20px 0 20px;
      position: relative;
      overflow: auto;
      .top-button {
        // position: absolute;
        // top: 10px;
        // left: 16px;
        z-index: 9;
        .upload-btn {
          display: inline-block;
          margin: 0 12px;
        }
      }
    }
  }

  .table-content {
    background: #ffffff;
    margin-top: 20px;
  }
  h4 {
    font-size: 18px;
    margin-bottom: 0;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }

  .item-div {
    border-bottom: 1px solid #cccccc;
    .item-btn {
      margin-bottom: 12px;
      display: flex;
      justify-content: flex-end;
    }
    .item-form {
      margin-top: 12px;
    }
  }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }

  .avatar-uploader .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
  }

  .avatar-uploader .el-upload:hover {
    border-color: var(--el-color-primary);
  }

  .el-icon.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 60px;
    height: 60px;
    text-align: center;
  }
</style>
