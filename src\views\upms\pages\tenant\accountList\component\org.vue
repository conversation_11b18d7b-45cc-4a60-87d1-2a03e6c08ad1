<template>
  <div class="content-model">
    <div class="left">
      <div class="tree-content">
        <div class="tree-div">
          <VTree ref="tree" :load="getDeptData" @click="handleTreeClick">
            <template #node="{ node }">
              <span
                :class="node.id == nodeId ? 'active' : ''"
                :style="{
                  color:
                    name && node.instName.indexOf(name) > -1 ? 'rgb(166, 0, 0)' : '',
                  fontWeight: name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                }"
                >{{ node.instName }}</span
              >
            </template>
          </VTree>
        </div>
      </div></div
    >
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, nextTick } from 'vue';
  import VTree from '@wsfe/vue-tree';
  import { deptAsyn } from '/@/views/upms/api/dept';
  let emits = defineEmits(['currentOrgSelect']);
  const name = ref('');
  const nodeId = ref('');
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      // currentIsLeaf.value = e.isLeaf;
    }
  };

  const currentNode = (id) => {
    console.log(id);
    nextTick(() => {
      setTimeout(() => {
        nodeId.value = id;
      }, 1000);
    });
  };

  watch(
    () => nodeId.value,
    (val) => {
      console.log(val, tree.value.getNode(nodeId.value));
      const node = tree.value.getNode(nodeId.value);
      emits('currentOrgSelect', node);
    },
  );
  defineExpose({
    currentNode,
  });
</script>

<style scoped lang="scss">
  .content-model {
    display: flex;
    height: 500px;
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    // height: calc(100% - 24px);
    // margin: 12px 12px;
    height: 100%;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;

    .mid {
      height: 100%;
      box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }
  /*左侧div样式*/
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: calc(100vh - 30px);
      .active {
        background-color: #c9e9f7;
      }
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    border-radius: 6px;

    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 68px;
      font-size: 18px;
      padding-left: 18px;
      background: #ffffff;
      font-weight: 700;
    }
    .search-all {
      background: #ffffff;
    }
    .table-content {
      margin-top: 20px;
      background: #ffffff;
      height: calc(100% - 200px);
      padding: 10px 16px 0;
      position: relative;
      overflow: auto;
      .top-button {
        position: absolute;
        top: 10px;
        left: 16px;
        z-index: 9;
      }
    }
  }

  .table-content {
    background: #ffffff;
    margin-top: 20px;
  }
  h4 {
    font-size: 18px;
    margin-bottom: 0;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
</style>

<style lang="scss">
  .content-model .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }

  .select-div {
    line-height: 40px;
    font-size: 16px;

    span {
      color: #409eff;
    }
  }
</style>
