import {
  noticeListParams,
  NoticeItem,
  AddNoticeParams,
  } from './model/noticeModel';
  import { sysHttp } from '/@/views/upms/common/http';

  enum Api {
    noticeList = '/notice/list',
    notice = '/notice',
    noticePush = '/notice/push',
    noticeRevoke = '/notice/revoke',
    getNoticeTemplates = '/notice/getNoticeTemplates',
  }

  /**
   * @description: Get user menu based on id
   */
  // 分页查询【公告】列表
  export const postNoticeList = (params?: noticeListParams) => {
    return sysHttp.post<NoticeItem[]>({
      url: Api.noticeList,
      params: params,
    });
  };
  // 新增【公告】
  export const saveNotice = (params: AddNoticeParams) => {
    return sysHttp.post({
      url: Api.notice,
      params: params,
    });
  };
  // 查询【公告】详情
  export const getNotice = (id: string) => {
    return sysHttp.get({
      url: `${Api.notice}/${id}?flag=true`,
    });
  };
  // 批量删除【公告】
  export const deleteNotice = (params) => {
    return sysHttp.delete({
      url: Api.notice,
      params: params,
    });
  };
  // 删除【公告】
  export const deleteNoticeId = (id: string) => {
    return sysHttp.delete({
      url: `${Api.notice}/${id}`,
    });
  };
  // 发布【公告】
  export const pushNotice = (id: string) => {
    return sysHttp.put({
      url: `${Api.noticePush}/${id}`,
    });
  };
  // 撤销【公告】
  export const noticeRevoke = (id: string) => {
    return sysHttp.put({
      url: `${Api.noticeRevoke}/${id}`,
    });
  };
  // 消息模板
  export const getNoticeTemplates = () => {
    return sysHttp.get({
      url: `${Api.getNoticeTemplates}`,
    });
  };
