<template>
  <div class="menu-container">
    <div class="menu-header">
      <h3>菜单管理</h3>
      <p>这是菜单管理页面，用于管理系统菜单结构。</p>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-form :inline="true" :model="searchParams" class="demo-form-inline">
        <el-form-item label="菜单名称">
          <el-input v-model="searchParams.title" placeholder="请输入菜单名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus">
        新增菜单
      </el-button>
      <el-button type="info" @click="handleRefresh" :icon="Refresh">
        刷新
      </el-button>
      <el-button type="warning" @click="handleExpandAll" :icon="Sort">
        展开/折叠
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-table
        ref="tableRef"
        :data="tableData"
        v-loading="loading"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="isExpandAll"
        style="width: 100%"
      >
        <el-table-column prop="title" label="菜单名称" width="200" align="left" />
        <el-table-column prop="icon" label="图标" width="80" align="center">
          <template #default="{ row }">
            <el-icon v-if="row.icon">
              <component :is="row.icon" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="orderNum" label="排序" width="80" align="center" />
        <el-table-column prop="perms" label="权限标识" width="150" align="center" />
        <el-table-column prop="component" label="组件路径" width="200" align="center" />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag v-if="row.status === '0'" type="success">正常</el-tag>
            <el-tag v-else type="danger">停用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160" align="center" />
        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="primary" link @click="handleAddChild(row)">
              新增
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 菜单编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="菜单名称" prop="title">
          <el-input v-model="formData.title" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="路由名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入路由名称" />
        </el-form-item>
        <el-form-item label="路由地址" prop="path">
          <el-input v-model="formData.path" placeholder="请输入路由地址" />
        </el-form-item>
        <el-form-item label="组件路径" prop="component">
          <el-input v-model="formData.component" placeholder="请输入组件路径" />
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="formData.orderNum" :min="0" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="formData.status" placeholder="请选择状态">
            <el-option label="正常" value="0" />
            <el-option label="停用" value="1" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Refresh, Sort } from '@element-plus/icons-vue';
import {
  getMenuList,
  saveMenu,
  updateMenu,
  deleteMenus,
  type MenuInfo,
  type MenuQueryParams
} from '/@/api/sys/menus';

// 响应式数据
const tableRef = ref();
const formRef = ref();

const tableData = ref<MenuInfo[]>([]);
const loading = ref(false);
const submitLoading = ref(false);
const isExpandAll = ref(false);

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('');
const isEdit = ref(false);

// 表单数据
const formData = ref<MenuInfo>({
  title: '',
  name: '',
  path: '',
  component: '',
  orderNum: 0,
  status: '0',
});

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
};

// 搜索参数
const searchParams = ref<MenuQueryParams>({});

// 生命周期
onMounted(() => {
  loadMenuList();
});

// 加载菜单列表
const loadMenuList = async () => {
  try {
    loading.value = true;
    const result = await getMenuList(searchParams.value);
    if (result) {
      tableData.value = result;
    }
  } catch (error) {
    ElMessage.error('加载菜单列表失败');
    console.error('Load menu list error:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  loadMenuList();
};

// 重置搜索
const handleReset = () => {
  searchParams.value = {};
  loadMenuList();
};

// 刷新
const handleRefresh = () => {
  loadMenuList();
};

// 展开/折叠
const handleExpandAll = () => {
  isExpandAll.value = !isExpandAll.value;
};

// 新增菜单
const handleAdd = () => {
  dialogTitle.value = '新增菜单';
  isEdit.value = false;
  formData.value = {
    title: '',
    name: '',
    path: '',
    component: '',
    orderNum: 0,
    status: '0',
  };
  dialogVisible.value = true;
};

// 新增子菜单
const handleAddChild = (record: MenuInfo) => {
  dialogTitle.value = '新增子菜单';
  isEdit.value = false;
  formData.value = {
    title: '',
    name: '',
    path: '',
    component: '',
    orderNum: 0,
    parentId: record.id!,
    status: '0',
  };
  dialogVisible.value = true;
};

// 编辑菜单
const handleEdit = (record: MenuInfo) => {
  dialogTitle.value = '编辑菜单';
  isEdit.value = true;
  formData.value = { ...record };
  dialogVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate();
    if (!valid) return;

    submitLoading.value = true;

    if (isEdit.value) {
      await updateMenu(formData.value);
      ElMessage.success('更新菜单成功');
    } else {
      await saveMenu(formData.value);
      ElMessage.success('新增菜单成功');
    }

    dialogVisible.value = false;
    loadMenuList();
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新菜单失败' : '新增菜单失败');
    console.error('Submit menu error:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 删除菜单
const handleDelete = async (record: MenuInfo) => {
  try {
    await ElMessageBox.confirm('确定要删除该菜单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await deleteMenus(record.id!);
    ElMessage.success('删除菜单成功');
    loadMenuList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除菜单失败');
      console.error('Delete menu error:', error);
    }
  }
};
</script>

<style scoped lang="scss">
.menu-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  margin: 8px;

  .menu-header {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e6e8ee;

    h3 {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }

  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .action-section {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
  }

  .table-section {
    // 表格样式
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}
</style>
