<template>
  <div class="content">
    <div class="box">
      <el-tabs tab-position="left" class="tabs-div" v-model="activeName">
        <el-tab-pane label="基本设置" name="info">
          <basic-form
            :isCreate="false"
            :form-list="userSchema"
            :formData="formData"
            @success="submitData"
          >
            <template #avatar>
              <el-upload
                class="avatar-uploader"
                action="/sys/oss/upload"
                :show-file-list="false"
                :headers="{
                  Authorization: getAuthToken(),
                }"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
              >
                <img v-if="avatarPath" :src="avatarPath" class="avatar" />
                <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </template>
            <template #autocomplete>
              <el-autocomplete
                style="width: 92%; margin-right: 10px"
                v-model="formData.email"
                :fetch-suggestions="querySearchEmail"
                :trigger-on-focus="false"
                placeholder="电子邮箱"
                :disabled="!checkFlag.email"
              />
              <el-icon class="user-check-full" @click="getAccurate('email')"
                ><View style="cursor: pointer"
              /></el-icon>
              <!--<el-autocomplete
                style="width: 100%;"
                v-model="formData.email"
                :fetch-suggestions="querySearchEmail"
                :trigger-on-focus="false"
                placeholder="电子邮箱"
              />-->
            </template>
            <template #phone>
              <el-input
                style="width: 92%; margin-right: 10px"
                v-model="formData.phone"
                placeholder="手机号"
                :disabled="!checkFlag.phone"
              />
              <el-icon class="user-check-full" @click="getAccurate('phone')"
                ><View style="cursor: pointer"
              /></el-icon>
              <!--<el-input
                style="width: 100%"
                v-model="formData.phone"
                placeholder="手机号"
              />-->
            </template>
            <template #roleNames>
              <el-tag
                style="margin-right: 8px"
                v-for="item in formData.roleNameList"
                :key="item"
                >{{ item }}</el-tag
              >
            </template>
            <template #TenantsNames>
              <el-tag
                style="margin-right: 8px"
                v-for="item in formData.tenantList"
                :key="item"
                >{{ item }}</el-tag
              >
            </template>
            <template #projectsNames>
              <el-tag
                style="margin-right: 8px"
                v-for="item in formData.projectList"
                :key="item"
                >{{ item }}</el-tag
              >
            </template>
            <template #deptName>
              <el-tag
                style="margin-right: 8px"
                v-for="item in formData.deptNameList"
                :key="item"
                >{{ item }}</el-tag
              >
            </template>

            <template #description>
              <el-input
                v-model="formData.description"
                type="textarea"
                placeholder="请输入个人简介"
              />
            </template>
          </basic-form>
        </el-tab-pane>
        <!-- <el-tab-pane label="新消息通知">新消息通知</el-tab-pane> -->
        <el-tab-pane label="修改密码" name="editPassWord">
          <basic-form
            :isCreate="false"
            :form-list="updatepwdFormSchema"
            :formData="formPassWordData"
            @success="submitPassWordData"
          />
        </el-tab-pane>
        <el-tab-pane label="消息通知" name="message">
          <div v-for="item in noticeTypeList" :key="item.id">
            <div class="notice">
              <div class="left">
                <div class="title">{{ item.label }}</div>
                <div class="describe">{{ item.label }}将以站内信的形式通知</div>
              </div>
              <div class="right">
                <el-switch
                  v-model="userMsgConf"
                  @change="postConfUser"
                  v-if="item.value == '1'"
                />
                <el-switch
                  v-model="sysMsgConf"
                  @change="postConfUser"
                  v-if="item.value == '2'"
                />
                <el-switch
                  v-model="noticeMsgConf"
                  @change="postConfUser"
                  v-if="item.value == '3'"
                />
              </div>
            </div>
            <el-divider />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-dialog
      v-model="accurateVisible"
      title="校验密码"
      width="400px"
      class="batch-Confirmation-dialog"
      :destroy-on-close="true"
    >
      <AccurateConfirm @getPassword="(value) => (accuratePassWord = value)" />
      <template #footer>
        <el-button @click="accurateVisible = false">取消</el-button>
        <el-button @click="confirmAccurate" type="primary">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup name="UpdatePwd">
  import { ref } from 'vue';
  import { ElMessage } from 'element-plus';
  import { Plus } from '@element-plus/icons-vue';
  import { useMessage } from '/@/hooks/web/useMesage';
  import { getAuthStorage, setAuthStorage } from '/@/utils/storage/auth';
  import { getDictStorage } from '/@/utils/storage/dict';
  import { getAuthToken } from '/@/utils/storage/auth';
  // import { userSchema } from './userinfo';
  import { updateInfo, getConf, userConf, accurate } from '/@/views/shares/api/user';
  // import { userConf } from '/@/views/shares/api/model/userSetting'
  import { encryptedSM4 } from '/@/utils/cipher';
  import { updatepwdFormSchema } from './updatepwd.data';
  import { UpdatePassword } from '/@/views/shares/api/userSetting';
  import { updatePwdParams } from '/@/views/shares/api/model/userSetting';
  import { useLogout } from '/@/hooks/web/useLogout';
  import { EditDetParams } from '../../../shares/api/model/deptModel';
  import AccurateConfirm from './accurateConfirm.vue';
  const emailReg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  const phoneReg =
    /^((\+86|0086)?\s*)((134[0-8]\d{7})|(((13([0-3]|[5-9]))|(14[5-9])|15([0-3]|[5-9])|(16(2|[5-7]))|17([0-3]|[5-8])|18[0-9]|19(1|[8-9]))\d{8})|(14(0|1|4)0\d{7})|(1740([0-5]|[6-9]|[10-12])\d{7}))$/;
  import { checkApi } from '../../../upms/api/user';
  const { createMessage } = useMessage();
  const userInfo = getAuthStorage();

  console.log(userInfo);
  const activeName = ref<string | number>('info');
  let formData = ref<any>({
    // empName: userInfo.empName,
    // empType: userInfo.empType,
    // status: userInfo.status,
    // gender: userInfo.gender,
    // email: userInfo.email,
    // industryType: userInfo.industryType,
    ...userInfo.sysEmployee,
    userName: userInfo.userName,
    avatarPath: userInfo.avatarPath,
    roleNames: userInfo.roleNames,
    deptName: userInfo.deptName,
    deptNameList: !!userInfo.deptName ? userInfo.deptName?.split(',') : [],
    roleNameList: !!userInfo.roleNames ? userInfo.roleNames?.split(',') : [],
    tenantList: !!userInfo.myTenantNames ? userInfo.myTenantNames?.split(',') : [],
    projectList: !!userInfo.myProjectNames ? userInfo.myProjectNames?.split(',') : [],
    description: !!userInfo.sysEmployee.description
      ? userInfo.sysEmployee.description
      : '',
  });

  let formPassWordData = ref<updatePwdParams>({
    id: userInfo.id,
    userPasswd: '',
    newPasswd: '',
    realName: userInfo.realName,
    email: userInfo.email,
    phone: userInfo.phone,
    available: userInfo.available,
    dept: userInfo.dept,
    description: userInfo.description,
    userName: userInfo.userName,
  });

  console.log(formPassWordData.value);
  const userSchema: any[] = [
    {
      label: '电子邮箱',
      field: 'email',
      slot: 'autocomplete',
      rules: [
        {
          required: true,
          trigger: 'blur',
          // pattern: emailReg,
          message: '请输入正确的电子邮箱',
        },
        { max: 100, message: '超过长度限制，最多100字' },
      ],
      required: true,
    },
    {
      field: 'empName',
      label: '员工姓名',
      component: 'Input',
      componentProps: {
        placeholder: '请输入姓名',
        disabled: false,
      },
      rules: [{ max: 90, message: '超过长度限制，最多90字' }],
      required: true,
    },
    {
      field: 'userName',
      label: '账号',
      component: 'Input',
      componentProps: {
        placeholder: '请输入账号',
        disabled: true,
      },
      rules: [{ max: 90, message: '超过长度限制，最多90字' }],
      required: true,
    },
    {
      field: 'avatar',
      label: '头像',
      component: 'Input',
      slot: 'avatar',
    },
    {
      label: '手机号',
      field: 'phone',
      component: 'Input',
      componentProps: {
        placeholder: '请输入手机号',
      },
      slot: 'phone',
      rules: [
        {
          // pattern: phoneReg,
          message: '请输入正确的手机号',
        },
        {
          validator: (rule, value) => {
            if (!value) {
              /* eslint-disable-next-line */
              return true;
            }
            return new Promise<void>((resolve, reject) => {
              const params = {
                type: 2,
                value: value,
                id: userInfo.sysEmployee.id,
              };
              checkApi(params).then((res) => {
                res ? resolve() : reject(res.message || '手机号码已存在');
              });
            });
          },
        },
      ],
      // required: true,
    },
    {
      label: '机构',
      component: 'Slot',
      field: 'deptName',
      slot: 'deptName',
      required: true,
    },
    {
      label: '角色',
      component: 'Slot',
      field: 'roleNames',
      slot: 'roleNames',
      required: true,
    },
    // {
    //   label: '消息提醒设置',
    //   field: 'available',
    //   component: 'Select',
    //   placeholder: '请选择提醒设置',
    //   componentProps: {
    //     clearable: true,
    //     options: [
    //       {
    //         label: '站内信',
    //         value: '1',
    //       },
    //       {
    //         label: '公告',
    //         value: '0',
    //       },
    //     ],
    //   },
    // },
    {
      label: '我的租户',
      component: 'Slot',
      field: 'TenantsNames',
      slot: 'TenantsNames',
    },
    {
      label: '我的项目',
      component: 'Slot',
      field: 'projectsNames',
      slot: 'projectsNames',
    },
    {
      label: '个人简介',
      component: 'Slot',
      field: 'description',
      slot: 'description',
    },
    // {
    //   label: '所属类型：',
    //   field: 'empType',
    //   component: 'Text',
    // },
    // {
    //   label: '状态：',
    //   field: 'status',
    //   component: 'Text',
    // },
    {
      label: '性别：',
      field: 'gender',
      component: 'RadioGroup',
      componentProps: {
        options: [
          {
            label: '男',
            value: 'M',
          },
          {
            label: '女',
            value: 'F',
          },
        ],
      },
      required: true,
    },

    // {
    //   label: '所属板块：',
    //   field: 'industryType',
    //   component: 'Text',
    // },
  ];
  const userMsgConf = ref(false);
  const sysMsgConf = ref(false);
  const noticeMsgConf = ref(false);
  const userId = ref('1');
  const avatarPath = ref('');
  avatarPath.value = userInfo.avatarPath;
  // const userConfData = ref(<userConf>{
  //   userId: '',
  //   noticeMsgConf: '0',
  //   sysMsgConf: '0',
  //   userMsgConf: '0',
  // })
  const noticeTypeList = getDictStorage().biz_notice_type;
  const currentFlag = ref<string>('');
  const accurateVisible = ref(false);
  const accuratePassWord = ref<string>('');
  const checkFlag = ref({
    phone: false,
    email: false,
  });
  // .map((item) => {
  //   if (item.value == '1') {
  //     item.describe = '用户的消息将以站内信的形式通知';
  //   } else if (item.value == '3') {
  //     item.describe = '公告消息将以站内信的形式通知';
  //   } else {
  //     item.describe = '系统消息将以站内信的形式通知';
  //   }
  //   return item;
  // });
  /**
   * 提交数据
   */
  const submitData = (data) => {
    userInfo.sysEmployee = data;
    userInfo.realName = data.empName;
    // setAuthStorage(userInfo);
    const { id, email, empName, gender, phone } = data;
    updateInfo({
      id,
      email: email ? encryptedSM4(email) : undefined,
      empName,
      gender,
      phone: phone ? encryptedSM4(phone) : undefined,
      avatarPath: avatarPath.value,
      userId: userInfo.id,
    }).then(() => {
      ElMessage({
        type: 'success',
        message: '修改成功',
      });
      userInfo.avatarPath = avatarPath.value;
      setAuthStorage(userInfo);
      // location.reload();
    });
  };

  interface queryListe {
    value: string;
  }
  // 邮箱自动填充后缀名
  function querySearchEmail(queryString, callback) {
    const emailList = [
      { value: '@chinapost.com.cn' },
      { value: '@qq.com' },
      { value: '@163.com' },
      { value: '@sina.com' },
      { value: '@sohu.com' },
      { value: '@yahoo.com.cn' },
    ];
    let results: queryListe[] = [];
    let queryList: queryListe[] = [];
    emailList.map((item) =>
      queryList.push({ value: queryString.split('@')[0] + item.value }),
    );
    results = queryString ? queryList.filter(createFilter(queryString)) : queryList;
    callback(results);
  }

  // 邮箱填写过滤
  function createFilter(queryString) {
    return (item) => {
      return item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
    };
  }
  const getAccurate = (flag) => {
    currentFlag.value = flag;
    accurateVisible.value = true;
  };
  const confirmAccurate = () => {
    if (!accuratePassWord.value) {
      ElMessage({
        message: '请输入密码确认！',
        grouping: true,
        type: 'warning',
      });
    } else {
      accurate(accuratePassWord.value, formData.value.id).then((res) => {
        accurateVisible.value = false;
        let pattern = '';
        if (currentFlag.value == 'phone') {
          formData.value.phone = res.phone;
          pattern = phoneReg;
          checkFlag.value.phone = true;
        } else if (currentFlag.value == 'email') {
          formData.value.email = res.email;
          pattern = emailReg;
          checkFlag.value.email = true;
        }
        userSchema.map((item, index) => {
          if (item.field == currentFlag.value) {
            userSchema[index].rules[0].pattern = pattern;
          }
        });
        accuratePassWord.value = '';
      });
    }
  };
  const { logout } = useLogout();

  const submitPassWordData = (data: EditDetParams) => {
    if (data.newPasswd != data.confirmnewPasswd) {
      ElMessage({
        message: '新密码和确认新密码不一致,请重新输入',
        grouping: true,
        type: 'warning',
      });
      return false;
    }

    if (
      data.userPasswd === data.newPasswd ||
      data.userPasswd === data.confirmnewPasswd
    ) {
      ElMessage({
        message: '新密码和旧密码不可以一致,请重新输入',
        grouping: true,
        type: 'warning',
      });
      return false;
    }
    UpdatePassword({
      id: userInfo.id,
      userName: userInfo.userName,
      userPasswd: encryptedSM4(data.userPasswd),
      newPasswd: encryptedSM4(data.newPasswd),
    }).then(() => {
      createMessage.success('修改密码成功');
      ElNotification.close();
      // 退出登录
      logout();
    });
  };

  const getQueryParam = (param) => {
    let url = new URL(window.location.href);
    return url.searchParams.get(param);
  };
  const getConfUser = () => {
    getConf().then((res) => {
      console.log('res', res);
      // userConfData.value = res;
      // const {noticeMsgConf, sysMsgConf, userMsgConf, userId } = res;
      // console.log(' Number(', userId)
      userId.value = res.userId.toString();
      userMsgConf.value = Boolean(Number(res.userMsgConf));
      sysMsgConf.value = Boolean(Number(res.sysMsgConf));
      noticeMsgConf.value = Boolean(Number(res.noticeMsgConf));
      // console.log(' Number(1111111', userId.value)
    });
  };
  const postConfUser = () => {
    // console.log('e', e);
    // if(type == '1'){
    //   userMsgConf.value =
    // }
    // return false;
    userConf({
      userId: userId.value,
      userMsgConf: Number(userMsgConf.value).toString(),
      sysMsgConf: Number(sysMsgConf.value).toString(),
      noticeMsgConf: Number(noticeMsgConf.value).toString(),
    }).then((res) => {
      getConfUser();
      console.log('res', res);
      // userConfData.value = res;
      // const {noticeMsgConf, sysMsgConf, userMsgConf, userId } = res;
      // userMsgConf.value = Boolean(userMsgConf);
      // sysMsgConf.value = Boolean(sysMsgConf);
      // noticeMsgConf.value = Boolean(noticeMsgConf);
      // userId.value = userId;
    });
  };
  const handleAvatarSuccess: UploadProps['onSuccess'] = (response, uploadFile) => {
    avatarPath.value = uploadFile.response.result;
  };

  const beforeAvatarUpload: UploadProps['beforeUpload'] = (rawFile) => {
    if (
      rawFile.type !== 'image/jpeg' &&
      rawFile.type !== 'image/png' &&
      rawFile.type !== 'image/jpg'
    ) {
      ElMessage.error('请上传"jpeg、png、jpg"格式图片!');
      return false;
    } else if (rawFile.size / 1024 / 1024 > 2) {
      ElMessage.error('图片大小不能超过2MB!');
      return false;
    }
    return true;
  };
  // alert(getQueryParam('tab'));
  if (getQueryParam('tab') === 'editPassWord') {
    // activeName
    activeName.value = 'editPassWord';
  } else if (getQueryParam('tab') === 'message') {
    activeName.value = 'message';
  } else {
    activeName.value = 'info';
  }
  getConfUser();
  // const id = getQueryParam('id');
</script>
<route lang="yaml">
meta:
  auth: true
</route>
//
<style scoped lang="scss">
  .update-pwd {
    margin: 10px 100px;
  }
  .content {
    display: flex;
    height: calc(100vh - 81px);
    /* height: 500px; */
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    margin: 12px;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;
    background: #ffffff;
  }
  .notice {
    width: calc(100% - 40px);
    display: flex;
    margin: 20px;
    justify-content: space-between;
    .title {
      font-weight: 600;
    }
    .describe {
      color: #ccc;
    }
  }
  .avatar-uploader .avatar {
    width: 60px;
    height: 60px;
    display: block;
  }
</style>

<style lang="scss">
  .tabs-div {
    width: 100%;
    padding: 12px;
    .el-tabs__item.is-active {
      background: rgba(230, 247, 255, 1);
    }
    .el-tabs__header {
      .el-tabs__nav {
        width: 180px;
      }
    }
    // .el-tabs--left .el-tabs__item.is-left {
    //   justify-content: flex-start;
    // }

    .el-tabs__item.is-left {
      justify-content: flex-start !important;
    }
    .avatar-uploader .el-upload {
      border: 1px dashed var(--el-border-color);
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: var(--el-transition-duration-fast);
    }

    .avatar-uploader .el-upload:hover {
      border-color: var(--el-color-primary);
    }

    .el-icon.avatar-uploader-icon {
      font-size: 28px;
      color: #8c939d;
      width: 60px;
      height: 60px;
      text-align: center;
    }
  }
</style>
