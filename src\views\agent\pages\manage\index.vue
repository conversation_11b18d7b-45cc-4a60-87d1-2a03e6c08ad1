<template>
  <div class="agent-container">
    <div class="agent-header"> 智能体管理 </div>
    <div class="main">
      <!-- MCP工具管理  智能体配置  智能体编排 -->

      <div class="sider">
        <ul>
          <li class="active">智能体编排</li>
          <li>智能体配置</li>
          <li>MCP工具管理</li>
        </ul>
      </div>
      <div class="content"> 2323 </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  // const agentSetList = [
  //   {
  //     id: '40',
  //     name: 'DEFAULT_AGENT',
  //     description:
  //       '一个多功能默认代理，可以使用文件操作和shell命令处理各种用户请求。非常适合可能涉及文件操作、系统操作或文本处理的通用任务。',
  //     systemPrompt: ' ',
  //     nextStepPrompt:
  //       '为实现我的目标，下一步应该做什么？\n\n请记住：\n1. 在操作前验证所有输入和路径\n2. 为每个任务选择最合适的工具：\n   - 使用bash进行系统操作\n   - 使用text_file_operator进行文件操作\n   - 任务完成时使用terminate\n3. 优雅地处理错误\n4. 重要：你必须在回复中使用至少一个工具才能取得进展！\n\n逐步思考：\n1. 需要的核心操作是什么？\n2. 哪种工具组合最合适？\n3. 如何处理潜在错误？\n4. 预期的结果是什么？\n5. 如何验证成功？\n\n',
  //     availableTools: ['bash', 'text_file_operator', 'terminate'],
  //     className:
  //       'com.alibaba.cloud.ai.example.manus.dynamic.agent.startupAgent.DDefaultAgent',
  //   },
  //   {
  //     id: '48',
  //     name: 'SQL_EXE_AGENT',
  //     description: 'SQL执行与智能展示专家，基于前序分析提供表格和图表展示方案',
  //     systemPrompt: '',
  //     availableTools: ['bash', 'text_file_operator', 'terminate'],
  //     className:
  //       'com.alibaba.cloud.ai.example.manus.dynamic.agent.startupAgent.DDefaultAgent',
  //   },
  // ];
</script>

<style scoped lang="scss">
  .agent-container {
    height: calc(100vh - 62px);
    border: 1px solid #e6e8ee;
    background-color: #fff;
    border-radius: 8px;
    margin: 0 8px 8px 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .agent-header {
      border-bottom: 1px solid #e6e8ee;
      background: #fff;
      background-color: #ffffff;
      justify-content: space-between;
      align-items: center;
      height: 64px;
      padding: 16px 20px;
      display: flex;
      position: relative;
    }
    .main {
      display: flex;
      flex: 1;
      .sider {
        flex: 0 0 200px;
        border-right: 1px solid #e6e8ee;
        ul {
          li.active {
            margin: 8px 12px;
            background: rgba(205, 208, 220, 0.8);
            // margin-bottom: 12px;
          }
          li {
            margin: 8px 12px;
            padding: 8px 12px;
            border-radius: 5px;
            cursor: pointer;
            // margin-bottom: 12px;
          }
        }
      }
      .content {
        flex: 1;
        overflow: auto;
      }
    }
  }
</style>
