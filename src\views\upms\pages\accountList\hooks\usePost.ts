import { ref, nextTick } from 'vue';
import { Option } from '/@/components/sys/BasicForm/types';
export default function usePost(formData, postOptions, postSelect, postListRef) {
  const visiblePost = ref(false);
  const selectPostData = ref<any>([]);
  const handleFocusPostEvent = (e) => {
    visiblePost.value = true;
    nextTick(() => {
      // postSelect.value && postSelect.value?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      if (selectPostData.value.id) {
        if (formData.value.postStationIdList) {
          postListRef.value &&
            postListRef.value?.handleSetCurrentRow(selectPostData.value);
        } else {
          postListRef.value && postListRef.value?.handleSetCurrentRow(null);
        }
      }
    });
  };
  const handleChangePost = (data) => {
    if (data === '' || data === null) {
      selectPostData.value = {};
      postListRef.value && postListRef.value?.handleSetCurrentRow(null);
    }
  };
  const currentRowPostSelect = (data) => {
    if (data) {
      selectPostData.value = data;
    } else {
      selectPostData.value = {};
    }
  };
  const savePost = () => {
    postOptions.value = [];
    if (selectPostData.value.id) {
      const obj: Option = {
        label: selectPostData.value.postStationName,
        value: selectPostData.value.id,
      };
      postOptions.value.push(obj);
      formData.value.postStationIdList = selectPostData.value.id;
    } else {
      postOptions.value = [];
      formData.value.postStationIdList = [];
    }

    visiblePost.value = false;
  };
  return {
    handleFocusPostEvent,
    handleChangePost,
    visiblePost,
    currentRowPostSelect,
    savePost,
    selectPostData,
  };
}
