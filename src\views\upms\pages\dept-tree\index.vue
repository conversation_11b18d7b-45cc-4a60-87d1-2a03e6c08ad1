<template>
  <div class="content">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane min-size="30" size="30">
        <div class="left">
          <div class="top-button">
            <el-button
              type="primary"
              @click="addNode"
              :disabled="addDisabled"
              v-auth="['platform_dept_add']"
              :icon="Plus"
              >新增</el-button
            >
            <el-button
              @click="downLoad(false)"
              v-auth="['platform_dept_export']"
              plain
              type="primary"
              :icon="Download"
              >导出</el-button
            >
            <el-button
              @click="fileVisible = true"
              v-auth="['platform_dept_import']"
              plain
              type="primary"
              :icon="Upload"
              >导入</el-button
            >
            <el-button
              type="danger"
              @click="deleteNode(null)"
              v-auth="['platform_dept_delete']"
              plain
              :icon="Delete"
              >删除</el-button
            >
          </div>
          <div class="tree-content" style="">
            <VTreeSearch
              ref="tree"
              :expandOnFilter="true"
              draggable
              droppable
              :load="getDeptData"
              @click="handleTreeClick"
              @node-dragstart="handleStart"
              @node-drop="handleDrop"
              @node-dragleave="handleLeave"
              :beforeDropMethod="beforeDropMethod"
            >
              <template #actions>
                <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
              </template>
              <template #search-input>
                <el-input
                  class="search-input"
                  placeholder="至少3个字才能触发自动搜索"
                  v-model="searchName"
                  @input="handleSearch(0)"
                />
              </template>
              <template #node="{ node }">
                <span
                  :class="node.id == nodeId ? 'active' : ''"
                  :style="{
                    color:
                      name && node.instName.indexOf(name) > -1 ? 'rgb(166, 0, 0)' : '',
                    fontWeight: name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                  }"
                  >{{ node.instName }}</span
                >
              </template>
            </VTreeSearch>
          </div>
        </div>
      </Pane>
      <Pane class="mid" ref="mid">
        <!--右侧div内容-->
        <div class="right">
          <el-tabs v-model="activeName" class="dept-tabs-xl" @tab-click="handleClick">
            <el-tab-pane label="机构列表" name="1">
              <div v-if="nodeId || addDisabled === true">
                <basic-search
                  :searchArray="searchArray"
                  class="search-all"
                  ref="searchRef"
                  :labelShow="false"
                  @onSearch="onSearch"
                  @reset="reset"
                />
                <div class="table-content">
                  <basic-table
                    :columns="columns"
                    :data="tableData"
                    :total="param.total"
                    :page-size="param.pageSize"
                    :current-page="param.pageNum"
                    @page-change="pageChange"
                    @size-change="sizeChange"
                    @sortChange="sortChange"
                    :downSetting="true"
                    height="calc(100vh - 315px)"
                  >
                    <template #instCode="{ record }">
                      <span class="inst-code" @click="handleDetail(record)">{{
                        record.instCode
                      }}</span>
                    </template>
                    <template #action="{ record }">
                      <el-button
                        type="primary"
                        link
                        @click="handleDelete(record)"
                        v-auth="['platform_dept_delete']"
                        >删除</el-button
                      >
                      <el-button
                        type="primary"
                        link
                        @click="handleDetail(record)"
                        v-auth="['platform_dept_details']"
                        >查看</el-button
                      >
                      <el-button
                        type="primary"
                        link
                        @click="handleEdit(record)"
                        v-auth="['platform_dept_edit']"
                        >编辑</el-button
                      >
                    </template>
                  </basic-table>
                </div>
              </div>
              <div v-else class="no-form"> 未选择任何机构 </div>
            </el-tab-pane>
            <el-tab-pane label="机构信息" name="0">
              <div class="form-div" v-if="nodeId || addDisabled === true">
                <basic-form
                  class="basic-form-div"
                  labelWidth="100px"
                  :form-list="deptFormSchema"
                  :form-data="formData"
                  auth="dept_save"
                  ref="formRef"
                  :disabled="disabled"
                  :showSubmit="false"
                  :check-strictly="false"
                  :isCreate="false"
                >
                  <template #region>
                    <el-cascader
                      style="width: 100%"
                      :disabled="formData.id ? true : false"
                      :props="props"
                      v-model="regionArr"
                      @change="handleCascader"
                      :clearable="true"
                    />
                  </template>
                </basic-form>
                <div class="bottom-btn-div">
                  <el-button
                    type="primary"
                    @click="saveForm"
                    :loading="loading"
                    v-auth="['platform_dept_save']"
                  >
                    <img
                      :src="saveIcon"
                      alt="saveIcon"
                      style="
                        width: 14px;
                        height: 14px;
                        display: block;
                        margin-right: 6px;
                      "
                    />保存</el-button
                  >
                </div>
              </div>
              <div v-else class="no-form"> 未选择任何机构 </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </Pane>
    </Splitpanes>
    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
      class="file-dialog"
    >
      <el-button type="primary" link @click="downLoad(true)" style="margin-bottom: 12px"
        >下载平台机构导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, markRaw } from 'vue';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import {
    getDeptDetail,
    getDeptListApi,
    saveDept,
    delDep,
    getCodeApi,
    getDeptCodeUrlApi,
    getSortUrlApi,
    orgDownLoadApi,
    uploadApi,
    deptAsyn,
  } from '/@/views/upms/api/dept';
  import type { FormInstance } from 'element-plus';
  import '@cpit-cpvf/tree/dist/style.css';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import BasicForm from '/@/components/sys/BasicForm';
  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  import { deptFormSchema, searchArray, columns } from './dept.data';
  import {
    CircleClose,
    UploadFilled,
    Plus,
    Download,
    Upload,
    Delete,
  } from '@element-plus/icons-vue';
  import saveIcon from '/@/assets/icons/save.svg';
  // import saveIcon from './../../../../assets/icon/save.svg';
  import type { CascaderProps } from 'element-plus';
  import useUpload from '/@/hooks/upms/upload/useUpload';
  import type { UploadInstance } from 'element-plus';
  const tree = ref<InstanceType<typeof VTreeSearch> | null>(null);
  const upload = ref<UploadInstance>();
  const formRef = ref<FormInstance | null>(null);
  const loading = ref(false);
  const disabled = ref(false);
  const regionArr = ref<string[]>([]);
  const status = ref('');
  const addDisabled = ref(false);
  const editOrDetail = ref('edit');
  const uploadLoading = ref(false);
  const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const activeName = ref('0');
  const parentId = ref('');
  const newNode = ref<any>({});

  const param = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const searchRef = ref({
    instCode: '',
    instName: '',
  });
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const tableData = ref([]);
  const formData = ref<any>({
    parentInstName: '',
    parentId: '',
    instCode: '',
    parentInstCode: '',
    instName: '',
    shortName: '',
    instStatus: '0',
    instLevel: '',
    instHsClass: '',
    instHsDetailClass: '',
    stepCode: '',
    serviceCode: '',
    deptName: '',
    region: [],
    id: '',
  });
  const parentInstName = ref('');
  const parentInstCode = ref('');
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  const isChildFirst = ref(true);
  const currentIsLeaf = ref(1);
  const firstNodeId = ref(''); // 根节点最后一条数据的id
  const dragStart = ref({});
  const dragDrop = ref({});
  const dragLeave = ref({});
  const props: CascaderProps = {
    lazy: true,
    label: 'name',
    value: 'code',
    lazyLoad(node, resolve) {
      const { level, value } = node;
      getCodeData(level, value, resolve);
    },
  };
  const handleClick = async (data) => {
    tableData.value = [];
    if (data.index === '0') {
      getDeptList();
    }
  };

  const getOrgDetail = async (id: string) => {
    const res = await getDeptDetail(id);
    activeName.value = '0';
    formData.value = res;
    parentId.value = res.parentId;
    status.value = '';

    const province: string = formData.value.instCode.slice(0, 2) + '0000';
    const city: string = formData.value.instCode.slice(0, 4) + '00';
    const area: string = formData.value.instCode.slice(0, 6);
    regionArr.value = [province, city, area];
    formData.value.region = regionArr.value;
    const ruleFormRef = formRef.value && formRef.value.ruleFormRef;
    if (ruleFormRef) {
      formRef.value?.resetForm(ruleFormRef);
    }
    if (editOrDetail.value === 'edit') {
      disabled.value = false;
    } else {
      disabled.value = true;
    }
  };
  const saveForm = () => {
    loading.value = true;
    const getData = formRef.value && formRef.value?.submitForm;
    const ruleFormRef = formRef.value && formRef.value?.ruleFormRef;
    getData(ruleFormRef, (type, data) => {
      if (type === 'success') {
        // const node = ztree.value.getNodesByParam('id', data.id, null)[0];
        // node.instName = data.instName;
        // node.deptName = data.instName;
        // ztree.value.updateNode(node, false);
        saveDept(data)
          .then((res) => {
            getOrgDetail(res.id);
            regionArr.value = [];
            if (data.id) {
              // 编辑节点更新树节点名称
              tree.value.updateNode({
                instName: res.deptName,
              });
            } else if (!data.parentId) {
              // 添加一级节点
              tree.value.remove('add');
              tree.value.insertAfter(
                {
                  id: res.id,
                  instName: res.deptName,
                  isLeaf: 1,
                },
                firstNodeId.value,
              );
              nodeId.value = res.id;
            } else {
              // 添加子节点
              tree.value.remove('add');
              tree.value.append(
                {
                  id: res.id,
                  instName: res.deptName,
                  isLeaf: 1,
                },
                res.parentId,
              );
              nodeId.value = res.id;
            }
            ElMessage({
              showClose: true,
              type: 'success',
              message: !!data.id ? '修改机构成功' : '新增机构成功',
            });
            addDisabled.value = false;
            status.value = '';
            loading.value = false;
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };
  const addNode = () => {
    status.value = 'add';
    const parentForm = formData.value;
    if (parentForm.id) {
      // 非根节点
      const parentForm = formData.value;
      if (isChildFirst.value) {
        tree.value.updateNode(parentForm.id, {
          children: [
            {
              id: 'add',
              instName: '未命名',
              parentInstName: parentForm.deptName,
              parentInstCode: parentForm.instCode || '',
              isLeaf: 1,
            },
          ],
        });
      }
      tree.value.setExpand(parentForm.id, true);
    } else {
      // 根节点
      tree.value.insertAfter(
        {
          id: 'add',
          parentId: '',
          instName: '未命名',
          parentInstName: '',
          isLeaf: 1,
        },
        firstNodeId.value,
      );
    }
    // 先存一下父节点详情
    parentId.value = nodeId.value || '';
    parentInstName.value = parentForm.deptName || '';
    nodeId.value = 'add';
    regionArr.value = [];
    addDisabled.value = true;
  };

  const deleteNode = (id) => {
    if (nodeId.value) {
      ElMessageBox.confirm('确认删除该机构？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'error',
        icon: markRaw(CircleClose),
        customStyle: {
          backgroundColor: 'rgba(255, 241, 240, 1)',
          border: '1px soild rgba(255, 163, 158, 1)',
        },
      })
        .then(() => {
          if (id === 'add' || formData.value.id === '') {
            // 未调用添加接口的节点
            tree.value.remove(nodeId.value);
            nodeId.value = '';
            addDisabled.value = false;
          } else {
            delDep(!!id ? id : nodeId.value).then(() => {
              ElMessage({
                message: '删除成功',
                grouping: true,
                type: 'success',
              });
              // 删除成功后调用
              tree.value.remove(nodeId.value);
              nodeId.value = '';
            });
          }
          status.value = '';
        })
        .catch(() => {});
    } else if (!nodeId.value && JSON.stringify(newNode.value) === '{}') {
      ElMessage({
        type: 'warning',
        showClose: true,
        message: '请先选择机构节点在进行删除操作！',
      });
    }
  };
  const getDeptList = async () => {
    tableData.value = [];
    const params = {
      ...searchRef.value?.['searchValue'],
      ...param.value,
      parentInstCode: formData.value.instCode,
      ...sortData.value,
    };
    const res = await getDeptListApi(params);
    const { list, total, currentPage, pageSize } = res;
    tableData.value = list;
    param.value = {
      total,
      pageNum: currentPage,
      pageSize,
    };
  };
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    getDeptList();
  };
  const onSearch = () => {
    getDeptList();
  };

  const reset = () => {
    searchRef.value = {
      instCode: '',
      instName: '',
    };
    param.value = {
      total: 0,
      pageNum: 1,
      pageSize: 10,
    };
    getDeptList();
  };
  const pageChange = (val: number) => {
    param.value.pageNum = val;
    getDeptList();
  };
  const sizeChange = (val: number) => {
    param.value.pageSize = val;
    getDeptList();
  };
  const handleCascader = (data) => {
    formData.value.region = data;
    const params = {
      parentInstCode: formData.value.parentInstCode,
      code: data[2],
    };
    getDeptCode(params);
  };

  const getDeptCode = async (params) => {
    const res = await getDeptCodeUrlApi(params);
    formData.value.instCode = res;
  };
  const getCodeData = async (level, value, resolve) => {
    let obj = {};
    if (level === 0) {
      obj = {};
    } else if (level === 1) {
      obj = {
        provinceCode: value,
      };
    } else if (level === 2) {
      obj = {
        cityCode: value,
      };
    }

    if (level > 2) {
      resolve([]);
    } else {
      const res = await getCodeApi(obj);
      const nodes = res;
      nodes.forEach((item) => {
        item.leaf = level >= 2;
      });
      resolve(nodes);
    }
  };

  const handleDelete = (item) => {
    deleteNode(item.id);
  };
  const handleEdit = (item) => {
    tree.value.setChecked(item.id);
    nodeId.value = item.id;
    parentId.value = item.parentId;
    parentInstName.value = item.parentInstName;
    parentInstCode.value = item.parentInstCode;
    editOrDetail.value = 'edit';
  };
  const handleDetail = (item) => {
    tree.value.setChecked(item.id);
    nodeId.value = item.id;
    parentId.value = item.parentId;
    parentInstName.value = item.parentInstName;
    parentInstCode.value = item.parentInstCode;
    editOrDetail.value = 'detail';
  };

  function downLoad(isTemplate) {
    orgDownLoadApi({ id: formData.value.id.toString(), isTemplate }).then((res) => {
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
      });
      const fileName = '平台机构管理' + '.xlsx';
      const elink = document.createElement('a'); // 创建a标签
      elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click(); // 点击下载
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink); // 释放标签
    });
  }
  const uploadFile = () => {
    uploadLoading.value = true;
    let form = new FormData();
    form.append('file', fileData.value?.raw);
    form.append('targetId', formData.value?.id ? formData.value.id.toString() : '');
    form.append(
      'targetInstCode',
      formData.value?.instCode ? formData.value.instCode.toString() : '',
    );
    uploadApi(form).then(async () => {
      fileVisible.value = false;
      tree.value.setExpand(formData.value.id, true);
      getDeptData(formData.value);
      ElMessage({
        type: 'success',
        message: '导入成功',
      });
      uploadLoading.value = false;
    })
    .catch(() => {
      uploadLoading.value = false;
    });
  };
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    })
      .then((res) => {
        if (resolve) {
          if (status.value == 'add' && node.isLeaf) {
            // 选中节点->点击新增按钮，并且当前节点下没有子节点
            const parentForm = formData.value;
            resolve([
              {
                id: 'add',
                instName: '未命名',
                parentInstName: parentForm.deptName,
                parentInstCode: parentForm.instCode || '',
                isLeaf: 1,
              },
            ]);
            tree.value.setExpand(parentForm.id, true);
            status.value = '';
          } else if (status.value == 'add' && !node.isLeaf) {
            const parentForm = formData.value;
            res.push({
              id: 'add',
              instName: '未命名',
              parentInstName: parentForm.deptName,
              parentInstCode: parentForm.instCode || '',
              isLeaf: 1,
            });
            resolve(res);
            status.value = '';
          } else {
            resolve(res);
          }
        } else {
          // 直接调用方法获取节点数据（排序失败、导入数据场景）
          setTimeout(() => {
            if (name.value) {
              // 只要有name就返回的所有数据 处理搜索后再排序场景
              tree.value.loadRootNodes(res);
            } else if (node?.id) {
              tree.value.updateNode(node.id, { children: res });
            } else {
              tree.value.loadRootNodes(res);
            }
          }, 0);
        }
        // 搜索展开所有节点
        if (name.value) {
          setTimeout(() => {
            tree.value.setExpandAll(true);
          }, 0);
        }
        // 一级树最后一个节点id
        if (res.length > 0 && !node) {
          firstNodeId.value = res[res.length - 1].id;
          nodeId.value = res[0].id;
          isChildFirst.value = res[0].isLeaf;
        }
      })
      .catch(() => {
        name.value = '';
        getDeptData();
      });
  };
  const handleTreeClick = (e) => {
    // !e.isLeaf && !name.value 非搜索状态下的叶子节点
    // e.children && e.children.length>0 搜索状态下判断children
    if (
      (e.children && e.children.length > 0 && name.value) ||
      (!e.isLeaf && !name.value)
    ) {
      isChildFirst.value = false;
    } else {
      isChildFirst.value = true;
    }
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      currentIsLeaf.value = e.isLeaf;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    addDisabled.value = false;
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  const handleStart = (e) => {
    dragStart.value = e;
  };
  const handleLeave = (e) => {
    dragLeave.value = e;
  };
  const beforeDropMethod = () => {
    if (dragLeave.value._level == dragStart.value._level) {
      if (
        dragLeave.value._level === 0 ||
        dragLeave.value._parent.id == dragStart.value._parent.id
      ) {
        return true;
      } else {
        ElMessage({
          showClose: true,
          type: 'warning',
          message: '只允许在同一个父机构节点下排序！',
        });
        return false;
      }
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '只允许在同一个父机构节点下排序！',
      });
      return false;
    }
  };
  const handleDrop = (e) => {
    dragDrop.value = e;
    let type = '';
    if (dragStart.value.sort > dragDrop.value.sort) {
      type = 'prev';
    }
    if (dragStart.value.sort < dragDrop.value.sort) {
      type = 'next';
    }
    getSortUrlApi({
      id: dragStart.value.id,
      targetId: dragDrop.value.id,
      type,
    })
      .then(() => {
        getDeptData(dragStart.value._parent);
      })
      .catch(() => {
        getDeptData(dragStart.value._parent);
      });
  };
  watch(
    () => nodeId.value,
    (val) => {
      if (val) {
        if (val == 'add') {
          formData.value = {
            parentId: parentId.value,
            parentInstName: parentInstName.value,
            instCode: '',
            parentInstCode: parentInstCode.value,
            instName: '',
            shortName: '',
            instStatus: '0',
            instLevel: '',
            instHsClass: '',
            instHsDetailClass: '',
            stepCode: '',
            serviceCode: '',
            deptName: '',
            id: '',
            region: [],
            isLeaf: 1,
          };
          regionArr.value = [];
        } else {
          getOrgDetail(val);
        }
      } else {
        formData.value = {
          parentId: parentId.value,
          parentInstName: parentInstName.value,
          instCode: '',
          parentInstCode: parentInstCode.value,
          instName: '',
          shortName: '',
          instStatus: '0',
          instLevel: '',
          instHsClass: '',
          instHsDetailClass: '',
          stepCode: '',
          serviceCode: '',
          deptName: '',
          id: '',
          region: [],
          isLeaf: 1,
        };
        regionArr.value = [];
      }
      const ruleFormRef = formRef.value && formRef.value.ruleFormRef;
      if (ruleFormRef) {
        formRef.value?.resetForm(ruleFormRef);
      }
    },
  );
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;

    .mid {
      height: 100%;
      // box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }

  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: calc(100vh - 180px);
      overflow-x: hidden;
      padding-left: 10px;
      // .search-input {
      //   width: calc(100% - 70px)
      // }
      :deep(.vtree-tree-search__search) {
        width: 100%;
        display: flex;
      }
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      height: 57px;
      .el-input {
        padding: 12px 20px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 48px);
      overflow-y: auto;
      overflow-x: hidden;
    }
    .active {
      background-color: #c9e9f7;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #fff;
    border-radius: 6px;
    .table-content {
      // margin-top: 16px;
      // background: #ffffff;
      // height: calc(100% - 180px);
      padding: 0 20px;
      // position: relative;
      overflow: auto;
      // border-radius: 6px;
      .top-button {
        // position: absolute;
        // top: 10px;
        // left: 16px;
        z-index: 9;
        .upload-btn {
          display: inline-block;
          margin: 0 9px;
        }
      }
    }
  }
  .top-button {
    padding: 8px 15px 8px 15px;
    border-bottom: 1px solid #e4e7ed;
    // height: 50px;
    .upload-btn {
      display: inline-block;
      margin: 0 12px;
    }
  }

  .inst-code {
    color: var(--el-color-primary);
    cursor: pointer;
  }
  .form-div {
    width: 100%;
    height: 100%;
    .basic-form-div {
      height: calc(100vh - 236px);
      border-bottom: 1px solid #e4e7ed;
      overflow: auto;
    }
    .bottom-btn-div {
      display: flex;
      justify-content: flex-end;
      padding-right: 18px;
      padding-top: 14px;
    }
  }
  .no-form {
    width: 100%;
    height: 100%;
    line-height: 100%;
    padding-top: 120px;
    font-size: 24px;
  }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__header {
    margin: 0 15px 15px 15px;
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }

  .file-dialog .el-dialog__body {
    padding: 20px;
  }

  .el-tabs__nav-wrap.is-top {
    height: 52px;
  }

  .el-tabs__nav-scroll {
    height: 100%;
  }

  .el-tabs__nav.is-top {
    height: 100%;
  }

  .el-tabs__item {
    padding: 25px 20px;
  }
</style>
