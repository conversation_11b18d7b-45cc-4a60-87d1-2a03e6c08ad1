import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export type AccountParams = BasicPageParams & {
  deptId?: string;
  userName?: string;
  email?: string;
  phone?: number;
  opType?: string;
  name?: string;
  username?: string;
  telephone?: string;
  instCode?: string;
  empName?: string;
  industryType?: string;
  instName?: string;
  empCode?: string;
  status?: string;
  sub?: string; // 搜索是否包含下级 1 包含 0 不包含
  available?: string | number;
  employeeName?: string;
  selfDeptId?: string;
  selfInstCode?: string;
};

export interface AccountListItem {
  available: string;
  availableText: string;
  avatar: string;
  createdBy: string;
  createdDate: string;
  creatorName: string;
  delFlag: string;
  deptId: string;
  deptName: string;
  description: string;
  email: string;
  id: string;
  lastModifiedBy: string;
  lastModifiedDate: string;
  lastModifierName: string;
  nickName: string;
  pageNum: number;
  pageSize: number;
  parentDeptId: string;
  password: string;
  phone: string;
  qqOpenId: string;
  roleIdList: string[];
  roleList: [];
  roleNames: string;
  type: string;
  typeText: string;
  userName: string;
  version: number;
  wxOpenId: string;
}

export interface EditAccountParams {
  available?: string;
  availableText?: string;
  avatar?: string;
  createdBy?: string;
  createdDate?: string;
  creatorName?: string;
  delFlag?: string;
  deptId?: string;
  deptName?: string;
  description?: string;
  email?: string;
  id?: string;
  lastModifiedBy?: string;
  lastModifiedDate?: string;
  lastModifierName?: string;
  nickName?: string;
  parentDeptId?: string;
  password?: string;
  phone?: string;
  qqOpenId?: string;
  roleIdList?: string[];
  roleList?: [];
  roleNames?: string;
  type?: string;
  typeText?: string;
  userName?: string;
  username?: string;
  version?: number;
  wxOpenId?: string;
}
export interface ResetParams {
  confirmPassword?: string;
  id: string;
  password?: string;
  sendEmail: boolean;
  sendSMS: boolean;
  userName?: string;
  username?: string;
}

/**
 * @description: Request list return value
 */
export type AccountListGetResultModel = BasicFetchResult<AccountListItem> & {
  list?: [];
  pageNum: number;
  pageSize: number;
  currentPage: number;
  total: number;
  records?: [];
};

export interface roleOption {
  label: string;
  value: string;
  disabled?: boolean;
}
export interface tableItem {
  id?: string | undefined;
  employeeId?: string;
  available?: string;
  orgId?: string;
  dept?: string[] | string | undefined;
  deptId?: string[] | undefined | string;
  deptName?: string | undefined | string[];
  description?: string;
  nameJianpin?: string;
  email?: string;
  nickName?: string;
  password?: string;
  userPasswd?: string;
  phone?: string;
  roleIdList?: string[];
  userName?: string;
  username?: string;
  organization?: string[];
  organizationId?: string[];
  postStationIdList?: string[] | string;
  telephone?: string;
  name?: string;
  gender?: string;
  empType?: string;
  roleNames?: string | undefined;
  organizationName?: string | string[];
  postStationNames?: string | string[];
  industryType?: string;
  empName?: string;
  instCode?: string[] | string;
  instName?: string[] | string;
  empCode?: string;
  empPost?: string;
  nodeCode?: string;
  serviceCode?: string;
  sortOrder?: string;
  status?: string;
  opType?: string;
  version?: number;
}
export interface tableItemMain {
  id: string;
  employeeId?: string[];
  available?: string;
  orgId?: string;
  dept?: string[];
  deptId?: string[];
  deptName?: string[];
  description?: string;
  nameJianpin?: string;
  email?: string;
  nickName?: string;
  userPasswd?: string;
  phone?: string;
  roleIdList: string[];
  userName?: string;
  organization?: string[];
  organizationId?: string[];
  postStationIdList?: string[];
  telephone?: string;
  name?: string;
  gender?: string;
  empType?: string;
  roleNames?: string[];
  organizationName?: string[];
  postStationNames?: string[];
  employeeName?: string[];
}
export interface resetPwdType {
  id: string;
  userName?: string;
  username?: string;
  sendEmail: string[];
  sendSMS: string[];
  userPasswd: string;
}

//部门机构
export interface DeptItem {
  id: string;
  label: string;
  parentId: string;
  children: DeptItem[];
}
export type DeptResultModel = DeptItem[];

export interface TreeItem {
  id?: string;
  label: string;
  parentId?: string;
  children?: TreeItem[];
}

export interface RoleParams {
  opType?: string;
}

export interface PostListModel {
  id: string;
}

export interface PostForm {
  label: string;
  value: string;
}
