import { FormOptions } from '/@/components/sys/BasicForm/types';
import { getDictStorage } from '/@/utils/storage/dict';
export const deptFormSchema: FormOptions[] = [
  {
    field: 'parentInstName',
    label: '上级机构名称',
    component: 'Input',
    componentProps: {
      placeholder: '无',
      disabled: true,
    },
  },
  {
    label: '行政区划',
    field: 'region',
    component: 'Input',
    componentProps: {
      placeholder: '请输入行政区划',
      clearable: true,
    },
    slot: 'region',
    required: true,
  },
  {
    field: 'instCode',
    label: '机构编码',
    component: 'Input',
    componentProps: {
      placeholder: '请输入机构名称',
      disabled: true,
    },
    rules: [{ max: 50, message: '超过长度限制，最多50字' }],
    required: true,
  },
  {
    field: 'instName',
    label: '机构名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入机构名称',
    },
    rules: [{ max: 50, message: '超过长度限制，最多50字' }],
    required: true,
  },
  {
    field: 'shortName',
    label: '机构简称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入机构简称',
    },
    rules: [{ max: 30, message: '超过长度限制，最多30字' }],
    required: true,
  },

  {
    label: '机构层级',
    field: 'instLevel',
    component: 'Select',
    componentProps: {
      placeholder: '请选择机构层级',
      options: getDictStorage()?.biz_inst_level || [],
      clearable: true,
    },
    // required: true,
    rules: [{ max: 20, message: '超过长度限制，最多20字', trigger: 'blur' }],
  },
  {
    label: '机构大类',
    field: 'instHsClass',
    component: 'Select',
    componentProps: {
      placeholder: '请选择机构大类',
      options: getDictStorage()?.biz_hs_class || [],
      clearable: true,
    },
    rules: [{ max: 20, message: '超过长度限制，最多20字', trigger: 'blur' }],
  },
  {
    label: '机构小类',
    field: 'instHsDetailClass',
    component: 'Select',
    componentProps: {
      placeholder: '请选择机构小类',
      options: getDictStorage()?.biz_hs_detail_class || [],
      clearable: true,
    },
    rules: [{ max: 20, message: '超过长度限制，最多20字', trigger: 'blur' }],
  },
  {
    label: '环节',
    field: 'stepCode',
    component: 'Select',
    componentProps: {
      placeholder: '请选择环节',
      options: getDictStorage()?.biz_step_code || [],
      clearable: true,
    },
    rules: [{ max: 20, message: '超过长度限制，最多20字', trigger: 'blur' }],
  },
  {
    label: '专业',
    field: 'serviceCode',
    component: 'Select',
    componentProps: {
      placeholder: '请选择专业',
      options: getDictStorage()?.biz_service_code || [],
      clearable: true,
    },
    rules: [{ max: 20, message: '超过长度限制，最多20字', trigger: 'blur' }],
  },
  {
    label: '机构状态',
    field: 'instStatus',
    component: 'RadioGroup',
    componentProps: {
      options: getDictStorage()?.biz_inst_status || [],
    },
    required: true,
    rules: [{ max: 20, message: '超过长度限制，最多20字', trigger: 'blur' }],
  },
];
export const searchArray: any[] = [
  {
    field: 'instCode',
    label: '机构编码',
    component: 'Input',
    placeholder: '请输入机构编码',
    colProps: { span: 12 },
    componentProps: {
      clearable: true,
    },
    span: 8,
    labelWidth: 80,
  },
  {
    field: 'instName',
    label: '机构名称',
    component: 'Input',
    placeholder: '请输入机构名称',
    colProps: { span: 12 },
    componentProps: {
      clearable: true,
    },
    span: 8,
    labelWidth: 80,
  },
];
export const columns: any[] = [
  {
    label: '机构编码',
    prop: 'instCode',
    width: 120,
    align: 'center',
    slot: 'instCode',
  },
  {
    label: '机构名称',
    prop: 'instName',
    hideColumn: false,
    align: 'center',
    sortable: 'custom',
  },
  {
    label: '机构简称',
    prop: 'shortName',
    align: 'center',
    sortable: 'custom',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    width: 160,
  },
];
