<template>
  <div class="content">
    <div class="box">
      <div class="search-div">
        <p class="title"> 项目角色搜索 </p>
        <basic-search
          :searchArray="searchFormSchema"
          class="search-all"
          ref="searchRef"
          :labelWidth="80"
          :labelShow="false"
          @reset="getRoleList"
          @onSearch="getRoleList"
        />
      </div>
      <div class="table-content">
        <div class="table-top-div">
          <p>项目角色列表</p>
          <div class="top-button">
            <el-button
              type="primary"
              @click="handleAdd"
              v-auth="['project_role_add']"
              :icon="Plus"
              >新增</el-button
            >
            <el-button
              type="primary"
              @click="downLoad"
              v-auth="['project_role_export']"
              :icon="Download"
              plain
              >导出</el-button
            >
            <el-button
              type="primary"
              @click="handlefileVisible"
              v-auth="['project_role_import']"
              :icon="Upload"
              plain
              >导入</el-button
            >
            <el-button
              type="danger"
              @click="batchDeletion"
              :disabled="selectionIds.length > 0 ? false : true"
              v-auth="['project_role_delete']"
              :icon="Delete"
              plain
              >删除</el-button
            >
          </div>
        </div>
        <basic-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :total="page.total"
          :page-size="page.pageSize"
          :current-page="page.pageNum"
          @page-change="pageChange"
          @size-change="sizeChange"
          @selectionChange="handleCurrentChange"
          :downSetting="true"
          height="calc(100vh - 392px)"
        >
          <template #available="{ record }">
            <span v-if="record.available === '1'"
              ><span class="active-span"></span> 启用</span
            >
            <span v-else type="danger"> <span class="close-span"></span> 禁用</span>
          </template>
          <template #builtIn="{ record }">
            <el-tag v-if="record.builtIn === 1" type="danger">系统内置</el-tag>
            <el-tag v-else>自定义</el-tag>
          </template>
          <template #action="{ record }">
            <el-button
              type="primary"
              link
              @click="handleDelete(record, '0')"
              v-if="record.builtIn !== 1"
              v-auth="['project_role_delete']"
              >删除</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleEdit(record)"
              v-auth="['project_role_edit']"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleDetail(record)"
              v-auth="['project_role_details']"
              >查看</el-button
            >
            <!-- <el-button type="primary" link @click="handleStatus(record)">{{
              record.available === '1' ? '禁用' : '启用'
            }}</el-button> -->
            <el-button
              type="primary"
              link
              @click="handleStatus(record)"
              v-show="record.available == 1 && record.builtIn !== 1"
              v-auth="['project_role_enable_disable']"
              >禁用</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleStatus(record)"
              v-show="record.available != 1"
              v-auth="['project_role_enable_disable']"
              >启用</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleMenu(record)"
              v-if="record.available === '1'"
              v-auth="['project_role_menu']"
              >菜单权限</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleData(record)"
              v-if="record.available === '1'"
              v-auth="['project_role_data']"
              >数据权限</el-button
            >
          </template>
        </basic-table>
      </div>
    </div>

    <el-dialog v-model="dialogVisible" :title="title" width="80%" destroy-on-close>
      <add-role
        :data="savaData"
        :open="dialogVisible"
        ref="roleDataRef"
        :disabled="!isEdit"
        @menu-check="menuSelect"
        @dept-check="deptSelect"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="validData" :loading="roleLoading"
            >保存</el-button
          >
        </span>
      </template>
    </el-dialog>
    <el-drawer
      v-model="drawerMenu"
      direction="rtl"
      size="600px"
      :destroy-on-close="true"
    >
      <template #header>
        <h4>{{ menuTitle }}</h4>
      </template>
      <template #default>
        <menu-auth
          ref="menuAuthRef"
          :menuIds="savaData.menuIdList"
          :roleId="savaData.id"
          @checkboxIds="getMenuIds"
          @close="savaData.menuIdList = []"
        />
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawerMenu = false">取消</el-button>
          <el-button type="primary" @click="editMenu" :loading="menuLoading"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>

    <el-drawer
      v-model="drawerData"
      direction="rtl"
      size="600px"
      :destroy-on-close="true"
    >
      <template #header>
        <h4>{{ dataTitle }}</h4>
      </template>
      <template #default>
        <data-auth
          ref="dataAuthRef"
          :deptIds="savaData.deptIdList"
          :dataScope="savaData.dataScope"
          @dataIdsAndType="getDataIdsAndType"
        />
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawerData = false">取消</el-button>
          <el-button type="primary" @click="editData" :loading="dataLoading"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>
    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
    >
      <el-button type="primary" link @click="downLoad" style="margin-bottom: 12px"
        >下载项目角色导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="batchConfirmationVisible"
      title="删除警告"
      width="400px"
      class="batch-Confirmation-dialog"
      :destroy-on-close="true"
    >
      <BatchConfirmation @getPassword="getPassword" />
      <template #footer>
        <el-button @click="batchConfirmationVisible = false">取消</el-button>
        <el-button
          type="info"
          @click="batchConfirmationSave"
          :loading="batchConfirmationLoading"
          >删除</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, markRaw } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { CircleClose, Plus, Download, Upload, Delete } from '@element-plus/icons-vue';
  import { columns, searchFormSchema } from './role.data';
  import BasicTable from '/@/components/sys/BasicTable';
  import AddRole from './AddRole.vue';
  import menuAuth from './menuAuth.vue';
  import dataAuth from './dataAuth.vue';
  import {
    getDetail,
    getRoleListByPage,
    saveRole,
    updateMenuApi,
    updateDataApi,
    getRoleLockOrUnLock,
    delIdsApi,
    downLoadApi,
    uploadApi,
  } from '/@/views/upms/api/roleSystem';
  import {
    RoleListItem,
    RolePageListGetResultModel,
    SaveRoleItem,
  } from '/@/views/upms/api/model/roleSystemModel';
  import useUpload from '/@/hooks/upms/upload/useUpload';
  import type { UploadInstance } from 'element-plus';

  import BatchConfirmation from '/@/views/upms/components/BatchConfirmation.vue';
  import userBatchConfirmation from '/@/views/upms/common/hooks/userBatchConfirmation';
  import { getAuthStorage, setAuthStorage } from '/@/utils/storage/auth';
  import { userPermission } from '/@/views/upms/api/system';
  const userInfo = getAuthStorage();
  const upload = ref<UploadInstance>();
  const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const roleDataRef = ref<InstanceType<typeof AddRole>>();
  const menuAuthRef = ref<HTMLDivElement | null>(null);
  const dataAuthRef = ref<HTMLDivElement | null>(null);
  const tableData = ref<RoleListItem[]>([]);
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });

  const searchRef = ref();
  const dialogVisible = ref(false);
  const isEdit = ref(true);
  const title = ref('新增');
  const selectionIds = ref<string[]>([]);
  const initSaveData = {
    isEdit: false,
    code: '', // 角色编码
    name: '', // 角色名称
    remark: '', // 描述
    dataScope: '1', // 数据权限code
    available: '1', // 状态
    description: '', // 描述
    deptIdList: [], // 机构id
    menuIdList: [], // 菜单id
  };
  const savaData = ref<SaveRoleItem>(initSaveData);

  const drawerMenu = ref(false);
  const menuTitle = ref('角色权限配置');
  const drawerData = ref(false);
  const dataTitle = ref('数据权限配置');

  const menuLoading = ref(false);
  const roleLoading = ref(false);
  const dataLoading = ref(false);
  const uploadLoading = ref(false);

  const getRoleList = () => {
    const { pageNum, pageSize } = page.value;
    const { code, name, available } = searchRef?.value?.['searchValue'] || {};
    getRoleListByPage({
      code,
      name,
      available,
      pageNum,
      pageSize,
      opType: 'project',
    }).then((res: RolePageListGetResultModel) => {
      tableData.value = res.list;
      page.value.total = res.total;
    });
  };
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    getRoleList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    getRoleList();
  };
  getRoleList();
  const handleAdd = () => {
    if (!!userInfo.projectId) {
      savaData.value = {
        isEdit: false,
        code: '', // 角色编码
        name: '', // 角色名称
        remark: '', // 描述
        dataScope: '1', // 数据权限code
        available: '1', // 状态
        description: '', // 描述
        deptIdList: [], // 机构id
        menuIdList: [], // 菜单id
      };
      isEdit.value = true;
      dialogVisible.value = true;
      title.value = '新增角色';
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择项目',
      });
    }
  };

  const handleEdit = (item) => {
    isEdit.value = true;
    title.value = '编辑角色';
    getDetail(item.id).then((res) => {
      savaData.value = res;
      savaData.value.isEdit = true;
      dialogVisible.value = true;
    });
  };
  const handleDetail = (item) => {
    isEdit.value = false;
    title.value = '查看角色';
    getDetail(item.id).then((res) => {
      savaData.value = res;
      savaData.value.isEdit = true;
      dialogVisible.value = true;
    });
  };
  const handleStatus = (item) => {
    // 禁用操作
    if (item.available === '1') {
      ElMessageBox.confirm('是否要禁用该角色', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
        customStyle: {
          backgroundColor: 'rgba(255, 241, 240, 1)',
          border: '1px soild rgba(255, 163, 158, 1)',
        },
      })
        .then(() => {
          item.available = '0';
          getRoleLockOrUnLock(item.id)
            .then(() => {
              ElMessage({
                type: 'success',
                message: '禁用成功',
              });
              getRoleList();
            })
            .catch(() => {
              item.available = '1';
            });
        })
        .catch(() => {});
    } else {
      ElMessageBox.confirm('是否要启用该角色', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'success',
      })
        .then(() => {
          item.available = '1';
          getRoleLockOrUnLock(item.id).then(() => {
            ElMessage({
              type: 'success',
              message: '启用成功',
            });
            getRoleList();
          });
        })
        .catch(() => {
          item.available = '0';
        });
    }
  };

  const handleDelete = (item, type) => {
    // type 1 批量删除  0 单独删除
    let title = '';
    if (type === 1) {
      title = `确认删除当前所选中${selectionIds.value.length}条角色数据？`;
    } else {
      title = '确认删除该角色？';
    }
    ElMessageBox.confirm(title, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        let params = {};
        if (item === null) {
          params = selectionIds.value;
        } else {
          params = [item.id];
        }
        delIdsApi({ ids: params }).then(() => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '角色删除成功',
          });
          if (type === 1) {
            selectionIds.value = [];
            tableRef.value!.clearSelection();
          }
          getRoleList();
        });
      })
      .catch(() => {});
  };

  const callDelete = (params) => {
    delIdsApi({ ids: params }).then(() => {
      ElMessage({
        showClose: true,
        type: 'success',
        message: '角色删除成功',
      });
      selectionIds.value = [];
      tableRef.value!.clearSelection();
      getRoleList();
    });
  };
  const {
    batchConfirmationVisible,
    batchDeletion,
    batchConfirmationSave,
    getPassword,
    batchConfirmationLoading,
  } = userBatchConfirmation(selectionIds, callDelete);

  const handleCurrentChange = (val) => {
    selectionIds.value = [];
    for (let i = 0; i < val.length; i++) {
      selectionIds.value.push(val[i].id);
    }
  };

  function menuSelect(value: string[]) {
    savaData.value.menuIdList = value;
  }

  function deptSelect(value: string[]) {
    savaData.value.deptIdList = value;
  }

  function validData() {
    roleLoading.value = true;
    roleDataRef.value?.validData(saveDataFn);
  }
  /**
   * 保存数据 将 saveData 作为 回调函数，传递给roleDataRef.value?.validData(saveData);
   * @param type success | fail
   * @param data 表单数据
   */
  function saveDataFn(type: 'success' | 'fail', data: any) {
    if (type === 'success') {
      Object.assign(savaData.value, data);
      saveRole({ ...savaData.value, opType: 'project' })
        .then(async () => {
          getRoleList();
          ElMessage({
            type: 'success',
            showClose: true,
            message: savaData.value.id ? '修改角色成功' : '新增角色成功',
          });
          dialogVisible.value = false;
          roleLoading.value = false;
        })
        .catch(() => {
          roleLoading.value = false;
        });
    }
  }

  const handleMenu = (item) => {
    drawerMenu.value = true;
    getDetail(item.id).then((res) => {
      savaData.value = res;
    });
  };

  const editMenu = () => {
    menuLoading.value = true;
    menuAuthRef.value?.getCheckboxIds();
  };
  const getuserPermission = (userInfo) => {
    userPermission().then((res) => {
      userInfo.permissionMap = res;
      setAuthStorage(userInfo);
    });
  };
  const getMenuIds = (ids) => {
    const params = {
      id: savaData.value.id,
      menuIdList: ids,
    };
    updateMenuApi(params)
      .then(() => {
        drawerMenu.value = false;
        menuLoading.value = false;
        let userInfo = getAuthStorage();
        userInfo.sysRoleList.map((item) => {
          if (item.code == savaData.value.code) {
            getuserPermission(userInfo);
          }
        });
      })
      .catch(() => {
        menuLoading.value = false;
      });
  };
  const handleData = (item) => {
    drawerData.value = true;
    getDetail(item.id).then((res) => {
      savaData.value = res;
    });
  };
  const getDataIdsAndType = (params) => {
    const data = {
      id: savaData.value.id,
      dataScope: params.dataScope,
      deptIdList: params.dataScope === '5' ? params.ids : null,
      opType: 'tenant',
    };
    updateDataApi(data)
      .then(() => {
        drawerData.value = false;
        dataLoading.value = false;
      })
      .catch(() => {
        dataLoading.value = false;
      });
  };
  const editData = () => {
    dataLoading.value = true;
    dataAuthRef.value?.getData();
  };

  const downLoad = () => {
    if (!!userInfo.projectId) {
      const { code, name, available } = searchRef?.value?.['searchValue'] || {};
      const params = {
        code,
        name,
        available,
        opType: 'project',
      };
      downLoadApi(params).then((res) => {
        const blob = new Blob([res.data], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
        });
        const fileName = '项目角色管理' + '.xlsx';
        const elink = document.createElement('a'); // 创建a标签
        elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
        elink.style.display = 'none';
        elink.href = URL.createObjectURL(blob);
        document.body.appendChild(elink);
        elink.click(); // 点击下载
        console.log(elink.href);
        URL.revokeObjectURL(elink.href); // 释放URL 对象
        document.body.removeChild(elink); // 释放标签
      });
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择项目',
      });
    }
  };
  const handlefileVisible = () => {
    if (!!userInfo.projectId) {
      fileVisible.value = true;
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择项目',
      });
    }
  };
  const uploadFile = () => {
    uploadLoading.value = true;
    let formData = new FormData();
    formData.append('file', fileData.value?.raw);
    formData.append('opType', 'project');
    uploadApi(formData)
      .then(() => {
        ElMessage({
          type: 'success',
          message: '导入成功',
        });
        fileVisible.value = false;
        getRoleList();
        uploadLoading.value = false;
      })
      .catch(() => {
        uploadLoading.value = false;
      });
  };
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);

    // .title {
    //   border-bottom: 1px solid #e4e7ed;
    //   line-height: 68px;
    //   font-size: 18px;
    //   padding-left: 24px;
    //   background: #ffffff;
    //   font-weight: 700;
    // }

    // .search-all {
    //   background: #ffffff;
    //   padding-left: 12px;
    // }
    .search-div {
      border-radius: 6px;
      background: #fff;
      // padding: 10px 16px;
      .title {
        // border-bottom: 1px solid #e4e7ed;
        // line-height: 68px;

        font-size: 18px;
        padding: 20px 0 6px 20px;
        // background: #ffffff;
        font-weight: 700;
        color: #333333;
      }
    }
    .table-top-div {
      display: flex;
      justify-content: space-between;
      // line-height: 64px;
      margin-bottom: 20px;
      p {
        font-size: 18px;
        color: #333333;
        font-weight: 700;
        margin: 0;
        padding: 0;
        line-height: 32px;
      }
    }
    .table-content {
      margin-top: 20px;
      background: #ffffff;
      // height: calc(100% - 180px);
      padding: 20px 20px 0 20px;
      position: relative;
      overflow: auto;
      .top-button {
        // position: absolute;
        // top: 10px;
        // left: 24px;
        z-index: 9;
        .upload-btn {
          display: inline-block;
          margin: 0 12px;
        }
      }
      .tip {
        position: absolute;
        top: 22px;
        right: 70px;
        z-index: 9;
        color: #409eff;
      }

      .active-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #00a854;
        margin-right: 5px;
      }
      .close-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #bfbfbf;
        margin-right: 5px;
      }
    }
  }

  h4 {
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 700;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
</style>
