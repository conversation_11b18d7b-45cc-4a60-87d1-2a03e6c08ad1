<template>
  <div class="content-model">
    <div class="box">
      <div class="tree-content">
        <div class="tree-div">
          <VTreeSearch
            ref="tree"
            selectable
            :load="getDeptData"
            @click="handleTreeClick"
          >
            <template #actions>
              <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
            </template>
            <template #search-input>
              <el-input
                class="search-input"
                placeholder="至少3个字才能触发自动搜索"
                v-model="searchName"
                @input="handleSearch(0)"
              />
            </template>
            <template #node="{ node }">
              <span
                :class="node.id == nodeId ? 'active' : ''"
                :style="{
                  color:
                    name && node.instName.indexOf(name) > -1 ? 'rgb(166, 0, 0)' : '',
                  fontWeight: name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                }"
                >{{ node.instName }}</span
              >
            </template>
          </VTreeSearch>
        </div>
      </div></div
    >
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, nextTick } from 'vue';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import { deptAsyn } from '/@/views/upms/api/dept';
  let emits = defineEmits(['currentOrgSelect']);
  const tree = ref<InstanceType<typeof VTreeSearch> | null>(null);
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  const currentNode = (id) => {
    nextTick(() => {
      setTimeout(() => {
        nodeId.value = id;
      }, 1000);
    });
  };
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      // currentIsLeaf.value = e.isLeaf;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  /**
   * 切换分页，当前显示的页数
   */
  // accountList();
  watch(
    () => nodeId.value,
    () => {
      const node = tree.value.getNode(nodeId.value);
      emits('currentOrgSelect', node);
    },
  );
  defineExpose({
    currentNode,
  });
</script>

<style scoped lang="scss">
  .content-model {
    display: flex;
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    overflow: hidden;
    height: 100%;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    background: #ffffff;
    display: flex;

    // border-radius: 6px;
    .tree-content {
      // height: calc(100% - 60px);
      width: 100%;
      height: calc(100vh - 30px);
      .active {
        background-color: #c9e9f7;
      }
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
</style>

<style lang="scss">
  .content-model .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }
  .select-div {
    line-height: 40px;
    font-size: 16px;

    span {
      color: #409eff;
    }
  }
</style>
