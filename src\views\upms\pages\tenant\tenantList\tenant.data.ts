const codeReg = /^((?![\-]).)*$/;
export const formTenantSchema: any[] = [
  {
    field: 'tenantName',
    label: '租户名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租户名称',
    },
    required: true,
    rules: [{ max: 30, message: '超过长度限制，最多30字' }],
  },
  {
    label: '租户标识',
    field: 'tenantAlias',
    component: 'Input',
    componentProps: {
      placeholder: '请输入租户标识',
      disabled: true,
    },
    required: true,
    rules: [
      { required: true, pattern: codeReg, message: '请输入正确的编码，不能包含“-”' },
      { max: 30, message: '超过长度限制，最多30字' },
    ],
  },
  {
    label: '租户管理员',
    field: 'adminIds',
    slot: 'adminIds',
    required: true,
    filterable: true,
  },
  {
    field: 'description',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字' }],
  },
];
