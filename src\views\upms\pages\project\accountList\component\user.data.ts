import { TableOptions } from '/@/components/sys/BasicTable/types';
import { getDictStorage } from '/@/utils/storage/dict';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
export const columns: TableOptions[] = [
  {
    label: '员工工号',
    prop: 'empCode',
    ellipsis: true,
    align: 'center',
  },
  {
    label: '姓名',
    prop: 'empName',
    ellipsis: true,
    align: 'center',
  },
  {
    label: '所属机构',
    prop: 'instName',
    ellipsis: true,
    align: 'center',
  },
];

export const searchFormSchema: SearchOptions[] = [
  {
    field: 'empCode',
    label: '员工工号：',
    component: 'Input',
    placeholder: '请输入员工工号',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'empName',
    label: '员工姓名',
    component: 'Input',
    placeholder: '请输入员工姓名',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'industryType',
    label: '所属版块：',
    component: 'Select',
    placeholder: '请选择所属版块',
    componentProps: {
      clearable: true,
      options: getDictStorage()?.dic_industry_type
        ? getDictStorage().dic_industry_type
        : [],
    },
    span: 8,
  },
  {
    field: 'sub',
    label: '查看本机构及下属机构员工',
    componentProps: {
      defaultValue: '1',
    },
    span: 8,
    labelWidth: 50,
    slot: 'sub',
  },
];
