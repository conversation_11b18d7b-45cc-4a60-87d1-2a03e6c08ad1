import { sysHttp } from '/@/views/upms/common/http';
import {
  addupdateProject,
  ProjectListGetResultModel,
  getProjectPramas,
  TenantUserGetResultModel,
  deleteProjectType,
  ProUserParams,
} from './model/projectModel';
enum Api {
  qryProject = '/project/', //项目列表
  delProject = '/project/del', //删除项目
  saveProject = '/project/save', //新增、修改项目
  TenantUser = '/user/getUsers', //获取租户下的用户
  inviteAccountUrl = '/project-user/editUser',
  projectAccountDelUrl = '/project-user/delUser',
  uploadRoleUrl = '/user/update/project/role',
}

// 项目列表
export const GetProjectGroup = (params?: getProjectPramas) =>
  sysHttp.get<ProjectListGetResultModel>({ url: Api.qryProject, params: params });
// 新增项目|修改项目
export const AddProjectGroup = (params: addupdateProject) =>
  sysHttp.post({ url: Api.saveProject, params });
// 删除项目
export const DelProjectGroup = (params: deleteProjectType) =>
  sysHttp.delete({ url: Api.delProject, params });
// 获取账号字典表
export const GetTenantUser = (params?: ProUserParams) =>
  sysHttp.post<TenantUserGetResultModel>({
    url: Api.TenantUser,
    params: params,
  });
export const inviteAccountApi = (params) =>
  sysHttp.post({ url: `${Api.inviteAccountUrl}`, params });

export const projectAccountDelApi = (params) =>
  sysHttp.post({ url: `${Api.projectAccountDelUrl}`, params });

export const uploadRoleApi = (params) => {
  return sysHttp.post({
    url: Api.uploadRoleUrl,
    params,
  });
};
