<template>
  <div class="vtable-container">
    <div ref="tableContainer" class="vtable-wrapper" :style="{height: height}"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import * as VTable from '@visactor/vtable';

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  columns: {
    type: Array,
    required: true
  },
  // 添加过滤配置选项
  filterConfig: {
    type: Object,
    default: () => ({})
  },
  height: {
    type: String,
    default: '200px',
    required: false
  }
});

const emit = defineEmits(['filter-change']);

const tableContainer = ref(null);
let tableInstance = null;

// 根据数据样本判断列的数据类型
const getColumnType = (column) => {
  if (!props.data || props.data.length === 0) return 'string';
  
  // 获取第一个非空值
  const sample = props.data.find(item => item[column] !== null && item[column] !== undefined);
  if (!sample) return 'string';
  
  const value = sample[column];
  if (typeof value === 'number') return 'number';
  if (value instanceof Date) return 'date';
  if (typeof value === 'boolean') return 'boolean';
  return 'string';
};

// 将列名转换为VTable需要的列配置格式
const getColumnConfig = (columns) => {
  return columns.map(column => {
    // 检查数据类型，为不同类型的列应用不同的过滤器配置
    const columnType = getColumnType(column);
    
    return {
      field: column,
      title: column,
      width: 'auto',
      sort: true,
      filter: {
        // 启用过滤功能
        filterIcon: true,
        // 配置过滤面板
        filterPanel: {
          // 设置面板宽度
          width: 200,
          // 设置面板高度
          height: 300,
          // 设置面板位置
          placement: 'bottom-end'
        },
        // 根据列类型设置不同的过滤模式
        filterMode: columnType === 'number' ? 'range' : 'multiple',
        // 设置过滤条件
        condition: true,
        // 设置是否显示搜索框
        showSearchInput: true,
        // 设置是否显示全选按钮
        showSelectAll: true,
        // 自定义过滤器配置
        ...(props.filterConfig[column] || {})
      }
    };
  });
};

// 初始化表格
const initTable = () => {
    console.log('initTable',props.data,props.columns,getColumnConfig(props.columns))
  if (!tableContainer.value || !props.data.length || !props.columns.length) return;
  
  // 销毁之前的实例
  if (tableInstance) {
    tableInstance.dispose();
  }

  // 创建VTable实例
  tableInstance = new VTable.ListTable({
    container: tableContainer.value,
    columns: getColumnConfig(props.columns),
    records: props.data,
    widthMode: 'autoWidth',
    heightMode: 'autoHeight',
    defaultHeaderRowHeight: 40,
    defaultRowHeight: 40,
    hover: {
      highlightMode: 'row'
    }
  });
  
  // 添加过滤事件监听
  tableInstance.on('filter-change', (filterResult) => {
    console.log('过滤条件变更:', filterResult);
    // 通过emit向父组件发送过滤后的数据
    emit('filter-change', filterResult);
  });
};

// 监听数据变化重新渲染表格
watch(
  () => [props.data, props.columns],
  () => {
    initTable();
  },
  { deep: true }
);

onMounted(() => {
  initTable();
});

onBeforeUnmount(() => {
  if (tableInstance) {
    tableInstance.dispose();
    tableInstance = null;
  }
});
</script>

<style scoped>
.vtable-container {
  width: 100%;
  overflow: auto;
  height: 100%;
}

.vtable-wrapper {
  width: 100%;
  min-height: 200px;
}
</style>