<template>
  <div class="content">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane size="30" min-size="30">
        <div class="left">
          <!-- tenant_first_class  新增顶级 -->
          <div class="top-button">
            <el-button
              type="primary"
              @click="addTopNode"
              v-auth="['tenant_tenant_first_class']"
              :icon="Plus"
              >新增顶级租户</el-button
            >
            <el-button
              type="primary"
              @click="addNode"
              v-auth="['tenant_tenant_add']"
              :disabled="formData.id ? false : true"
              :icon="Plus"
              >新增</el-button
            >
            <el-button
              type="danger"
              @click="deleteNode"
              v-auth="['tenant_tenant_delete']"
              :disabled="!nodeId"
              :icon="Delete"
              plain
              >删除</el-button
            >
          </div>
          <div class="tree-content">
            <div class="tree-input">
              <el-input placeholder="请输入租户名称" clearable v-model="value" />
            </div>
            <div class="tree-div">
              <z-tree
                :setting="setting"
                :nodes="treeDate"
                class="baseTree"
                :name="value"
                @onCreated="handleCreated"
              />
            </div> </div
        ></div>
      </Pane>
      <Pane class="mid" ref="mid">
        <!--右侧div内容-->
        <div class="right">
          <p class="title"> 租户信息 </p>
          <div class="form-div" v-if="formData.id || status === 'add'">
            <basic-form
              class="basic-form-div"
              labelWidth="140px"
              :form-list="formTenantSchema"
              :form-data="formData"
              ref="formRef"
              :showSubmit="false"
              :check-strictly="false"
              :isCreate="isCreate"
            >
              <template #adminIds>
                <el-select
                  style="width: 100%"
                  v-model="formData.adminIds"
                  clearable
                  multiple
                  @focus="handleFocusAccountEvent"
                  @change="handleChangeAccount"
                  @remove-tag="removeTagAccount"
                  ref="userSelect"
                >
                  <el-option
                    v-for="item in userOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                /></el-select>
              </template>
            </basic-form>

            <div class="bottom-btn-div">
              <!-- v-auth="['tenant_save']" -->
              <el-button
                type="primary"
                :loading="loading"
                @click="saveForm"
                v-auth="['tenant_tenant_save']"
                >保存</el-button
              >
            </div>
          </div>
          <div v-else class="no-form"> 未选择任何租户 </div>
        </div>
      </Pane>
    </Splitpanes>
    <el-dialog
      v-model="visibleAccount"
      title="选择账号"
      width="60%"
      :destroy-on-close="true"
    >
      <account-list
        @selectAccounts="selectAccounts"
        ref="userListRef"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleAccount = false">取消</el-button>
        <el-button type="primary" @click="saveAccount">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, markRaw } from 'vue';
  import ZTree from '@cpit-cpvf/tree';
  import '@cpit-cpvf/tree/dist/style.css';
  import type { FormInstance } from 'element-plus';
  import { Plus, Delete } from '@element-plus/icons-vue';
  import useZtree from '/@/hooks/upms/tenant/useTenantTree';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { NodeItem } from '/@/views/upms/api/model/tenantModel';
  import { tenantTree, SaveTenant, DelTenant } from '/@/views/upms/api/tenant';
  import { formTenantSchema } from './tenant.data';
  import { CircleClose } from '@element-plus/icons-vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import accountList from './component/account.vue';
  import { Option } from '/@/components/sys/BasicForm/types';
  import useAccount from './hooks/useAccount';
  import { useUserStore } from '/@/stores/modules/user';
  import { useTenantStore } from '/@/stores/modules/tenant';
  import { getAuthStorage, setAuthStorage } from '/@/utils/storage/auth';
  import { userPermission } from '/@/views/upms/api/system';
  const userStore = useUserStore();
  const useTenant = useTenantStore();
  const userSelect = ref<HTMLDivElement | null>(null);
  const userListRef = ref<HTMLDivElement | null>(null);
  const formRef = ref<FormInstance | null>(null);
  const { setting, handleCreated, value, ztree, nodeId, nodeStatus } = useZtree();
  const isCreate = ref(false);
  const treeDate = ref<any>([]);
  const formData = ref<NodeItem>({
    id: '',
    tenantName: '',
    tenantAlias: '',
    adminId: '',
    description: '',
    adminName: '',
    adminIds: [],
    version: null,
    leaf: '',
  });
  const loading = ref(false);
  const parentId = ref('');
  const status = ref('');
  const newNode = ref<any>({});

  const userOptions = ref<Option[]>([]);
  const formSchema = ref({});
  const itemData = ref<any>({});
  formSchema.value = formTenantSchema;

  const getTreeData = async () => {
    const res = await tenantTree();
    treeDate.value = res;
  };

  const addNode = () => {
    const nodes =
      ztree.value && ztree.value.getSelectedNodes()
        ? ztree.value.getSelectedNodes()
        : [];
    status.value = 'add';
    newNode.value = {
      parentId: nodes.length > 0 ? nodes[0].id : '',
      tenantName: '未命名',
      id: '',
    };
    parentId.value = nodes.length > 0 ? nodes[0].id : '';
    const newTreeNode =
      nodes.length > 0
        ? ztree.value.addNodes(nodes[0], [-1], newNode.value)
        : ztree.value.addNodes(null, [-1], newNode.value);

    ztree.value.selectNode(newTreeNode[0]);
    ztree.value.setting.callback.onClick(
      '',
      ztree.value.setting.treeId,
      newTreeNode[0],
    );
    isCreate.value = false;
    formSchema.value[1].componentProps.disabled = false;
    const ruleFormRef = formRef.value && formRef.value.ruleFormRef;
    if (ruleFormRef) {
      formRef.value?.resetForm(ruleFormRef);
    }
  };
  const addTopNode = () => {
    status.value = 'add';
    newNode.value = {
      parentId: '',
      tenantName: '未命名',
      id: '',
    };
    parentId.value = '';
    const newTreeNode = ztree.value.addNodes(null, [-1], newNode.value);

    ztree.value.selectNode(newTreeNode[0]);
    ztree.value.setting.callback.onClick(
      '',
      ztree.value.setting.treeId,
      newTreeNode[0],
    );
    isCreate.value = false;
    formSchema.value[1].componentProps.disabled = false;
    const ruleFormRef = formRef.value && formRef.value.ruleFormRef;
    if (ruleFormRef) {
      formRef.value?.resetForm(ruleFormRef);
    }
  };

  getTreeData();
  const getuserPermission = (userInfo) => {
    userPermission().then(res => {
      userInfo.permissionMap = res;
      setAuthStorage(userInfo);
    })
  };
  const saveForm = () => {
    const getData = formRef.value && formRef.value?.submitForm;
    const ruleFormRef = formRef.value && formRef.value?.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      data.adminId = data.adminIds.length > 0 ? data.adminIds.join(',') : '';
      if (status === 'success') {
        loading.value = true;
        SaveTenant({ ...data, parentId: parentId.value })
          .then(async (res) => {
            nodeId.value = res.id;
            formData.value = res;
            userOptions.value = [];
            formData.value.adminIds = [];
            const userNames =
              formData.value.adminName && formData.value.adminName.split(',');
            const adminListId: string[] =
              formData.value.adminId && formData.value.adminId.split(',');
            selectAccountData.value = [];
            for (let i = 0; i < adminListId.length; i++) {
              formData.value.adminIds.push(adminListId[i]);
              let userObj = {
                label: userNames[i],
                value: adminListId[i],
              };
              userOptions.value.push(userObj);
              let selectUserObj = {
                userName: userNames[i],
                id: adminListId[i],
                label: userNames[i],
              };
              selectAccountData.value.push(selectUserObj);
            }
            parentId.value = formData.value.parentId ? formData.value.parentId : '';

            getTreeData();
            await useTenant.requestData(userStore.getUserInfo);
            ElMessage({
              type: 'success',
              showClose: true,
              message: formData.value.id ? '修改租户成功' : '新增租户成功',
            });
            formSchema.value[1].componentProps.disabled = true;
            status.value = '';

            loading.value = false;
            nodeStatus.value = true;
            let userInfo = getAuthStorage();
            getuserPermission(userInfo);
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        loading.value = false;
      }
    });
  };

  const deleteNode = () => {
    if (nodeId.value && JSON.stringify(newNode.value) === '{}') {
      ElMessageBox.confirm('确认删除该租户数据？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'error',
        icon: markRaw(CircleClose),
        customStyle: {
          backgroundColor: 'rgba(255, 241, 240, 1)',
          border: '1px soild rgba(255, 163, 158, 1)',
        },
      })
        .then(() => {
          DelTenant({ id: nodeId.value }).then(async () => {
            ElMessage({
              message: '删除成功',
              grouping: true,
              type: 'success',
            });
            getTreeData();
            // useDict.reqDict();
            await useTenant.requestData(userStore.getUserInfo);
            nodeId.value = '';
          });
        })
        .catch(() => {});
    } else if (!nodeId.value && JSON.stringify(newNode.value) === '{}') {
      ElMessage({
        type: 'warning',
        showClose: true,
        message: '请先选择租户节点在进行删除操作！',
      });
    } else {
      const node = ztree.value.getSelectedNodes();
      ztree.value.removeNode(node[0]);
      getTreeData();
      newNode.value = {};
      nodeId.value = '';
    }
  };

  const {
    handleFocusAccountEvent,
    handleChangeAccount,
    selectAccounts,
    saveAccount,
    visibleAccount,
    selectAccountData,
    removeTagAccount,
  } = useAccount(formData, userOptions, userSelect, userListRef);
  watch(
    () => nodeId.value,
    (val) => {
      if (val) {
        const node =
          ztree.value && ztree.value.getNodesByParam('id', nodeId.value, null)[0];
        if (node) {
          formData.value = {
            id: node.id,
            tenantName: node.tenantName,
            tenantAlias: node.tenantAlias,
            adminId: node.adminId,
            description: node.description,
            adminName: node.adminName,
            version: node.version,
            parentId: node.parentId,
            leaf: node.leaf,
          };
        }

        userOptions.value = [];
        formData.value.adminIds = [];
        const userNames =
          formData.value.adminName && formData.value.adminName.split(',');
        const adminListId: string[] =
          formData.value.adminId && formData.value.adminId.split(',');
        selectAccountData.value = [];
        for (let i = 0; i < adminListId.length; i++) {
          formData.value.adminIds.push(adminListId[i]);
          let userObj = {
            label: userNames[i],
            value: adminListId[i],
          };
          userOptions.value.push(userObj);
          let selectUserObj = {
            userName: userNames[i],
            id: adminListId[i],
            label: userNames[i],
          };
          selectAccountData.value.push(selectUserObj);
        }
        parentId.value = formData.value.parentId ? formData.value.parentId : '';
        nodeStatus.value = false;
        status.value = '';
        formSchema.value[1].componentProps.disabled = true;
      } else {
        selectAccountData.value = [];
        itemData.value = {};
        formData.value = {
          id: '',
          tenantName: '',
          tenantAlias: '',
          adminId: '',
          adminIds: [],
          description: '',
          adminName: '',
          version: null,
        };
      }
    },
  );
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  }
  /*左侧div样式*/
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;

    border-radius: 6px;
    .tree-content {
      height: calc(100% - 60px);
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 48px;
      font-size: 18px;
      padding-left: 18px;
      background: #ffffff;
      font-weight: 700;
    }
  }

  .top-button {
    padding: 8px 18px 8px 18px;
    border-bottom: 1px solid #e4e7ed;
    .upload-btn {
      display: inline-block;
      margin: 0 12px;
    }
  }
  /*拖拽区div样式*/
  .resize {
    cursor: col-resize;
    float: left;
    position: relative;
    top: 45%;
    background-color: #d6d6d6;
    border-radius: 5px;
    margin-top: -10px;
    width: 10px;
    height: 50px;
    background-size: cover;
    background-position: center;
    font-size: 32px;
    color: white;
  }
  /*拖拽区鼠标悬停样式*/
  .resize:hover {
    color: #444444;
  }
  /*右侧div'样式*/
  .mid {
    float: left;
    width: 55%; /*右侧初始化宽度*/
    height: 100%;
    background: #fff;
    box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
  }
  .dept-tabs-xl {
    padding-top: 9px;
    padding-left: 1px;
    width: 100%;
    height: 100%;

    ::deep(.el-tabs__content) {
      height: calc(100% - 60px) !important;
    }
  }
  .table-content {
    background: #ffffff;
    margin-top: 20px;
  }

  .base-table-all {
    margin: 0 12px;
  }
  .inst-code {
    color: var(--el-color-primary);
    cursor: pointer;
  }
  .form-div {
    width: 100%;
    height: 100%;
    .basic-form-div {
      height: calc(100% - 110px);
      border-bottom: 1px solid #e4e7ed;
      overflow: auto;
    }
    .bottom-btn-div {
      display: flex;
      justify-content: flex-end;

      padding-right: 18px;
      padding-top: 14px;
    }
  }
  .no-form {
    width: 100%;
    height: 100%;
    line-height: 100%;
    padding-top: 120px;
    font-size: 24px;
    text-align: center;
  }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }
</style>
