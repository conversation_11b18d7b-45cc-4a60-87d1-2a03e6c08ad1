<template>
  <div class="content">
    <div class="box">
      <div class="search-div">
        <p class="title"> 平台微应用搜索 </p>
        <basic-search
          :searchArray="searchFormSchema"
          class="search-all"
          ref="searchRef"
          :labelWidth="80"
          :labelShow="false"
          @reset="getMicroAppList"
          @onSearch="getMicroAppList"
        />
      </div>
      <div class="table-content">
        <div class="table-top-div">
          <p>平台微应用列表</p>
          <div class="top-button">
            <el-button
              type="primary"
              @click="handleAdd"
              v-auth="['platform_micro_add']"
              :icon="Plus"
              >新增</el-button
            >
            <el-button
              type="danger"
              @click="batchDeletion"
              :disabled="selectionIds.length > 0 ? false : true"
              v-auth="['platform_micro_delete']"
              :icon="Delete"
              plain
              >删除</el-button
            >
            <el-button
              type="primary"
              @click="downLoad(false)"
              v-auth="['platform_micro_export']"
              :icon="Download"
              plain
              >导出</el-button
            >
            <el-button
              type="primary"
              @click="fileVisible = true"
              v-auth="['platform_micro_import']"
              :icon="Upload"
              plain
              >导入</el-button
            >
          </div>
        </div>
        <basic-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :total="page.total"
          :page-size="page.pageSize"
          :current-page="page.pageNum"
          @page-change="pageChange"
          @size-change="sizeChange"
          @selectionChange="handleCurrentChange"
          @sortChange="sortChange"
          :downSetting="true"
          height="calc(100vh - 392px)"
        >
          <template #status="{ record }">
            <span v-if="record.status === '1'"
              ><span class="active-span"></span> 启用</span
            >
            <span v-else type="danger"> <span class="close-span"></span> 禁用</span>
          </template>
          <template #action="{ record }">
            <el-button
              type="primary"
              link
              @click="handleDelete(record, '0')"
              v-if="record.status === '0'"
              v-auth="['platform_micro_delete']"
              >删除</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleEdit(record)"
              v-if="record.status === '1'"
              v-auth="['platform_micro_edit']"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleStatus(record)"
              v-show="record.status === '1'"
              >禁用</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleStatus(record)"
              v-show="record.status === '0'"
              >启用</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleDetail(record)"
              v-auth="['platform_micro_details']"
              >查看</el-button
            >
          </template>
        </basic-table>
      </div>
    </div>

    <el-drawer v-model="drawer" direction="rtl" size="600px" :destroy-on-close="true">
      <template #header>
        <h4>{{ title }}</h4>
      </template>
      <template #default>
        <basic-form
          :formList="FormSchema"
          :isCreate="false"
          :formData="formData"
          :showSubmit="false"
          :check-strictly="true"
          :disabled="!isEdit"
          ref="formPost"
          labelWidth="130"
        />
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmClick"
            v-if="isEdit"
            :loading="loading"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>
    <el-dialog
      v-model="batchConfirmationVisible"
      title="删除警告"
      width="400px"
      class="batch-Confirmation-dialog"
      :destroy-on-close="true"
    >
      <BatchConfirmation @getPassword="getPassword" />
      <template #footer>
        <el-button @click="batchConfirmationVisible = false">取消</el-button>
        <el-button
          type="info"
          @click="batchConfirmationSave"
          :loading="batchConfirmationLoading"
          >删除</el-button
        >
      </template>
    </el-dialog>
    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
      class="file-dialog"
    >
      <el-button type="primary" link @click="downLoad(true)" style="margin-bottom: 12px"
        >下载微应用导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :before-upload="beforeUpload"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, markRaw } from 'vue';
  import { ElMessage, ElMessageBox, ElTable } from 'element-plus';
  import { CircleClose, Plus, Delete, Download, Upload } from '@element-plus/icons-vue';
  import useUpload from '/@/hooks/upms/upload/useUpload';
  import type { UploadInstance } from 'element-plus';
  import { columns, searchFormSchema, FormSchema } from './index.data';
  import {
    MicroAppList,
    SaveMicroApp,
    DelMicroApp,
    downLoadApi,
    uploadApi,
    PutMicroApp,
  } from '/@/views/upms/api/microApp';
  import { ListItem, ListResultModel } from '/@/views/upms/api/model/microAppModel';
  import BatchConfirmation from '/@/views/upms/components/BatchConfirmation.vue';
  import userBatchConfirmation from '/@/views/upms/common/hooks/userBatchConfirmation';
  const searchRef = ref({});
  const tableData = ref<ListItem[]>([]);
  const tableRef = ref<InstanceType<typeof ElTable>>();
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const formData = ref<ListItem>({
    id: '',
    microName: '',
    name: '',
    path: '',
    url: '',
  });
  const drawer = ref(false);
  const isEdit = ref(true);
  const title = ref('新增');
  const formPost = ref();
  const selectionIds = ref<string[]>([]);
  const loading = ref(false);
  const uploadLoading = ref(false);
  const upload = ref<UploadInstance>();
  const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    getMicroAppList();
  };
  // 微应用列表
  const getMicroAppList = () => {
    const { pageNum, pageSize } = page.value;
    const { microName, name, url } = searchRef?.value?.['searchValue'] || {};
    const params = {
      pageNum,
      pageSize,
      microName,
      name,
      url,
      ...sortData.value,
    };
    MicroAppList(params).then((res: ListResultModel) => {
      tableData.value = res.list;
      page.value.total = res.total;
    });
  };
  /**
   * 切换分页，每页显示数量
   */
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    getMicroAppList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    getMicroAppList();
  };
  const downLoad = () => {
    const { groupId, groupName, groupCode, tenantId, projectId, status, channelId } =
      searchRef?.value?.['searchValue'] || {};
    const params = {
      groupId,
      groupName,
      groupCode,
      tenantId,
      projectId,
      status,
      channelId,
      ...sortData.value,
    };
    downLoadApi(params).then((res) => {
      const blob = new Blob([res.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
      });
      const fileName = '微应用管理' + '.xlsx';
      const elink = document.createElement('a'); // 创建a标签
      elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
      elink.style.display = 'none';
      elink.href = URL.createObjectURL(blob);
      document.body.appendChild(elink);
      elink.click(); // 点击下载
      console.log(elink.href);
      URL.revokeObjectURL(elink.href); // 释放URL 对象
      document.body.removeChild(elink); // 释放标签
    });
  };
  const beforeUpload = (file) => {
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];
    const isExcel = allowedTypes.includes(file.type);
    const isExtensionValid = file.name.endsWith('.xls') || file.name.endsWith('.xlsx');

    if (!isExcel || !isExtensionValid) {
      ElMessage.warning('只支持xls,xlsx文件类型的文件!');
      return false; // 阻止上传
    }
    return true;
  };
  const uploadFile = () => {
    uploadLoading.value = true;
    let formData = new FormData();
    if (!fileData.value) {
      ElMessage.warning('请上传文件!');
      return false;
    }
    formData.append('file', fileData.value?.raw);
    uploadApi(formData)
      .then(() => {
        ElMessage({
          type: 'success',
          message: '导入成功',
        });
        fileVisible.value = false;
        getMicroAppList();
        uploadLoading.value = false;
      })
      .catch(() => {
        uploadLoading.value = false;
      });
  };
  getMicroAppList();
  const confirmClick = () => {
    const getData = formPost.value.submitForm;
    const ruleFormRef = formPost.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        loading.value = true;
        let saveData = { ...data };
        saveData.path = '/micro/' + saveData.path;
        SaveMicroApp(saveData)
          .then((res) => {
            ElMessage({
              type: 'success',
              showClose: true,
              message: res.id ? '修改成功' : '新增成功',
            });
            loading.value = false;
            getMicroAppList();
            drawer.value = false;
          })
          .catch(() => {
            loading.value = false;
          });
      }
    });
  };
  const handleAdd = () => {
    formData.value = {
      id: '',
      microName: '',
      name: '',
      path: 'vite',
      url: '',
    };
    isEdit.value = true;
    drawer.value = true;
    title.value = '新增微应用';
  };

  const handleEdit = (item) => {
    isEdit.value = true;
    drawer.value = true;
    title.value = '编辑微应用';

    let editData = { ...item };
    editData.path = editData.path.replace(/\/micro\//, '');
    formData.value = editData;
  };
  const handleDetail = (item) => {
    isEdit.value = false;
    drawer.value = true;
    title.value = '查看微应用';
    let editData = { ...item };
    editData.path = editData.path.replace(/\/micro\//, '');
    formData.value = editData;
  };
  const handleDelete = (item, type) => {
    // type 1 批量删除  0 单独删除
    let title = '';
    if (type === 1) {
      title = `确认删除当前所选中${selectionIds.value.length}条微应用数据？`;
    } else {
      title = '确认删除该微应用？';
    }
    ElMessageBox.confirm(title, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        let params = '';
        if (item === null) {
          params = selectionIds.value.join(',');
        } else {
          params = item.id;
        }
        console.log(params);
        DelMicroApp(params).then(() => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '微应用删除成功',
          });
          if (type === '1') {
            selectionIds.value = [];
            tableRef.value!.clearSelection();
          }
          getMicroAppList();
        });
      })
      .catch(() => {});
  };

  const callDelete = (params) => {
    params = selectionIds.value.join(',');
    DelMicroApp(params).then(() => {
      ElMessage({
        showClose: true,
        type: 'success',
        message: '微应用删除成功',
      });
      selectionIds.value = [];
      tableRef.value!.clearSelection();
      getMicroAppList();
    });
  };
  const {
    batchConfirmationVisible,
    batchDeletion,
    batchConfirmationSave,
    getPassword,
    batchConfirmationLoading,
  } = userBatchConfirmation(selectionIds, callDelete);

  const handleCurrentChange = (val) => {
    console.log(val);
    selectionIds.value = [];
    for (let i = 0; i < val.length; i++) {
      console.log(val[i].id);
      selectionIds.value.push(val[i].id);
    }
    // console.log(old);
  };
  const handleStatus = function (item) {
    PutMicroApp(item.id).then(() => {
      getMicroAppList();
    });
  };
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    // .title {
    //   border-bottom: 1px solid #e4e7ed;
    //   line-height: 68px;
    //   font-size: 18px;
    //   padding-left: 24px;
    //   background: #ffffff;
    //   font-weight: 700;
    // }

    // .search-all {
    //   background: #ffffff;
    //   padding-left: 12px;
    // }
    .search-div {
      border-radius: 6px;
      background: #fff;
      // padding: 10px 16px;
      .title {
        // border-bottom: 1px solid #e4e7ed;
        // line-height: 68px;

        font-size: 18px;
        padding: 20px 0 6px 20px;
        // background: #ffffff;
        font-weight: 700;
        color: #333333;
      }
    }
    .table-top-div {
      display: flex;
      justify-content: space-between;
      // line-height: 64px;
      margin-bottom: 20px;
      p {
        font-size: 18px;
        color: #333333;
        font-weight: 700;
        margin: 0;
        padding: 0;
        line-height: 32px;
      }
    }
    .table-content {
      margin-top: 16px;
      background: #ffffff;
      // height: calc(100% - 154px);
      padding: 20px 20px 0 20px;
      position: relative;
      overflow: auto;
      .top-button {
        // position: absolute;
        // top: 10px;
        // left: 16px;
        z-index: 9;
        .upload-btn {
          display: inline-block;
          margin: 0 12px;
        }
      }
      .tip {
        position: absolute;
        top: 22px;
        right: 70px;
        z-index: 9;
        color: #409eff;
      }

      .active-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #00a854;
        margin-right: 5px;
      }
      .close-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #bfbfbf;
        margin-right: 5px;
      }
    }
  }

  h4 {
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 700;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
</style>
