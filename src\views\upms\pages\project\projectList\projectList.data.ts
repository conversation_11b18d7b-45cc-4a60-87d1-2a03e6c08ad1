import { SearchOptions } from '/@/components/sys/BasicSearch/types';
import { FormOptions } from '/@/components/sys/BasicForm/types';
const codeReg = /^((?![\-]).)*$/;
export const columns: any[] = [
  {
    label: '项目标识',
    prop: 'projectAlias',
    width: 100,
    align: 'center',
  },
  {
    label: '项目名称',
    prop: 'projectName',
    width: 200,
    align: 'center',
  },
  {
    label: '项目描述',
    prop: 'description',
    align: 'center',
  },
  {
    label: '项目管理员',
    prop: 'adminName',
    width: 200,
    align: 'center',
  },
  {
    label: '租户名称',
    prop: 'tenantName',
    width: 100,
    align: 'center',
  },
  {
    label: '修改时间',
    prop: 'lastModifiedDate',
    width: 170,
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    width: 160,
  },
];
export const formSchema: any[] = [
  {
    field: 'projectName',
    component: 'Input',
    label: '项目名称',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请输入项目名称',
    },
    required: true,
    rules: [{ max: 30, message: '超过长度限制，最多30字' }],
  },
  {
    field: 'projectAlias',
    component: 'Input',
    label: '项目标识',
    colProps: {
      span: 24,
    },
    componentProps: {
      disabled: true,
      placeholder: '请输入项目标识',
    },
    required: true,
    rules: [
      { required: true, pattern: codeReg, message: '请输入正确的编码，不能包含“-”' },
      { max: 30, message: '超过长度限制，最多30字' },
    ],
  },
  {
    field: 'adminIds',
    slot: 'adminIds',
    label: '项目管理员',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请选择项目管理员',
    },
    required: true,
    filterable: true,
    ifShow: true,
  },
  {
    field: 'description',
    component: 'InputTextArea',
    label: '项目描述',
    colProps: {
      span: 24,
    },
    componentProps: {
      placeholder: '请输入项目描述',
    },
    rules: [{ max: 240, message: '超过长度限制，最多240字' }],
  },
];
export const searchFormSchema: SearchOptions[] = [
  {
    field: 'projectName',
    label: '项目名称',
    component: 'Input',
    placeholder: '请输入项目名称',
    span: 6,
    componentProps: {
      clearable: true,
    },
  },
  {
    field: 'projectAlias',
    label: '项目标识',
    component: 'Input',
    placeholder: '请输入项目标识',
    span: 6,
    componentProps: {
      clearable: true,
    },
  },
];
export const formDetailSchema: FormOptions[] = [
  {
    label: '项目标识：',
    field: 'projectAlias',
    component: 'Text',
  },
  {
    label: '项目名称：',
    field: 'projectName',
    component: 'Text',
  },
  {
    label: '项目描述：',
    field: 'description',
    component: 'Text',
  },
  {
    label: '项目管理员：',
    field: 'adminName',
    component: 'Text',
  },
  {
    label: '租户名称：',
    field: 'tenantName',
    component: 'Text',
  },
  {
    label: '修改时间：',
    field: 'lastModifiedDate',
    component: 'Text',
  },
];
