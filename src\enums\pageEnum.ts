export enum PageEnum {
  // basic login path
  BASE_LOGIN = '/login',
  // basic home path
  BASE_HOME = 'dashboard',
  // BASE_HOME = '/',
  // error page path
  ERROR_PAGE = '/:all(.*)*',
  // BASE_HOME = '/report/mediumInfo',
  BASE_HOME_USER = '/report/mediumInfo',
  // error log page path
  ERROR_LOG_PAGE = '/error-log/list',
  //告警管理->媒体管理

  USER_INFO = '/dashboard',
  BASE_RETRIEVE = '/user/retrievepassword',
  BASE_REGIDSTER = '/user/register',
}
