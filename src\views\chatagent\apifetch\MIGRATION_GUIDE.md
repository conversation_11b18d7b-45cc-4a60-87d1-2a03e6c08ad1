# API 服务迁移指南

## 概述

本文档详细说明了如何从原有的 API 服务迁移到新的统一 fetch 封装层。新的封装层提供了更好的错误处理、缓存、重试等功能。

## 迁移对比

### 1. DataSource API 迁移

#### 原来的实现 (datasource-api-service.ts)

```typescript
export class DataSourceApiService {
  private readonly BASE_URL = '/api/connection/datasource'

  async getDataSourceList(): Promise<DataSource[]> {
    try {
      const response = await fetch(`${this.BASE_URL}/list?pageNo=1&pageSize=1000&refresh=true`)
      const result = await this.handleResponse(response)
      const data = await result.json()

      if (data && data.success && data.data && data.data.data) {
        return data.data.data.map((item: any) => ({
          id: item.id,
          name: item.alias || `数据源${item.id}`,
          type: item.type,
          // ...
        }))
      }
      return []
    } catch (error) {
      console.error('获取数据源列表失败:', error)
      throw new Error(`获取数据源列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  private async handleResponse(response: Response): Promise<Response> {
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    return response
  }
}
```

#### 新的实现 (datasource-api.ts)

```typescript
export class DataSourceApi extends ApiClient {
  constructor() {
    super('/api/connection/datasource', {
      enableLogging: true,
      enableCache: true,
      defaultCacheTime: 5 * 60 * 1000, // 5分钟缓存
      enableRetry: true,
      defaultRetryCount: 2
    })
  }

  async getDataSourceList(options?: RequestOptions): Promise<DataSource[]> {
    try {
      const params = {
        pageNo: 1,
        pageSize: 1000,
        refresh: true
      }

      const response = await this.get<DataSourceListResponse>('/list', params, {
        cache: true,
        cacheTime: 5 * 60 * 1000, // 5分钟缓存
        ...options
      })

      if (response && response.success && response.data && response.data.data) {
        return response.data.data.map((item: any) => ({
          id: item.id,
          name: item.alias || `数据源${item.id}`,
          type: item.type,
          // ...
        }))
      }
      return []
    } catch (error) {
      console.error('获取数据源列表失败:', error)
      throw new Error(`获取数据源列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}
```

**主要改进：**
- ✅ 自动错误处理，无需手动检查 `response.ok`
- ✅ 内置缓存支持，提高性能
- ✅ 自动重试机制
- ✅ 统一的配置管理
- ✅ 更好的类型安全

### 2. Model API 迁移

#### 原来的实现 (model-api-service.ts)

```typescript
export class ModelApiService {
  private readonly BASE_URL = '/cpit/ai/setting/model'

  async getBriefModelList(): Promise<Model[]> {
    try {
      const response = await fetch(`${this.BASE_URL}/brief/list`)
      const result = await this.handleResponse(response)
      const data = await result.json()

      if (data && typeof data === 'object' && data.data) {
        return data.data
      }
      if (Array.isArray(data)) {
        return data
      }
      return []
    } catch (error) {
      console.error('获取大模型简要列表失败:', error)
      throw new Error(`获取大模型列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}
```

#### 新的实现 (model-api.ts)

```typescript
export class ModelApi extends ApiClient {
  constructor() {
    super('/cpit/ai/setting/model', {
      enableLogging: true,
      enableCache: true,
      defaultCacheTime: 10 * 60 * 1000, // 10分钟缓存
      enableRetry: true,
      defaultRetryCount: 2
    })
  }

  async getBriefModelList(options?: RequestOptions): Promise<Model[]> {
    try {
      const response = await this.get<ModelResponse | Model[]>('/brief/list', undefined, {
        cache: true,
        cacheTime: 10 * 60 * 1000, // 10分钟缓存
        ...options
      })

      if (Array.isArray(response)) {
        return response
      }
      if (response && typeof response === 'object' && 'data' in response) {
        return response.data || []
      }
      return []
    } catch (error) {
      console.error('获取大模型简要列表失败:', error)
      throw new Error(`获取大模型列表失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }
}
```

**主要改进：**
- ✅ 更长的缓存时间，适合模型数据
- ✅ 灵活的请求选项
- ✅ 更好的响应格式处理

### 3. Direct API 迁移

#### 原来的实现 (direct-api-service.ts)

```typescript
export class DirectApiService {
  private readonly BASE_URL = '/api'

  async sendMessage(query: string): Promise<any> {
    try {
      const response = await fetch(`${this.BASE_URL}/executor/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query })
      })

      const result = await this.handleResponse(response)
      return await result.json()
    } catch (error) {
      console.error('直接发送任务失败:', error)
      throw new Error(`发送任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  async sendMessageWithStreamingDirect(
    planTemplateId: string,
    request: DirectStreamingRequest
  ): Promise<ReadableStream<Uint8Array> | null> {
    try {
      const url = `${this.BASE_URL}/streaming-events/template/${encodeURIComponent(planTemplateId)}/direct`
      
      const response = await fetch(url, {
        method: 'POST',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      return response.body
    } catch (error) {
      console.error('直接流式请求失败:', error)
      throw error
    }
  }
}
```

#### 新的实现 (direct-api.ts)

```typescript
export class DirectApi extends ApiClient {
  constructor() {
    super('/api', {
      enableLogging: true,
      enableCache: false, // 直接执行不缓存
      enableRetry: true,
      defaultRetryCount: 1, // 减少重试次数
      timeout: 60000 // 60秒超时
    })
  }

  async sendMessage(query: string, options?: RequestOptions): Promise<any> {
    try {
      const response = await this.post<DirectExecuteResponse>(
        '/executor/execute',
        { query },
        {
          timeout: 60000, // 60秒超时
          ...options
        }
      )
      return response
    } catch (error) {
      console.error('直接发送任务失败:', error)
      throw new Error(`发送任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  async sendMessageWithStreamingDirect(
    planTemplateId: string,
    request: DirectStreamingRequest,
    handler: DirectStreamHandler,
    options?: RequestOptions
  ): Promise<void> {
    try {
      const url = `/streaming-events/template/${encodeURIComponent(planTemplateId)}/direct`
      
      const config = {
        url,
        method: 'POST' as const,
        data: request,
        headers: {
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        timeout: 0 // 流式请求不设置超时
      }

      await this.streamHandler.handleSSEStream<DirectStreamEvent>(config, handler)
    } catch (error) {
      console.error('直接流式请求失败:', error)
      handler.onError(error as Error)
    }
  }
}
```

**主要改进：**
- ✅ 统一的流式响应处理
- ✅ 更好的错误处理和回调
- ✅ 支持取消流式请求
- ✅ 自动的超时管理

### 4. Tool Detail API 迁移

#### 原来的实现 (tool-detail-api-service.ts)

```typescript
export class ToolDetailApiService {
  private readonly BASE_URL = '/api/streaming-events'

  async getToolDetail(thinkActId: string | number): Promise<ToolDetailResponse | null> {
    try {
      const response = await fetch(`${this.BASE_URL}/think-act/${thinkActId}/detail`)
      
      if (response.status === 404) {
        console.warn('⚠️ 工具详情不存在:', thinkActId)
        return null
      }

      const result = await this.handleResponse(response)
      return await result.json()
    } catch (error: any) {
      if (error?.response?.status === 404) {
        console.warn('⚠️ 工具详情不存在:', thinkActId)
        return null
      }
      console.error('❌ 获取工具详情失败:', error)
      throw error
    }
  }
}
```

#### 新的实现 (tool-detail-api.ts)

```typescript
export class ToolDetailApi extends ApiClient {
  constructor() {
    super('/api/streaming-events', {
      enableLogging: true,
      enableCache: true,
      defaultCacheTime: 30 * 1000, // 30秒缓存
      enableRetry: true,
      defaultRetryCount: 2
    })
  }

  async getToolDetail(thinkActId: string | number, options?: RequestOptions): Promise<ToolDetailResponse | null> {
    try {
      const response = await this.get<ToolDetailResponse>(
        `/think-act/${thinkActId}/detail`,
        undefined,
        {
          cache: true,
          cacheTime: 30 * 1000, // 30秒缓存
          ...options
        }
      )
      return response
    } catch (error: any) {
      if (error?.status === 404) {
        console.warn('⚠️ 工具详情不存在:', thinkActId)
        return null
      }
      console.error('❌ 获取工具详情失败:', error)
      throw error
    }
  }
}
```

**主要改进：**
- ✅ 自动缓存工具详情
- ✅ 批量请求支持
- ✅ 更好的404处理
- ✅ 统一的错误处理

## 使用方式对比

### 原来的使用方式

```typescript
// 需要手动创建实例
import { DataSourceApiService } from '../api/datasource-api-service'
import { ModelApiService } from '../api/model-api-service'

const dataSourceService = new DataSourceApiService()
const modelService = new ModelApiService()

// 使用
const dataSources = await dataSourceService.getDataSourceList()
const models = await modelService.getBriefModelList()
```

### 新的使用方式

```typescript
// 直接使用单例实例
import { apiServices } from '../apifetch'

// 使用
const dataSources = await apiServices.dataSource.getDataSourceList()
const models = await apiServices.model.getBriefModelList()

// 或者单独导入
import { dataSourceApi, modelApi } from '../apifetch'

const dataSources = await dataSourceApi.getDataSourceList()
const models = await modelApi.getBriefModelList()
```

## 迁移步骤

### 1. 更新导入语句

**原来：**
```typescript
import { DataSourceApiService } from '../api/datasource-api-service'
import { ModelApiService } from '../api/model-api-service'
import { DirectApiService } from '../api/direct-api-service'
import { ToolDetailApiService } from '../api/tool-detail-api-service'
```

**现在：**
```typescript
import { apiServices } from '../apifetch'
// 或者
import { dataSourceApi, modelApi, directApi, toolDetailApi } from '../apifetch'
```

### 2. 更新实例化代码

**原来：**
```typescript
const dataSourceService = new DataSourceApiService()
const modelService = new ModelApiService()
```

**现在：**
```typescript
// 直接使用，无需实例化
const dataSources = await apiServices.dataSource.getDataSourceList()
const models = await apiServices.model.getBriefModelList()
```

### 3. 更新流式请求处理

**原来：**
```typescript
const stream = await directService.sendMessageWithStreamingDirect(templateId, request)
if (stream) {
  const reader = stream.getReader()
  // 手动处理流式数据...
}
```

**现在：**
```typescript
const handler = apiServices.direct.createSimpleStreamHandler(
  (event) => console.log('事件:', event),
  (error) => console.error('错误:', error),
  () => console.log('完成')
)

await apiServices.direct.sendMessageWithStreamingDirect(templateId, request, handler)
```

### 4. 添加错误处理

**原来：**
```typescript
try {
  const result = await service.getData()
} catch (error) {
  console.error('请求失败:', error)
}
```

**现在：**
```typescript
import { apiUtils } from '../apifetch'

try {
  const result = await apiServices.dataSource.getDataSourceList()
} catch (error) {
  if (apiUtils.isNetworkError(error)) {
    console.error('网络错误')
  } else if (apiUtils.isAuthError(error)) {
    console.error('认证错误')
  } else {
    console.error('其他错误:', apiUtils.formatApiError(error))
  }
}
```

## 新功能使用

### 1. 缓存控制

```typescript
// 启用缓存
const result = await apiServices.dataSource.getDataSourceList({
  cache: true,
  cacheTime: 10 * 60 * 1000 // 10分钟
})

// 禁用缓存
const result = await apiServices.dataSource.getDataSourceList({
  cache: false
})

// 清除缓存
apiServices.dataSource.clearCache()
```

### 2. 重试控制

```typescript
// 自定义重试
const result = await apiServices.model.testModel('model-id', {
  retry: true,
  retryCount: 5,
  retryDelay: 2000
})
```

### 3. 批量请求

```typescript
// 批量获取工具详情
const details = await apiServices.toolDetail.getBatchToolDetails(['id1', 'id2', 'id3'])

// 批量测试模型
const testResults = await apiServices.model.batchTestModels(['model1', 'model2'])
```

### 4. 性能监控

```typescript
// 获取统计信息
const stats = apiServices.dataSource.getStats()
console.log('请求统计:', stats)
// {
//   total: 100,
//   success: 95,
//   error: 5,
//   cached: 20,
//   retried: 3,
//   averageTime: 250
// }
```

## 注意事项

1. **向后兼容**：新的 API 服务与原有的接口保持兼容
2. **性能提升**：缓存和请求去重可以显著提升性能
3. **错误处理**：新的错误处理更加统一和友好
4. **类型安全**：更完整的 TypeScript 类型支持
5. **调试友好**：更详细的日志和统计信息

## 总结

新的 fetch 封装层提供了：

- 🚀 **更好的性能**：缓存、去重、批量请求
- 🛡️ **更强的稳定性**：自动重试、错误处理
- 🔧 **更易维护**：统一配置、类型安全
- 📊 **更好的监控**：请求统计、性能分析
- 🌊 **更强的功能**：流式响应、拦截器

建议逐步迁移，可以新功能使用新的 API，现有功能保持不变，确保平滑过渡。
