<template>
  <div class="flex" style="height: 60vh">
    <div class="overflow-y-auto" style="width: 35%">
      <basic-form
        ref="roleForm"
        class="add-account"
        :formList="FormSchema"
        :show-submit="false"
        :formData="formData"
        :show-checkbox="true"
        :is-create="false"
        :disabled="disabled"
        :label-width="'80px'"
      >
        <template #code="{ record, field }">
          <el-input
            v-model="record[field]"
            placeholder="请输入角色编码"
            :disabled="data.isEdit"
          >
            <!-- <template #prepend>
              {{ data.codePrefix }}
            </template> -->
          </el-input>
        </template>
        <template #dataScope="{ record, field }">
          <el-select
            v-model="record[field]"
            placeholder="请选择"
            @change="dataScopeChange"
            s
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </basic-form>
    </div>

    <div class="overflow-y-auto" style="width: 30%">
      <div class="tree-input">
        <el-input placeholder="请输入菜单名称" clearable v-model="treeLabel" />
      </div>
      <z-tree
        :setting="setting"
        :nodes="treeData"
        class="baseTree"
        :name="treeLabel"
        @onCreated="handleCreated"
      />
    </div>
    <div v-show="dataScopeValue === '5'" class="overflow-y-auto" style="width: 35%">
      <div class="tree-input">
        <el-input
          placeholder="至少3个字才能触发自动搜索"
          v-model="searchName"
          @input="handleSearch(0)"
          style="width: calc(100% - 80px)"
        />
        <el-button type="primary" @click="handleSearch(1)" style="margin-left: 10px"
          >搜索</el-button
        >
      </div>
      <VTree
        ref="tree"
        checkable
        v-model="deptIdList"
        titleField="instName"
        :load="getDeptData"
      >
        <template #node="{ node }">
          <span
            :class="node.id == nodeId ? 'active' : ''"
            :style="{
              color: name && node.instName.indexOf(name) > -1 ? 'rgb(166, 0, 0)' : '',
              fontWeight: name && node.instName.indexOf(name) > -1 ? 'bold' : '',
            }"
            >{{ node.instName }}</span
          >
        </template>
      </VTree>
    </div>
  </div>
</template>
<script lang="ts" setup name="PageAddRole">
  import { FormSchema } from './role.data';
  import ZTree from '@cpit-cpvf/tree';
  import '@cpit-cpvf/tree/dist/style.css';
  import VTree from '@wsfe/vue-tree';
  import BasicForm from '/@/components/sys/BasicForm';
  import { PropType, ref, watch } from 'vue';
  import { SaveRoleItem } from '/@/views/upms/api/model/roleSystemModel';
  import { getRoleUserMenu } from '/@/views/upms/api/menu';
  import { FormOptions } from '/@/components/sys/BasicForm/types';
  import { deptAsyn } from '/@/views/upms/api/dept';
  const props = defineProps({
    data: {
      type: Object as PropType<SaveRoleItem>,
      default: () => {},
    },
    open: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    disabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  });

  const emits = defineEmits(['menuCheck', 'deptCheck']);
  const tree = ref<InstanceType<typeof VTree> | null>(null);
  const treeData = ref<any>([]);
  const formData = ref<SaveRoleItem>({
    code: '', // 角色编码
    name: '', // 角色名称
    remark: '', // 描述
    dataScope: '', // 数据权限code
    available: '', // 状态
    description: '', // 描述
    deptIdList: [], // 机构id
    menuIdList: [], // 菜单id
  });
  const roleForm = ref<InstanceType<typeof BasicForm>>();
  const dataScopeValue = ref('0');
  const menuIdList = ref<string[]>([]);
  const deptIdList = ref<string[]>([]);
  const treeLabel = ref('');
  const ztree = ref<any>();
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  const options = [
    {
      value: '1',
      label: '全部',
    },
    {
      value: '2',
      label: '所在机构及以下数据',
    },
    {
      value: '3',
      label: '所在机构数据',
    },
    {
      value: '4',
      label: '仅本人数据',
    },
    {
      value: '5',
      label: '按明细分配',
    },
  ];
  function validData(cb: Function | undefined = undefined) {
    var checkedNodes: any[] = ztree.value.getCheckedNodes();
    menuIdList.value = [];
    for (var i = 0; i < checkedNodes.length; i++) {
      menuIdList.value.push(checkedNodes[i].id);
    }
    formData.value.menuIdList = menuIdList.value;
    roleForm.value?.formValid(cb);
  }

  function dataScopeChange(value: string) {
    dataScopeValue.value = value;
  }
  const zOnCheck = () => {
    var checkedNodes: any[] = ztree.value.getCheckedNodes();
    menuIdList.value = [];
    for (var i = 0; i < checkedNodes.length; i++) {
      menuIdList.value.push(checkedNodes[i].id);
    }
    emits('menuCheck', menuIdList.value);
  };

  const setting = ref<any>({
    data: {
      simpleData: {
        enable: true,
        pIdKey: 'parentId',
      },
      key: {
        name: 'name',
      },
    },
    check: {
      enable: true, //显示复选框
      chkStyle: 'checkbox',
      chkboxType: { Y: 'ps', N: 'ps' },
    },
    view: {
      showIcon: false,
      dblClickExpand: false, // 双击不允许展开节点
    },
    callback: {
      onCheck: zOnCheck,
    },
  });
  const handleCreated = (ztreeObj) => {
    ztree.value = ztreeObj;
    if (formData.value.menuIdList && formData.value.menuIdList.length > 0) {
      for (let i = 0; i < formData.value.menuIdList.length; i++) {
        var node =
          ztree.value &&
          ztree.value.getNodesByParam('id', formData.value.menuIdList[i], null)[0]; //根据id获取节点

        if (node != null) {
          ztree.value && ztree.value.checkNode(node, true, false); //选中节点
          ztree.value && ztree.value.expandNode(node, true, true, true); //展开选中节点
        }
      }
    }
    if (props.disabled) {
      var nodesSys = ztree.value.getNodes(); //可以获取所有的父节点
      var nodesSysAll = ztree.value.transformToArray(nodesSys); //获取树所有节点
      for (var i = 0; i < nodesSysAll.length; i++) {
        ztree.value && ztree.value.setChkDisabled(nodesSysAll[i], true);
      }
    } else {
      var nodesSys = ztree.value.getNodes(); //可以获取所有的父节点
      var nodesSysAll = ztree.value.transformToArray(nodesSys); //获取树所有节点
      for (var i = 0; i < nodesSysAll.length; i++) {
        ztree.value && ztree.value.setChkDisabled(nodesSysAll[i], false);
      }
    }
  };

  const getMenuTreeData = () => {
    getRoleUserMenu({ opType: 'project' }).then((res) => {
      for (let i = 0; i < res.length; i++) {
        if (res[i]?.menuType === 'system') {
          res[i].name = res[i].name + '--系统';
        }
        if (res[i]?.menuType === 'tenant') {
          res[i].name = res[i].name + '--租户';
        }
        if (res[i]?.menuType === 'project') {
          res[i].name = res[i].name + '--项目';
        }
      }
      treeData.value = res;
    });
  };
  getMenuTreeData();
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  watch(
    () => props.data,
    (value) => {
      formData.value = value;
      dataScopeValue.value = value?.dataScope;
      deptIdList.value = formData.value.deptIdList || [];
      FormSchema.forEach((item: FormOptions) => {
        if (item.field == 'code') {
          if (!formData.value.id) {
            item.ifShow = false;
          } else {
            item.ifShow = true;
            item.componentProps.disabled = true;
          }
        }
      });
    },
    {
      immediate: true,
    },
  );
  watch(
    () => deptIdList.value,
    () => {
      emits('deptCheck', deptIdList.value);
    },
  );
  defineExpose({ validData });
</script>
<route lang="yaml">
meta:
  auth: true
</route>

<style lang="scss" scoped>
  .tree-content {
    height: calc(100vh - 58px);
    overflow-x: hidden;
    padding-left: 10px;
    :deep(.vtree-tree-search__search) {
      width: 100%;
      display: flex;
    }
  }
  .tree-input {
    margin: 10px 10px 10px;
  }
</style>
