<template>
  <div class="content-model">
    <div class="box">
      <!-- <p class="title"> 角色列表 </p> -->
      <basic-search
        :searchArray="searchFormSchema"
        class="search-all"
        ref="searchRef"
        :labelWidth="80"
        :labelShow="false"
        :btnShow="false"
        @onSearch="getRoleList"
      />
      <div class="table-content">
        <basic-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :total="page.total"
          :page-size="page.pageSize"
          :current-page="page.pageNum"
          @page-change="pageChange"
          @size-change="sizeChange"
          @selectionChange="handleChangeCheck"
        >
          <template #builtIn="{ record }">
            <el-tag v-if="record.builtIn === 1" type="danger">系统内置</el-tag>
            <el-tag v-else>自定义</el-tag>
          </template>
        </basic-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { columns, searchFormSchema } from './role.data';
  import { getRoleListByPage } from '/@/views/upms/api/roleSystem';
  import {
    RoleListItem,
    RolePageListGetResultModel,
  } from '/@/views/upms/api/model/roleSystemModel';
  import BasicTable from '/@/components/sys/BasicTable';
  const tableData = ref<RoleListItem[]>([]);
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  interface roleSearch {
    code?: string;
    name?: string;
    available?: string;
  }
  let emits = defineEmits(['selectRoles']);
  const selectDatas = ref<any>([]);
  const tableRef = ref<InstanceType<typeof BasicTable>>(null);
  const searchData = ref<roleSearch>({});
  const getRoleList = () => {
    const { code, name, available } = searchData.value;
    getRoleListByPage({
      code,
      name,
      available,
      pageNum: page.value.pageNum,
      pageSize: page.value.pageSize,
      opType: 'project',
    }).then((res: RolePageListGetResultModel) => {
      tableData.value = res.list;
      page.value.total = res.total;
    });
  };
  /**
   * 切换分页，每页显示数量
   */
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    getRoleList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    getRoleList();
  };
  const handleChangeCheck = (val) => {
    selectDatas.value = val;
    emits('selectRoles', selectDatas.value);
  };
  getRoleList();

  const handleSetSelectData = (row) => {
    tableRef.value.setSelectData(row);
  };
  defineExpose({
    handleSetSelectData,
  });
</script>

<style scoped lang="scss">
  .content-model {
    display: flex;
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: 100%;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 68px;
      font-size: 18px;
      padding-left: 24px;
      background: #ffffff;
      font-weight: 700;
    }

    .search-all {
      background: #ffffff;
      padding-left: 12px;
      padding-top: 8px;
    }
    .table-content {
      margin-top: 10px;
      background: #ffffff;
      height: calc(100% - 90px);
      padding: 10px 16px 0;
      position: relative;
      overflow: auto;
      .top-button {
        position: absolute;
        top: 10px;
        left: 24px;
        z-index: 9;
      }
      .tip {
        position: absolute;
        top: 22px;
        right: 70px;
        z-index: 9;
        color: #409eff;
      }

      .active-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #00a854;
        margin-right: 5px;
      }
      .close-span {
        display: inline-block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #bfbfbf;
        margin-right: 5px;
      }
    }
  }

  h4 {
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 700;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
</style>
