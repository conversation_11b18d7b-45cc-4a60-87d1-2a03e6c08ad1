import { ref, nextTick } from 'vue';
import { Option } from '/@/components/sys/BasicForm/types';
export default function useUser(formData, userOptions, postSelect, userListRef) {
  const visibleUser = ref(false);
  const selectUserData = ref<any>([]);
  const selectUserCurrent = ref<any>({});
  const handleFocusUserEvent = (e) => {
    visibleUser.value = true;
    nextTick(() => {
      //userSelect.value && userSelect.value?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      if (selectUserData.value.id) {
        if (formData.value.employeeId) {
          userListRef.value &&
            userListRef.value?.handleSetCurrentRow(selectUserData.value);
        } else {
          userListRef.value && userListRef.value?.handleSetCurrentRow(null);
        }
      }
    });
  };
  const handleChangeUser = (data) => {
    if (data === '' || data === null) {
      selectUserData.value = {};
      userListRef.value && userListRef.value?.handleSetCurrentRow(null);
    }
  };
  const currentRowSelect = (data) => {
    if (data) {
      selectUserCurrent.value = data;
    } else {
      selectUserCurrent.value = {};
    }
  };
  const saveUserInfo = () => {
    if (!selectUserCurrent.value.id) {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择要关联的员工',
      });
      return true;
    }

    selectUserData.value = selectUserCurrent.value;
    userOptions.value = [];
    const obj: Option = {
      label: selectUserData.value.empName + '/' + selectUserData.value.empCode,
      value: selectUserData.value.id,
    };
    userOptions.value.push(obj);
    formData.value.employeeId = selectUserData.value.id;
    formData.value.deptId = selectUserData.value.deptId
      ? [selectUserData.value.deptId]
      : [];
    formData.value.deptName = selectUserData.value.instName;
    visibleUser.value = false;
  };
  return {
    handleChangeUser,
    handleFocusUserEvent,
    visibleUser,
    selectUserData,
    currentRowSelect,
    selectUserCurrent,
    saveUserInfo,
  };
}
