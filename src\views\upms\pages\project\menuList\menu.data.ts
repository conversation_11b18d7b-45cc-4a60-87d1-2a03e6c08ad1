// import { sortReg } from '../../common/utils/regList';
import { FormOptions } from '/@/components/sys/BasicForm/types';
import { cloneDeep } from 'lodash-es';
import { ref } from 'vue';
export const menuFormSchema = ref<FormOptions[]>([
  {
    field: 'name',
    component: 'Input',
    label: '资源名称',
    colProps: {
      span: 24,
    },
    required: true,
    rules: [{ max: 30, message: '超过长度限制，最多30字' }],
  },
  {
    field: 'type',
    component: 'RadioGroup',
    label: '资源类型',
    colProps: {
      span: 24,
    },
    componentProps: {
      options: [
        {
          label: '菜单',
          value: '0',
        },
        {
          label: '按钮',
          value: '1',
        },
        {
          label: '微应用',
          value: '2',
        },
        {
          label: '接口权限',
          value: '3',
        },
      ],
    },
    onChange: (value) => {
      const permission: FormOptions | undefined = menuFormSchema.value.find(
        (item) => item['field'] === 'permission',
      );
      // permission = true;
      if (permission) {
        const props = cloneDeep(permission.componentProps || {});
        props.disabled = false;
        permission.componentProps = props;
      }
      console.log('value', value, permission);
    },
  },
  {
    field: 'icon',
    label: '图标',
    component: 'Input',
    slot: 'icon',
    ifShow: (values) => {
      return values.type !== '1' && values.type !== '3';
    },
  },
  {
    field: 'path',
    component: 'Input',
    label: '路由',
    required: true,
    colProps: {
      span: 24,
    },
    ifShow: (values) => {
      return values.type == 0;
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字' }],
  },
  {
    field: 'permission',
    component: 'Input',
    label: '权限编码',
    required: true,
    colProps: {
      span: 24,
    },
    componentProps: {
      disabled: true,
    },
    ifShow: (values) => {
      return values.type === '1' || values.type === '3';
    },
  },
  {
    field: 'microId',
    label: '微应用',
    component: 'Select',
    componentProps: {
      options: [],
    },
    required: true,
    ifShow: (values) => {
      return values.type == 2;
    },
  },
  {
    field: 'path',
    component: 'Input',
    label: '路由',
    required: true,
    colProps: {
      span: 24,
    },
    ifShow: (values) => {
      return values.type == 2;
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字' }],
  },
  {
    field: 'microPath',
    component: 'Input',
    label: '微应用路由',
    required: true,
    colProps: {
      span: 24,
    },
    ifShow: (values) => {
      return values.type == 2;
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字' }],
  },
  {
    field: 'hideMenu',
    component: 'RadioGroup',
    label: '菜单是否隐藏',
    required: true,
    colProps: {
      span: 24,
    },
    componentProps: {
      options: [
        {
          label: '否',
          value: '0',
        },
        {
          label: '是',
          value: '1',
        },
      ],
    },
    ifShow: (values) => {
      return values.type === '0' || values.type === '2';
    },
  },
  {
    field: 'display',
    component: 'RadioGroup',
    label: '是否启用',
    colProps: {
      span: 24,
    },
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '禁用',
          value: 0,
        },
      ],
    },
  },
  {
    field: 'description',
    component: 'Input',
    label: '描述',
    colProps: {
      span: 24,
    },
    rules: [{ max: 200, message: '超过长度限制，最多200字' }],
  },
  {
    field: 'meta',
    component: 'Input',
    label: 'meta配置',
    slot: 'meta',
    ifShow: (values) => {
      return values.type === '0' || values.type === '2';
    },
  },
]);
