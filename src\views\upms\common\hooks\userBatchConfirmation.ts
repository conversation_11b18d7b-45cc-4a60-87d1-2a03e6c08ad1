import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getBatchConfirmation } from '/@/api/sys';
export default function userBatchConfirmation(selectionIds, callDelete) {
  const batchConfirmationVisible = ref(false);
  const password = ref('');
  const batchConfirmationLoading = ref(false);

  const batchDeletion = () => {
    batchConfirmationVisible.value = true;
  };
  const batchConfirmationSave = () => {
    batchConfirmationLoading.value = true;
    if (!password.value) {
      ElMessage({
        message: '请输入密码确认！',
        grouping: true,
        type: 'warning',
      });
      batchConfirmationLoading.value = false;
    } else {
      console.log(password.value);
      batchConfirmationLoading.value = true;
      getBatchConfirmation({ userPasswd: password.value })
        .then(() => {
          // 调用删除 和 刷新列表
          callDelete(selectionIds.value);
          batchConfirmationVisible.value = false;
          batchConfirmationLoading.value = false;
        })
        .catch(() => {
          batchConfirmationLoading.value = false;
        });
    }
  };
  const getPassword = (value) => {
    password.value = value;
  };
  return {
    batchConfirmationVisible,
    batchDeletion,
    batchConfirmationSave,
    getPassword,
    batchConfirmationLoading,
  };
}
