# 执行结果智能渲染改造说明

## 概述

本次改造实现了对聊天界面中工具执行结果的智能渲染，根据不同的数据格式自动选择合适的展示方式：

1. **表格数据** - 使用 VTable 组件渲染
2. **图表数据** - 使用 ECharts 渲染折线图
3. **SQL语句** - 格式化显示SQL代码
4. **其他数据** - 保持原有的JSON格式显示

## 数据格式分析

### 1. detailtable.json - 表格数据
```json
{
  "actionResult": {
    "retrievedKnowledge": {
      "tableSchemas": [
        {
          "tableType": "TABLE",
          "tableName": "user_data",
          "data": [
            {"id": 1, "name": "张三", "age": 25, "department": "技术部", "salary": 8000},
            // ... 更多数据
          ]
        }
      ]
    }
  }
}
```

**识别条件**: `tableType` 为 "TABLE" 且包含 `data` 数组

### 2. detail.json - 图表数据
```json
{
  "actionResult": {
    "chartType": "line",
    "select_data": [
      {"date": "2024-01-01", "sales": 1200, "profit": 300},
      // ... 更多数据点
    ]
  }
}
```

**识别条件**: `chartType` 为 "line" 且包含 `select_data` 数组

### 3. detailsql.json - SQL语句
```json
{
  "actionResult": {
    "message": "SELECT u.id, u.name, u.age, u.department, u.salary \nFROM user_data u \nWHERE u.department = '技术部' \nAND u.salary > 8000 \nORDER BY u.salary DESC;",
    "executionTime": "0.025s",
    "rowsAffected": 2
  }
}
```

**识别条件**: `message` 字段包含SQL关键字（SELECT、INSERT、UPDATE、DELETE）

## 核心实现

### 1. 类型识别函数
```javascript
const getResultRenderType = (result) => {
  if (!result || !result.actionResult) return 'json'
  
  const actionResult = result.actionResult
  
  // 检查表格数据
  if (actionResult.retrievedKnowledge?.tableSchemas) {
    const hasTableData = actionResult.retrievedKnowledge.tableSchemas.some(
      (schema) => schema.tableType === 'TABLE' && schema.data && schema.data.length > 0
    )
    if (hasTableData) return 'table'
  }
  
  // 检查图表数据
  if (actionResult.chartType === 'line' && actionResult.select_data && actionResult.select_data.length > 0) {
    return 'chart'
  }
  
  // 检查SQL语句
  if (actionResult.message && typeof actionResult.message === 'string') {
    const message = actionResult.message.trim().toUpperCase()
    if (message.includes('SELECT') || message.includes('INSERT') || message.includes('UPDATE') || message.includes('DELETE')) {
      return 'sql'
    }
  }
  
  return 'json'
}
```

### 2. 模板渲染
```vue
<template>
  <div v-if="selectedTool.result" class="tool-detail-section">
    <h4>执行结果</h4>
    
    <!-- 表格渲染 -->
    <div v-if="getResultRenderType(selectedTool.result) === 'table'" class="result-table-container">
      <VTable 
        :data="getTableData(selectedTool.result)" 
        :columns="getTableColumns(selectedTool.result)"
        height="400px"
      />
    </div>
    
    <!-- 图表渲染 -->
    <div v-else-if="getResultRenderType(selectedTool.result) === 'chart'" class="result-chart-container">
      <div 
        :id="`tool-chart-${selectedTool.stepId}`" 
        class="chart-canvas" 
        style="width: 100%; height: 400px;"
      ></div>
    </div>
    
    <!-- SQL渲染 -->
    <div v-else-if="getResultRenderType(selectedTool.result) === 'sql'" class="result-sql-container">
      <div class="sql-header">
        <Icon icon="carbon:sql" />
        <span>SQL语句</span>
      </div>
      <div class="sql-content">
        <pre class="sql-code">{{ getSqlMessage(selectedTool.result) }}</pre>
      </div>
    </div>
    
    <!-- 默认JSON渲染 -->
    <div v-else class="json-viewer">
      <pre>{{ formatToolJson(selectedTool.result) }}</pre>
    </div>
  </div>
</template>
```

## 测试方法

在聊天页面的欢迎消息中添加了三个测试按钮：

1. **测试表格渲染** - 模拟 detailtable.json 数据
2. **测试图表渲染** - 模拟 detail.json 数据  
3. **测试SQL渲染** - 模拟 detailsql.json 数据

点击按钮后会在右侧面板显示相应的渲染结果。

## 兼容性处理

- 保持原有的JSON显示作为后备方案
- 所有新的渲染类型都是在原有基础上的增强
- 不影响现有的工具调用和数据流程
- 支持动态类型识别，可以处理同一接口返回的不同格式数据

## 文件修改清单

1. `src/views/chatagent/pages/chat/index.vue` - 主要改造文件
   - 添加了智能渲染逻辑
   - 新增了类型识别函数
   - 添加了图表渲染方法
   - 增加了测试功能

2. `src/views/chatagent/components/VTable.vue` - 表格组件（已存在）
   - 用于渲染表格数据

3. 测试数据文件：
   - `src/views/chatagent/test-data/detail.json`
   - `src/views/chatagent/test-data/detailtable.json`
   - `src/views/chatagent/test-data/detailsql.json`

## 使用说明

1. 启动开发服务器：`npm run dev`
2. 访问聊天页面：`http://localhost:5001/chatagent/chat`
3. 点击欢迎消息中的测试按钮查看不同类型的渲染效果
4. 在实际使用中，当工具返回符合格式的数据时，会自动选择合适的渲染方式

## 扩展性

该实现具有良好的扩展性，可以轻松添加新的数据类型支持：

1. 在 `getResultRenderType` 函数中添加新的识别逻辑
2. 在模板中添加对应的渲染组件
3. 实现相应的数据提取和渲染方法

这样的设计确保了系统能够灵活应对不同类型的数据展示需求。
