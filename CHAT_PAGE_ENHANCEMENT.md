# Chat Page Enhancement - 默认助手逻辑

## 功能概述

为 `src/views/chatagent/pages/chat/index.vue` 添加了以下逻辑：

当页面路由上获取不到 `id` 和 `name` 参数时，默认获取助手列表的第一个信息，获取 `planTemplateId` 请求历史会话，获取 `title` 赋值给 `assistantName`。

## 主要修改

### 1. 更新 onMounted 函数

```typescript
onMounted(async () => {
  // ... 现有代码 ...

  // 从路由参数获取助手名称和ID
  const routeId = route.query.id as string || ''
  const routeName = route.query.name as string || ''
  
  console.log('路由获取的参数:', { id: routeId, name: routeName })

  // 并行加载模型列表和助手列表
  await Promise.all([
    loadModelList(),
    loadAssistantsList()
  ])

  // 处理路由参数逻辑
  if (!routeId && !routeName) {
    // 当页面路由上获取不到id和name时，默认获取助手列表的第一个信息
    console.log('路由参数为空，使用默认助手')
    await handleDefaultAssistant()
  } else {
    // 有路由参数时，设置助手名称和planTemplateId
    assistantName.value = decodeURIComponent(routeName) || ''
    if (routeId) {
      newChatSettings.value.planTemplateId = routeId
      console.log('设置planTemplateId:', routeId)
      // 加载该助手的历史会话
      await refreshHistory()
    }
  }
})
```

### 2. 新增 handleDefaultAssistant 函数

```typescript
// 处理默认助手逻辑
const handleDefaultAssistant = async () => {
  try {
    if (assistantsList.value && assistantsList.value.length > 0) {
      const firstAssistant = assistantsList.value[0]
      console.log('使用第一个助手:', firstAssistant)
      
      // 设置助手名称
      assistantName.value = firstAssistant.title || firstAssistant.name || 'ChatBI'
      
      // 设置planTemplateId - 根据实际API响应结构，可能是id字段就是planTemplateId
      const planTemplateId = firstAssistant.planTemplateId || firstAssistant.id
      if (planTemplateId) {
        newChatSettings.value.planTemplateId = planTemplateId
        console.log('默认设置planTemplateId:', planTemplateId)
        
        // 获取该助手的历史会话
        await loadAssistantHistory(planTemplateId)
      } else {
        console.warn('第一个助手没有planTemplateId或id字段')
        // 如果没有planTemplateId，设置空的历史
        chatHistory.value = []
      }
    } else {
      console.warn('助手列表为空，使用默认配置')
      assistantName.value = 'ChatBI'
      // 设置空的历史
      chatHistory.value = []
    }
  } catch (error) {
    console.error('处理默认助手失败:', error)
    assistantName.value = 'ChatBI'
    chatHistory.value = []
  }
}
```

### 3. 更新 refreshHistory 函数

```typescript
const refreshHistory = async () => {
  historyLoading.value = true
  try {
    console.log('📚 加载聊天历史')
    
    // 获取planTemplateId，优先使用newChatSettings中的，其次使用路由参数
    const planTemplateId = newChatSettings.value.planTemplateId || route.query.id as string
    
    if (!planTemplateId) {
      console.warn('⚠️ 没有planTemplateId，无法加载历史会话')
      chatHistory.value = []
      return
    }

    console.log('📚 使用planTemplateId加载历史:', planTemplateId)
    const response = await AxiosApiService.getChatSessionsPaginated(currentUserId.value, planTemplateId)
    
    // ... 处理响应数据的现有逻辑 ...
  } catch (error) {
    console.error('❌ 加载历史失败:', error)
    chatHistory.value = []
  } finally {
    historyLoading.value = false
  }
}
```

### 4. 更新 Agent 接口定义

在 `src/views/chatagent/api/axios-api-service.ts` 中添加了 `planTemplateId` 字段：

```typescript
// Agent 相关接口
export interface Agent {
  id: string
  name?: string
  description?: string
  availableTools?: string[]
  nextStepPrompt?: string
  userRequest?: string
  title?: string
  updateTime?: string
  planTemplateId?: string  // 添加 planTemplateId 字段
}
```

## 功能流程

1. **页面加载时**：检查路由参数 `id` 和 `name`
2. **如果路由参数为空**：
   - 加载助手列表
   - 选择第一个助手
   - 设置助手名称（优先使用 `title`，其次 `name`，最后默认 'ChatBI'）
   - 设置 `planTemplateId`（优先使用 `planTemplateId` 字段，其次使用 `id` 字段）
   - 使用 `planTemplateId` 加载历史会话
3. **如果路由参数存在**：
   - 直接使用路由参数设置助手名称和 `planTemplateId`
   - 加载对应的历史会话

## 错误处理

- 如果助手列表为空，使用默认配置
- 如果获取历史会话失败，设置空的历史列表
- 所有错误都会在控制台输出详细日志

## 兼容性

- 保持了原有的路由参数处理逻辑
- 向后兼容现有的功能
- 不影响已有的助手选择和历史会话加载功能
