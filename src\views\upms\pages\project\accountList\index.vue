<template>
  <div class="content">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane class="pane-left" size="20" min-size="20">
        <div class="left">
          <div class="tree-content">
            <div class="tree-div">
              <VTreeSearch
                ref="tree"
                selectable
                :load="getDeptData"
                @click="handleTreeClick"
              >
                <template #actions>
                  <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
                </template>
                <template #search-input>
                  <el-input
                    class="search-input"
                    placeholder="至少3个字才能触发自动搜索"
                    v-model="searchName"
                    @input="handleSearch(0)"
                  />
                </template>
                <template #node="{ node }">
                  <span
                    :class="node.id == nodeId ? 'active' : ''"
                    :style="{
                      color:
                        name && node.instName.indexOf(name) > -1
                          ? 'rgb(166, 0, 0)'
                          : '',
                      fontWeight:
                        name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                    }"
                    >{{ node.instName }}</span
                  >
                </template>
              </VTreeSearch>
            </div>
          </div></div
        >
      </Pane>
      <Pane class="mid" size="80">
        <!--右侧div内容-->
        <div class="right">
          <div class="search-div">
            <p class="title"> 项目账号搜索</p>
            <basic-search
              :searchArray="searchFormSchema"
              class="search-all"
              ref="searchRef"
              :labelWidth="80"
              :labelShow="false"
              @reset="accountList"
              @onSearch="accountList"
            >
              <template #sub>
                <el-checkbox
                  v-model="sub"
                  label="查看本机构及下属机构账号"
                  @change="handleCheckBox"
                />
              </template>
            </basic-search>
          </div>
          <div class="table-content">
            <div class="table-top-div">
              <p>项目账号列表</p>
              <div class="top-button">
                <el-button
                  type="primary"
                  @click="handleInviteUser"
                  v-auth="['project_user_add']"
                  :icon="Plus"
                  >邀请成员</el-button
                >
                <el-button
                  type="danger"
                  @click="batchDeletion"
                  :disabled="selectionIds.length > 0 ? false : true"
                  v-auth="['project_user_edit']"
                  :icon="Delete"
                  plain
                  >批量移除</el-button
                >
                <!--<el-button
                  type="primary"
                  @click="downLoad(false)"
                  v-auth="['project_user_export']"
                  :icon="Download"
                  plain
                  >导出</el-button
                >
                <el-button
                  type="primary"
                  @click="fileVisible = true"
                  v-auth="['project_user_import']"
                  :icon="Upload"
                  plain
                  >导入</el-button
                >-->
              </div>
            </div>
            <basic-table
              ref="tableRef"
              :columns="columns"
              :data="tableData"
              :total="page.total"
              :page-size="page.pageSize"
              :current-page="page.pageNum"
              @page-change="pageChange"
              @size-change="sizeChange"
              @selectionChange="handleCurrentChange"
              @sortChange="sortChange"
              :downSetting="true"
              height="calc(100vh - 436px)"
            >
              <template #available="{ record }">
                <el-tag v-if="record.available === '1'">已激活</el-tag>
                <el-tag v-else-if="record.available === '0'" type="danger"
                  >已禁用</el-tag
                >
                <el-tag v-else type="info">已锁定</el-tag>
              </template>
              <template #roleIdList="{ record }">
                <div v-if="record.roleIdList.length > 0">
                  <el-tag
                    v-for="item in record.roleNames.split(',')"
                    :key="item"
                    style="margin-right: 5px; margin-bottom: 3px"
                    >{{ item }}</el-tag
                  >
                </div>
              </template>
              <template #action="{ record }">
                <el-button
                  type="primary"
                  link
                  @click="handleDelete(record, 0)"
                  v-auth="['project_user_delete']"
                  >移除</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="handleEditOrDetail(record, 'edit')"
                  v-show="record.available === '2' || record.available === '1'"
                  v-auth="['project_user_edit']"
                  >编辑</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="handleEditOrDetail(record, 'detail')"
                  v-show="record.available === '2' || record.available === '1'"
                  v-auth="['project_user_details']"
                  >查看</el-button
                >
                <el-button
                  type="primary"
                  link
                  v-if="record.available === '1' && getWebModel() === 'mix'"
                  @click="handleAuth(record, true)"
                  v-auth="['project_user_get_auths']"
                  >查看授权</el-button
                >
                <el-button
                  type="primary"
                  link
                  v-if="record.available === '1' && getWebModel() === 'mix'"
                  v-auth="['project_user_role_auths']"
                  @click="handleAuth(record, false)"
                  >授权操作</el-button
                >
              </template>
            </basic-table>
          </div>
        </div>
      </Pane>
    </Splitpanes>
    <el-drawer v-model="drawer" direction="rtl" size="600px" :destroy-on-close="true">
      <template #header>
        <h4>{{ title }}</h4>
      </template>
      <template #default>
        <basic-form
          class="add-account"
          :formList="accountFormSchema"
          :isCreate="false"
          :formData="formData"
          :showSubmit="false"
          :check-strictly="true"
          ref="formAccount"
          :disabled="!isEdit ? true : null"
        >
          <template #user>
            <el-select
              style="width: 100%"
              v-model="formData.employeeId"
              clearable
              @focus="handleFocusUserEvent"
              @change="handleChangeUser"
              ref="userSelect"
              :disabled="true"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            /></el-select>
          </template>

          <template #postStationIdList>
            <el-select
              style="width: 100%"
              v-model="formData.postStationIdList"
              clearable
              @focus="handleFocusPostEvent"
              @change="handleChangePost"
              ref="postSelect"
              :disabled="true"
            >
              <el-option
                v-for="item in postOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            /></el-select>
          </template>

          <template #roleIdList>
            <el-select
              style="width: 100%"
              v-model="formData.roleIdList"
              clearable
              multiple
              @focus="handleFocusRoleEvent"
              @change="handleChangeRole"
              ref="roleSelect"
              @remove-tag="removeTag"
            >
              <el-option
                v-for="item in roleOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            /></el-select>
          </template>
        </basic-form>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取消</el-button>
          <el-button type="primary" @click="confirmClick" v-if="isEdit">确定</el-button>
        </div>
      </template>
    </el-drawer>

    <el-dialog
      v-model="visibleUser"
      title="选择员工"
      width="60%"
      :destroy-on-close="true"
    >
      <user-list
        @currentRowSelect="currentRowSelect"
        ref="userListRef"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleUser = false">取消</el-button>
        <el-button type="primary" @click="saveUserInfo">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="visiblePost"
      title="选择岗位"
      width="60%"
      :destroy-on-close="true"
    >
      <post-list
        ref="postListRef"
        @currentRowSelect="currentRowPostSelect"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visiblePost = false">取消</el-button>
        <el-button type="primary" @click="savePost">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="visibleRole"
      title="选择角色"
      width="60%"
      :destroy-on-close="true"
    >
      <role-list
        ref="roleListRef"
        @selectRoles="selectRoles"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleRole = false">取消</el-button>
        <el-button type="primary" @click="saveRole">确定</el-button>
      </template>
    </el-dialog>

    <!-- 邀请员工 -->
    <el-drawer
      v-model="drawerAccount"
      direction="rtl"
      size="600px"
      :destroy-on-close="true"
    >
      <template #header>
        <h4>邀请成员</h4>
      </template>
      <basic-form
        :formList="inviteFormSchema"
        :isCreate="false"
        :formData="inviteFormData"
        :showSubmit="false"
        :check-strictly="true"
        ref="formInvite"
      >
        <template #userId>
          <el-select
            style="width: 100%"
            v-model="inviteFormData.userId"
            clearable
            multiple
            @focus="handleFocusAccountEvent"
            @change="handleChangeAccount"
            @remove-tag="removeTagAccount"
            ref="accountSelect"
          >
            <el-option
              v-for="item in accountOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          /></el-select>
        </template>

        <template #roleIds>
          <el-select
            style="width: 100%"
            v-model="inviteFormData.roleIds"
            clearable
            multiple
            @focus="handleFocusRoleInviteEvent"
            @change="handleChangeInviteRole"
            ref="roleSelect"
            @remove-tag="removeTag"
          >
            <el-option
              v-for="item in roleOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          /></el-select>
        </template>
      </basic-form>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawerAccount = false">取消</el-button>
          <el-button type="primary" @click="inviteConfirmClick">确定</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- 选择账号 -->
    <el-dialog
      v-model="visibleAccount"
      title="选择账号"
      width="60%"
      :destroy-on-close="true"
    >
      <acc-list
        @selectAccounts="selectAccounts"
        ref="accountListRef"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleAccount = false">取消</el-button>
        <el-button type="primary" @click="saveAccount">确定</el-button>
      </template>
    </el-dialog>

    <el-drawer
      v-model="drawerAuth"
      direction="rtl"
      size="600px"
      :destroy-on-close="true"
    >
      <template #header>
        <h4>{{ authTitle }}</h4>
      </template>
      <template #default>
        <el-form :model="formroleAuths" ref="authsFormRef" :disabled="authDisabled">
          <div
            v-for="(item, index) in formroleAuths.roleAuths"
            :key="index"
            class="item-div"
          >
            <div class="item-form">
              <el-form-item
                :label="'机构' + (index + 1)"
                :prop="`roleAuths[${index}].deptId`"
                :rules="{
                  required: true,
                  message: '请选择机构',
                  trigger: ['blur', 'change'],
                }"
              >
                <el-select
                  style="width: 70%"
                  v-model="item.deptId"
                  clearable
                  @focus="(e) => handleFocusOrgEvent(e, index)"
                  @change="handleChangeOrg"
                  ref="orgSelect"
                  :disabled="item.deptType === '0' ? true : false"
                >
                  <el-option
                    v-for="it in item.orgOptions"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                /></el-select>
                <el-switch
                  v-model="switchArr[index]['sortDefault']"
                  class="ml-2"
                  inline-prompt
                  style="--el-switch-on-color: #13ce66"
                  active-text="默认排序"
                  @change="(e) => changeSwitch(e, index, 'sortDefault')"
                />
                <el-switch
                  v-model="switchArr[index]['status']"
                  class="ml-2"
                  inline-prompt
                  style="--el-switch-on-color: #13ce66"
                  active-text="待失效"
                  @change="(e) => changeSwitch(e, index, 'status')"
                />
              </el-form-item>
              <el-form-item
                :label="'角色' + (index + 1)"
                :prop="`roleAuths[${index}].roleIdsList`"
                :rules="{
                  required: true,
                  message: '请选择角色',
                  trigger: ['blur', 'change'],
                }"
              >
                <el-select
                  style="width: 100%"
                  v-model="item.roleIdsList"
                  clearable
                  multiple
                  @focus="(e) => handleFocusRoleChargeEvent(e, index)"
                  @change="handleChangeRoleCharge(item.roleIdsList, index)"
                  @remove-tag="removeTagCharge(item.roleIdsList, index)"
                  ref="roleChargeSelect"
                  :disabled="item.deptType === '0' ? true : false"
                >
                  <el-option
                    v-for="it in item.roleOptions"
                    :key="it.value"
                    :label="it.label"
                    :value="it.value"
                /></el-select>
              </el-form-item>
            </div>
            <div class="item-btn" v-if="!authDisabled">
              <el-icon
                :size="24"
                color="#FF4950"
                class="cursor-pointer"
                @click="removeRoleAuths(index)"
                v-if="item.deptType === '0' ? false : true"
              >
                <RemoveFilled />
              </el-icon>
              <el-icon
                :size="24"
                color="#409EFC"
                class="ml-2 cursor-pointer"
                @click="addRoleAuths"
              >
                <CirclePlusFilled />
              </el-icon>
            </div>
          </div>
        </el-form>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawerAuth = false">取消</el-button>
          <el-button type="primary" @click="saveRoleAuths(authsFormRef)"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>

    <el-dialog
      v-model="visibleOrg"
      title="选择机构"
      width="40%"
      :destroy-on-close="true"
    >
      <org-list
        ref="orgRef"
        @currentOrgSelect="currentOrgSelect"
        style="height: calc(100vh - 360px)"
      />
      <template #footer>
        <el-button @click="visibleOrg = false">取消</el-button>
        <el-button type="primary" @click="saveOrg">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="batchConfirmationVisible"
      title="移除警告"
      width="400px"
      class="batch-Confirmation-dialog"
      :destroy-on-close="true"
    >
      <BatchConfirmation @getPassword="getPassword" />
      <template #footer>
        <el-button @click="batchConfirmationVisible = false">取消</el-button>
        <el-button
          type="info"
          @click="batchConfirmationSave"
          :loading="batchConfirmationLoading"
          >移除</el-button
        >
      </template>
    </el-dialog>
    <el-dialog
      v-model="fileVisible"
      title="文件导入"
      width="600px"
      align-center
      destroy-on-close
      class="file-dialog"
    >
      <el-button type="primary" link @click="downLoad(true)" style="margin-bottom: 12px"
        >下载账号导入模板</el-button
      >
      <el-upload
        ref="upload"
        class="upload-demo"
        drag
        accept=".xls,.xlsx"
        :limit="1"
        :show-file-list="true"
        :auto-upload="false"
        :on-exceed="handleExceed"
        :on-change="handleChange"
        action="javascript:void(0)"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将文件拖拽到此处 <br /><em>或者，您可以单击此处选择一个文件</em>
        </div>
        <template #tip>
          <div class="el-upload__tip" style="color: red">
            注：只支持xls,xlsx文件类型的文件</div
          >
        </template>
      </el-upload>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile" :loading="uploadLoading">
            上传提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, markRaw, nextTick } from 'vue';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import type { UploadInstance } from 'element-plus';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import {
    AccountListGetResultModel,
    tableItem,
  } from '/@/views/upms/api/model/accountModel';
  import BasicForm from '/@/components/sys/BasicForm';
  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { CircleClose, Plus, Delete } from '@element-plus/icons-vue';
  import {
    columns,
    searchFormSchema,
    accountFormSchema,
    inviteFormSchema,
  } from './account.data';
  import { Option } from '/@/components/sys/BasicForm/types';
  import {
    saveUser,
    authRoleApi,
    // projectDownloadUrl,
    // projectUploadApi,
  } from '/@/views/upms/api/account';
  import {
    inviteAccountApi,
    projectAccountDelApi,
    uploadRoleApi,
  } from '/@/views/upms/api/project';
  import { deptAsyn } from '/@/views/upms/api/dept';
  import { GetqryProjectUser } from '/@/views/upms/api/member';
  import userList from './component/user.vue';
  import postList from './component/post.vue';
  import roleList from './component/role.vue';
  import accList from './component/account.vue';
  import orgList from './component/org.vue';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { cloneDeep } from 'lodash-es';
  import useUser from './hooks/useUser';
  import usePost from './hooks/usePost';
  import useAccount from './hooks/useAccount';
  import { useTenantStore } from '/@/stores/modules/tenant';
  import { getWebModel, getAuthStorage, setAuthStorage } from '/@/utils/storage/auth';
  import BatchConfirmation from '/@/views/upms/components/BatchConfirmation.vue';
  import userBatchConfirmation from '/@/views/upms/common/hooks/userBatchConfirmation';
  import { userPermission } from '/@/views/upms/api/system';
  // import useUpload from '/@/hooks/upms/upload/useUpload';
  const upload = ref<UploadInstance>();
  // const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const tree = ref<InstanceType<typeof VTreeSearch> | null>(null);
  const userInfo = getAuthStorage();
  const useTenant = useTenantStore();
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  const deptId = ref('');
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const searchRef = ref({});
  const sub = ref(true);
  const tableData = ref([]);
  const formData = ref<tableItem>({
    id: '',
    employeeId: '',
    available: '',
    userPasswd: '',
    roleIdList: [],
    userName: '',
    postStationIdList: [],
    deptName: '',
    deptId: [],
  });
  const drawer = ref(false);
  const isEdit = ref(true);
  const formAccount = ref();
  // const deptCode = ref('');
  const title = ref('');
  const selectionIds = ref<string[]>([]);
  const userId = ref('');
  const userOptions = ref<Option[]>([]);
  const postOptions = ref<Option[]>([]);
  const roleOptions = ref<Option[]>([]);
  const postSelect = ref<HTMLDivElement | null>(null);
  const postListRef = ref<HTMLDivElement | null>(null);
  const roleSelect = ref<HTMLDivElement | null>(null);
  const roleListRef = ref<HTMLDivElement | null>(null);
  const userSelect = ref<HTMLDivElement | null>(null);
  const userListRef = ref<HTMLDivElement | null>(null);
  const visibleRole = ref(false);
  const selectPostData = ref<any>({});
  const selectRoleData = ref<any>([]);
  const listItem = ref<any>({});

  const initPass = ref('');
  const roleType = ref('1');

  const accountSelect = ref<HTMLDivElement | null>(null);
  const accountListRef = ref<HTMLDivElement | null>(null);
  const drawerAccount = ref(false);
  const accountOptions = ref<Option[]>([]);
  const inviteFormData = ref<any>({
    userId: [],
    projectId: useTenant.getCurProject.id,
    roleIds: [],
  });
  const selectRoleInviteData = ref<any>([]);
  const formInvite = ref();

  const drawerAuth = ref(false);
  const authsFormRef = ref<FormInstance>();
  const authTitle = ref('机构角色关联');
  const orgSelect = ref<HTMLDivElement | null>(null);
  const orgRef = ref<HTMLDivElement | null>(null);
  const formroleAuths = ref<any>({
    roleAuths: [],
  });
  const visibleOrg = ref(false);
  const orgAndRolesIndex = ref(0);
  const roleChargeSelect = ref<HTMLDivElement | null>(null);
  const authDisabled = ref(false);
  const uploadLoading = ref(false);
  const switchArr = ref([]);
  const {
    handleChangeUser,
    handleFocusUserEvent,
    visibleUser,
    selectUserData,
    currentRowSelect,
    saveUserInfo,
  } = useUser(formData, userOptions, userSelect, userListRef);

  const {
    handleFocusPostEvent,
    handleChangePost,
    visiblePost,
    currentRowPostSelect,
    savePost,
  } = usePost(formData, postOptions, postSelect, postListRef);

  const {
    handleFocusAccountEvent,
    handleChangeAccount,
    removeTagAccount,
    selectAccounts,
    saveAccount,
    visibleAccount,
  } = useAccount(inviteFormData, accountOptions, accountSelect, accountListRef);
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      // currentIsLeaf.value = e.isLeaf;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  const handleCheckBox = (data) => {
    sub.value = data;
    accountList();
  };
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    accountList();
  };
  const accountList = () => {
    const { pageNum, pageSize } = page.value;
    const { available, employeeName, userName } =
      searchRef?.value?.['searchValue'] || {};
    GetqryProjectUser({
      opType: 'project',
      pageNum,
      pageSize,
      // instCode: deptCode.value,
      deptId: deptId.value,
      available,
      userName,
      employeeName,
      sub: sub.value ? '1' : '0',
      ...sortData.value,
      selfDeptId: userInfo.sysEmployee.deptId,
    }).then((res: AccountListGetResultModel) => {
      const { list, total, currentPage, pageSize } = res;
      tableData.value = list;
      page.value = {
        total,
        pageNum: currentPage,
        pageSize,
      };
    });
  };
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    accountList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    accountList();
  };
  // const downLoad = (isTemplate) => {
  //   const { groupId, groupName, groupCode, tenantId, projectId, status, channelId } =
  //     searchRef?.value?.['searchValue'] || {};
  //   const params = {
  //     groupId,
  //     groupName,
  //     groupCode,
  //     tenantId,
  //     projectId,
  //     status,
  //     channelId,
  //     ...sortData.value,
  //     isTemplate,
  //   };
  //   projectDownloadUrl(params).then((res) => {
  //     const blob = new Blob([res.data], {
  //       type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
  //     });
  //     const fileName = '账号管理' + '.xlsx';
  //     const elink = document.createElement('a'); // 创建a标签
  //     elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
  //     elink.style.display = 'none';
  //     elink.href = URL.createObjectURL(blob);
  //     document.body.appendChild(elink);
  //     elink.click(); // 点击下载
  //     console.log(elink.href);
  //     URL.revokeObjectURL(elink.href); // 释放URL 对象
  //     document.body.removeChild(elink); // 释放标签
  //   });
  // };

  // const uploadFile = () => {
  //   // const { tenantId, projectId } = searchRef?.value?.['searchValue'] || {};
  //   uploadLoading.value = true;
  //   let formData = new FormData();
  //   formData.append('file', fileData.value?.raw);
  //   formData.append('tenantId', useTenant.getCurTenant.id);
  //   formData.append('projectId', useTenant.getCurProject.id);
  //   projectUploadApi(formData)
  //     .then(() => {
  //       ElMessage({
  //         type: 'success',
  //         message: '导入成功',
  //       });
  //       fileVisible.value = false;
  //       accountList();
  //       uploadLoading.value = false;
  //     })
  //     .catch(() => {
  //       uploadLoading.value = false;
  //     });
  // };
  const getuserPermission = () => {
    userPermission().then((res) => {
      userInfo.permissionMap = res;
      setAuthStorage(userInfo);
    });
  };
  const confirmClick = () => {
    const getData = formAccount.value.submitForm;
    const ruleFormRef = formAccount.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        data.sysUserRoleVoList = [];
        if (data.userPasswd) {
          initPass.value = cloneDeep(data.userPasswd);
          data.userPasswd = encryptedSM4(data.userPasswd);
        } else {
          delete data.userPasswd;
        }
        data.version = formData.value.version;
        if (Array.isArray(data.postStationIdList)) {
          data.postStationIdList = data.postStationIdList;
        } else if (!data.postStationIdList) {
          data.postStationIdList = [];
        } else {
          data.postStationIdList = [data.postStationIdList];
        }
        data.opType = 'project';
        data.sysUserRoleVoList[0] = {
          deptType: '0',
          deptId: data.deptId[0],
          roleIds: data.roleIdList.join(','),
        };
        saveUser(data)
          .then(() => {
            drawer.value = false;
            ElMessage({
              type: 'success',
              showClose: true,
              message: formData.value.id ? '修改账号成功' : '新增账号成功',
            });
            accountList();
            // 如果编辑的是本账号，需要更新接口权限码
            if (formData.value.id && formData.value.id == userInfo.id) {
              getuserPermission();
            }
          })
          .catch(() => {
            formData.value.userPasswd = initPass.value;
            if (Array.isArray(data.postStationIdList)) {
              formData.value.postStationIdList = data.postStationIdList[0];
            } else if (!data.postStationIdList) {
              formData.value.postStationIdList = '';
            } else {
              formData.value.postStationIdList = data.postStationIdList;
            }
            formData.value.postStationIdList = data.postStationIdList;
          });
      }
    });
  };
  const handleEditOrDetail = (item, type) => {
    drawer.value = true;
    if (type === 'edit') {
      title.value = '编辑账号';
      isEdit.value = true;
    } else {
      title.value = '查看账号详情';
      isEdit.value = false;
    }

    item.userPasswd = undefined;
    userId.value = item.id;
    listItem.value = cloneDeep(item);
    formData.value = {
      id: listItem.value.id,
      employeeId: listItem.value.employeeId,
      available: listItem.value.available,
      roleIdList: listItem.value.roleIdList,
      userName: listItem.value.userName,
      postStationIdList:
        listItem.value.postStationIdList && listItem.value.postStationIdList.length > 0
          ? listItem.value.postStationIdList[0]
          : '',
      userPasswd: undefined,
      deptId: listItem.value.deptId,
      deptName: listItem.value.deptName,
      version: listItem.value.version,
    };
    userOptions.value = [];
    const obj: Option = {
      label:
        listItem.value.sysEmployee.empName + '/' + listItem.value.sysEmployee.empCode,
      value: listItem.value.sysEmployee.id,
    };
    selectUserData.value = listItem.value.sysEmployee;
    userOptions.value.push(obj);

    postOptions.value = [];
    const postObj: Option = {
      label: listItem.value.postStationNames,
      value:
        listItem.value.postStationIdList && listItem.value.postStationIdList.length > 0
          ? listItem.value.postStationIdList[0]
          : '',
    };
    selectPostData.value = {
      id:
        listItem.value.postStationIdList && listItem.value.postStationIdList.length > 0
          ? listItem.value.postStationIdList[0]
          : '',
      postStationName: listItem.value.postStationNames,
    };
    postOptions.value.push(postObj);

    roleOptions.value = [];
    const roleNames = listItem.value.roleNames.split(',');
    selectRoleData.value = [];
    for (let i = 0; i < listItem.value.roleIdList.length; i++) {
      let roleObj = {
        label: roleNames[i],
        value: listItem.value.roleIdList[i],
      };
      roleOptions.value.push(roleObj);
      let selectRoleObj = {
        name: roleNames[i],
        id: listItem.value.roleIdList[i],
        label: roleNames[i],
      };
      selectRoleData.value.push(selectRoleObj);
    }
  };

  const handleCurrentChange = (val) => {
    selectionIds.value = [];
    for (let i = 0; i < val.length; i++) {
      selectionIds.value.push(val[i].id);
    }
  };
  const handleDelete = (item, type) => {
    // type 1 批量移除  0 单独移除
    let title = '';
    if (type === 1) {
      title = `确认移除当前所选中${selectionIds.value.length}条账号数据？`;
    } else {
      title = '确认移除该账号？';
    }
    ElMessageBox.confirm(title, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        let params = {};
        if (item === null) {
          params = selectionIds.value;
        } else {
          params = [item.id];
        }
        projectAccountDelApi({ userIds: params }).then(() => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '账号移除成功',
          });
          if (type === 1) {
            selectionIds.value = [];
            tableRef.value!.clearSelection();
          }
          accountList();
        });
      })
      .catch(() => {});
  };

  const callDelete = (params) => {
    projectAccountDelApi({ userIds: params }).then(() => {
      ElMessage({
        showClose: true,
        type: 'success',
        message: '账号移除成功',
      });
      selectionIds.value = [];
      tableRef.value!.clearSelection();
      accountList();
    });
  };
  const {
    batchConfirmationVisible,
    batchDeletion,
    batchConfirmationSave,
    getPassword,
    batchConfirmationLoading,
  } = userBatchConfirmation(selectionIds, callDelete);

  const handleFocusRoleEvent = (e) => {
    visibleRole.value = true;
    roleType.value = '1';
    nextTick(() => {
      // roleSelect.value && roleSelect.value?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      if (selectRoleData.value.length > 0) {
        if (formData.value.roleIdList && formData.value.roleIdList.length > 0) {
          roleListRef.value &&
            roleListRef.value?.handleSetSelectData(selectRoleData.value);
        } else {
          roleListRef.value && roleListRef.value?.handleSetSelectData(null);
        }
      }
    });
  };
  const handleChangeRole = (data) => {
    roleType.value = '1';
    if (data.length > 0) {
      roleListRef.value && roleListRef.value?.handleSetSelectData(selectRoleData.value);
    }
  };
  const handleFocusRoleInviteEvent = (e) => {
    visibleRole.value = true;
    roleType.value = '2';
    nextTick(() => {
      // roleSelect.value && roleSelect.value?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      if (selectRoleInviteData.value.length > 0) {
        if (inviteFormData.value.roleIds && inviteFormData.value.roleIds.length > 0) {
          roleListRef.value &&
            roleListRef.value?.handleSetSelectData(selectRoleInviteData.value);
        } else {
          roleListRef.value && roleListRef.value?.handleSetSelectData(null);
          selectRoleInviteData.value = [];
        }
      }
    });
  };
  const handleChangeInviteRole = (data) => {
    roleType.value = '2';
    if (data.length > 0) {
      roleListRef.value &&
        roleListRef.value?.handleSetSelectData(selectRoleInviteData.value);
    }
  };
  const selectRoles = (data) => {
    if (roleType.value === '1') {
      selectRoleData.value = data;
    } else if (roleType.value === '2') {
      selectRoleInviteData.value = data;
    } else {
      formroleAuths.value.roleAuths[orgAndRolesIndex.value].selectChargeRoleData = data;
    }
  };
  const removeTag = (val) => {
    if (roleType.value === '1') {
      selectRoleData.value = removeById(selectRoleData.value, val);
    } else {
      selectRoleInviteData.value = removeById(selectRoleInviteData.value, val);
    }
  };
  const removeById = (arr, id) => {
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].id === id) {
        arr.splice(i, 1);
        break;
      }
    }
    return arr;
  };

  const saveRole = () => {
    if (roleType.value === '1') {
      roleOptions.value = [];
      formData.value.roleIdList = [];
      for (let i = 0; i < selectRoleData.value.length; i++) {
        const obj = {
          label: selectRoleData.value[i].name,
          value: selectRoleData.value[i].id,
        };
        formData.value.roleIdList.push(selectRoleData.value[i].id);
        roleOptions.value.push(obj);
      }
      visibleRole.value = false;
    } else if (roleType.value === '2') {
      inviteFormData.value.roleIds = [];
      //
      console.log(selectRoleInviteData.value);
      for (let i = 0; i < selectRoleInviteData.value.length; i++) {
        const obj = {
          label: selectRoleInviteData.value[i].name,
          value: selectRoleInviteData.value[i].id,
        };

        inviteFormData.value.roleIds.push(selectRoleInviteData.value[i].id);
        roleOptions.value.push(obj);
      }
      visibleRole.value = false;
    } else {
      formroleAuths.value.roleAuths[orgAndRolesIndex.value].roleOptions = [];
      formroleAuths.value.roleAuths[orgAndRolesIndex.value].roleIdsList = [];
      const selectChargeRoleData =
        formroleAuths.value.roleAuths[orgAndRolesIndex.value].selectChargeRoleData;
      for (let i = 0; i < selectChargeRoleData.length; i++) {
        const obj = {
          label: selectChargeRoleData[i].name,
          value: selectChargeRoleData[i].id,
        };
        formroleAuths.value.roleAuths[orgAndRolesIndex.value].roleIdsList.push(
          selectChargeRoleData[i].id,
        );
        formroleAuths.value.roleAuths[orgAndRolesIndex.value].roleOptions.push(obj);
      }
      visibleRole.value = false;
    }
  };

  const inviteConfirmClick = () => {
    const getData = formInvite.value.submitForm;
    const ruleFormRef = formInvite.value.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        console.log(data);
        inviteAccountApi(data).then(() => {
          drawerAccount.value = false;
          ElMessage({
            type: 'success',
            showClose: true,
            message: '邀请账号成功',
          });
          accountList();
        });
      }
    });
  };

  const handleInviteUser = () => {
    if (!!userInfo.projectId) {
      drawerAccount.value = true;
      inviteFormData.value = {
        userId: [],
        projectId: useTenant.getCurProject.id,
        roleIds: [],
      };
    } else {
      ElMessage({
        showClose: true,
        type: 'warning',
        message: '请先选择项目',
      });
    }
  };

  const handleFocusOrgEvent = (e, index) => {
    visibleOrg.value = true;
    orgAndRolesIndex.value = index;
    nextTick(() => {
      console.log(formroleAuths.value.roleAuths[index]);
      // orgSelect.value && orgSelect.value[index]?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      if (formroleAuths.value.roleAuths[index].deptId) {
        orgRef.value &&
          orgRef.value?.currentNode(formroleAuths.value.roleAuths[index].deptId);
      }
    });
  };
  const handleChangeOrg = (data) => {
    console.log(data);
  };
  const handleAuth = (data, disabled) => {
    authDisabled.value = disabled;
    drawerAuth.value = true;
    console.log(data);
    listItem.value = cloneDeep(data);
    authRoleApi({
      userId: data.id,
      opType: 'project',
      projectId: useTenant.getCurProject.id,
    }).then((res) => {
      console.log(res);
      formroleAuths.value.roleAuths = res;
      switchArr.value = [];
      for (let i = 0; i < formroleAuths.value.roleAuths.length; i++) {
        const { status, sortDefault } = formroleAuths.value.roleAuths[i];
        formroleAuths.value.roleAuths[i].orgOptions = [
          {
            label: formroleAuths.value.roleAuths[i].instName || '机构1',
            value: formroleAuths.value.roleAuths[i].deptId,
          },
        ];
        switchArr.value.push({
          sortDefault: sortDefault && sortDefault === '1' ? true : false,
          status: status && status === '2' ? true : false,
        });
        formroleAuths.value.roleAuths[i].roleOptions = [];
        formroleAuths.value.roleAuths[i].selectChargeRoleData = [];
        formroleAuths.value.roleAuths[i].roleIdsList =
          formroleAuths.value.roleAuths[i].roleIds.split(',');

        formroleAuths.value.roleAuths[i].roleNamesList =
          formroleAuths.value.roleAuths[i].roleNames.split(',');

        const roleIdsList = formroleAuths.value.roleAuths[i].roleIdsList;
        const roleNamesList = formroleAuths.value.roleAuths[i].roleNamesList;
        for (let j = 0; j < roleIdsList.length; j++) {
          let obj = {
            label: roleNamesList[j],
            value: roleIdsList[j],
          };
          formroleAuths.value.roleAuths[i].selectChargeRoleData.push({
            id: roleIdsList[j],
          });
          formroleAuths.value.roleAuths[i].roleOptions.push(obj);
        }
      }
    });
  };

  const currentOrgSelect = (data) => {
    formroleAuths.value.roleAuths[orgAndRolesIndex.value].orgItem = data;
  };
  const saveOrg = () => {
    visibleOrg.value = false;
    formroleAuths.value.roleAuths[orgAndRolesIndex.value].orgOptions = [
      {
        label: formroleAuths.value.roleAuths[orgAndRolesIndex.value].orgItem
          ? formroleAuths.value.roleAuths[orgAndRolesIndex.value].orgItem.instName
          : '',
        value: formroleAuths.value.roleAuths[orgAndRolesIndex.value].orgItem
          ? formroleAuths.value.roleAuths[orgAndRolesIndex.value].orgItem.id
          : '',
      },
    ];
    formroleAuths.value.roleAuths[orgAndRolesIndex.value].deptId = formroleAuths.value
      .roleAuths[orgAndRolesIndex.value].orgItem
      ? formroleAuths.value.roleAuths[orgAndRolesIndex.value].orgItem.id
      : '';
  };
  const addRoleAuths = () => {
    let obj = {
      deptId: '',
      roleIds: '',
      roleIdsList: [],
      deptType: '1',
      orgOptions: [],
      roleOptions: [],
      selectChargeRoleData: [],
    };
    formroleAuths.value.roleAuths.push(obj);
    switchArr.value.push({
      sortDefault: false,
      status: false,
    });
  };
  const removeRoleAuths = (index) => {
    formroleAuths.value.roleAuths.splice(index, 1);
    switchArr.value.splice(index, 1);
  };
  const changeSwitch = (e, index, field) => {
    if (field == 'sortDefault' && e) {
      for (let i = 0; i < switchArr.value.length; i++) {
        if (i === index) {
          switchArr.value[index][field] = true;
        } else {
          switchArr.value[i][field] = false;
        }
      }
    } else {
      switchArr.value[index][field] = e;
    }
  };
  const saveRoleAuths = async (formEl: FormInstance | undefined) => {
    if (!formEl) return;
    await formEl.validate((valid) => {
      if (valid) {
        let auths: any = [];
        let deptArr: string[] = [];
        for (let i = 0; i < formroleAuths.value.roleAuths.length; i++) {
          const { status, sortDefault } = switchArr.value[i];
          // if (formroleAuths.value.roleAuths[i].deptType === '1') {
          const obj = {
            deptId: formroleAuths.value.roleAuths[i].deptId,
            deptType: formroleAuths.value.roleAuths[i].deptType,
            roleIds: formroleAuths.value.roleAuths[i].roleIdsList.join(','),
            sortDefault: sortDefault ? '1' : null,
            status: status ? '2' : null,
          };
          deptArr.push(formroleAuths.value.roleAuths[i].deptId);
          auths.push(obj);
          // }
        }
        const params = {
          id: listItem.value.id,
          sysUserRoleVoList: auths,
          deptId: deptArr,
          opType: 'project',
          projectId: useTenant.getCurProject.id,
          tenantId: useTenant.getCurTenant.id,
          deptAuth: true,
        };
        uploadRoleApi(params).then(() => {
          drawerAuth.value = false;
          ElMessage({
            type: 'success',
            showClose: true,
            message: '授权成功',
          });
          accountList();
          getuserPermission();
        });
      }
    });
  };
  const handleFocusRoleChargeEvent = (e, index) => {
    visibleRole.value = true;
    orgAndRolesIndex.value = index;
    roleType.value = '3';
    nextTick(() => {
      // roleChargeSelect.value && roleChargeSelect.value[index]?.blur();
      // 解决设置完之后会再次执行focus事件，导致保存后弹窗未关闭问题
      e.target.blur();
      const selectChargeRoleData =
        formroleAuths.value.roleAuths[index].selectChargeRoleData;
      if (selectChargeRoleData.length > 0) {
        if (formroleAuths.value.roleAuths[index].roleIdsList) {
          roleListRef.value &&
            roleListRef.value?.handleSetSelectData(selectChargeRoleData);
        } else {
          roleListRef.value && roleListRef.value[index]?.handleSetSelectData(null);
        }
      }
    });
  };
  const handleChangeRoleCharge = (data, index) => {
    roleType.value = '3';
    if (data && data.length > 0) {
      roleListRef.value &&
        roleListRef.value.handleSetSelectData(
          formroleAuths.value.roleAuths[index].selectChargeRoleData,
        );
    }
  };

  const removeTagCharge = (val, index) => {
    formroleAuths.value.roleAuths[index].selectChargeRoleData = removeByIds(
      formroleAuths.value.roleAuths[index].selectChargeRoleData,
      val,
    );
  };
  const removeByIds = (arr, ids) => {
    return arr.filter(function (val) {
      return ids.indexOf(val.id) > -1;
    });
  };
  watch(
    () => nodeId.value,
    (val) => {
      if (!!val) {
        deptId.value = val;
        accountList();
      } else {
        deptId.value = '';
        accountList();
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;

    .mid {
      height: 100%;
      box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }
  /*左侧div样式*/
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: calc(100vh - 30px);
      .active {
        background-color: #c9e9f7;
      }
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 12px 20px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    .search-div {
      border-radius: 6px;
      background: #fff;
      // padding: 10px 16px;
      .title {
        // border-bottom: 1px solid #e4e7ed;
        // line-height: 68px;

        font-size: 18px;
        padding: 20px 0 6px 20px;
        // background: #ffffff;
        font-weight: 700;
        color: #333333;
      }
    }
    .table-top-div {
      display: flex;
      justify-content: space-between;
      // line-height: 64px;d
      margin-bottom: 20px;
      p {
        font-size: 18px;
        color: #333333;
        font-weight: 700;
        margin: 0;
        padding: 0;
        line-height: 32px;
      }
    }
    .table-content {
      margin-top: 20px;
      background: #ffffff;
      height: calc(100% - 180px);
      padding: 20px 20px 0 20px;
      position: relative;
      overflow: auto;
      .top-button {
        // position: absolute;
        // top: 10px;
        // left: 16px;
        z-index: 9;
        .upload-btn {
          display: inline-block;
          margin: 0 12px;
        }
      }
    }
  }

  .table-content {
    background: #ffffff;
    margin-top: 20px;
  }
  h4 {
    font-size: 18px;
    margin-bottom: 0;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }

  .item-div {
    border-bottom: 1px solid #cccccc;
    .item-btn {
      margin-bottom: 12px;
      display: flex;
      justify-content: flex-end;
    }
    .item-form {
      margin-top: 12px;
    }
  }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }
</style>
