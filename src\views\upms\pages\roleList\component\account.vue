<template>
  <div class="content-model">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane class="pane-left" size="40" min-size="30">
        <div class="left">
          <div class="tree-content">
            <div class="tree-div">
              <VTreeSearch
                ref="tree"
                selectable
                :load="getDeptData"
                @click="handleTreeClick"
              >
                <template #actions>
                  <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
                </template>
                <template #search-input>
                  <el-input
                    class="search-input"
                    placeholder="至少3个字才能触发自动搜索"
                    v-model="searchName"
                    @input="handleSearch(0)"
                  />
                </template>
                <template #node="{ node }">
                  <span
                    :class="node.id == nodeId ? 'active' : ''"
                    :style="{
                      color:
                        name && node.instName.indexOf(name) > -1
                          ? 'rgb(166, 0, 0)'
                          : '',
                      fontWeight:
                        name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                    }"
                    >{{ node.instName }}</span
                  >
                </template>
              </VTreeSearch>
            </div>
          </div></div
        >
      </Pane>
      <Pane class="mid" size="80">
        <!--右侧div内容-->
        <div class="right">
          <!-- <p class="title"> 账号列表 </p> -->
          <el-tabs
            v-model="activeName"
            type="card"
            class="demo-tabs"
            @tab-click="handleClick"
          >
            <el-tab-pane label="已授权" name="1" />
            <el-tab-pane label="全部" name="0" />
          </el-tabs>
          <el-button
            type="primary"
            class="auth-btn"
            v-if="activeName == '1'"
            @click="delAccount(multipleSelection.map((item) => item?.id))"
            >取消授权</el-button
          >
          <el-button
            type="primary"
            class="auth-btn"
            v-else
            @click="
              setRoleAuthorization(
                multipleSelection.map((item) => {
                  return { userId: item?.userId, deptId: item?.deptId };
                }),
              )
            "
            >授权</el-button
          >
          <basic-search
            :searchArray="searchFormSchema"
            class="search-all"
            ref="searchRef"
            :labelWidth="80"
            :labelShow="false"
            :btnShow="false"
            @onSearch="accountList"
          />
          <div class="table-content">
            <basic-table
              ref="tableRef"
              :columns="columns"
              :data="tableData"
              :total="page.total"
              :page-size="page.pageSize"
              :current-page="page.pageNum"
              @page-change="pageChange"
              @size-change="sizeChange"
              @selection-change="handleSelectionChange"
            >
              <template #action="{ record }">
                <el-button
                  v-if="activeName == '1'"
                  type="primary"
                  link
                  @click="delAccount([record.id])"
                  >取消授权</el-button
                >
                <el-button
                  v-else
                  type="primary"
                  link
                  :disabled="record.mark == '1'"
                  @click="
                    setRoleAuthorization([
                      { userId: record?.userId, deptId: record?.deptId },
                    ])
                  "
                  >授权</el-button
                >
              </template>
            </basic-table>
          </div>
        </div>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, PropType, nextTick } from 'vue';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  import { columns, searchFormSchema } from './account.data';
  import {
    getAuthorizedUser,
    deleteAuthRole,
    roleAuthorization,
  } from '/@/views/upms/api/roleSystem';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { deptAsyn } from '/@/views/upms/api/dept';
  const props = defineProps({
    roleId: {
      type: String as PropType<string>,
      default: '',
    },
  });
  const tree = ref<InstanceType<typeof VTreeSearch> | null>(null);
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const activeName = ref('1');
  const multipleSelection = ref([]);
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  const deptId = ref('');
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const searchRef = ref({});
  const tableData = ref([]);
  const selectDatas = ref<any>([]);
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }

  const accountList = () => {
    const { pageNum, pageSize } = page.value;
    const { userName } = searchRef?.value?.['searchValue'] || {};
    getAuthorizedUser({
      auth: activeName.value == '1',
      roleId: props.roleId,
      pageNum,
      pageSize,
      deptIds: deptId.value ? [deptId.value] : [],
      userName,
    }).then((res) => {
      const { list, total, currentPage, pageSize } = res;
      tableData.value = list;
      page.value = {
        total,
        pageNum: currentPage,
        pageSize,
      };
    });
  };
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    accountList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    accountList();
  };

  const handleClick = () => {
    // 清空列表复选
    tableRef.value?.clearSelection();
    page.value = {
      total: 0,
      pageSize: 10,
      pageNum: 1,
    };
    tableData.value = [];
    selectDatas.value = [];
    nextTick(() => {
      accountList();
    });
  };

  const delAccount = (ids) => {
    if (!ids.length) {
      ElMessage({
        type: 'warning',
        message: '请至少选择一条数据',
      });
      return;
    }
    ElMessageBox.confirm('确认取消授权？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        deleteAuthRole({ ids }).then(() => {
          ElMessage({
            type: 'success',
            message: '取消授权成功',
          });
          tableRef.value!.clearSelection();
          multipleSelection.value = [];
          accountList();
        });
      })
      .catch(() => {});
  };
  function setRoleAuthorization(usersList) {
    if (!usersList.length) {
      ElMessage({
        type: 'warning',
        message: '请至少选择一条数据',
      });
      return;
    }
    ElMessageBox.confirm('确认授权？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        roleAuthorization({ users: usersList, roleId: props.roleId }).then(() => {
          ElMessage({
            // eslint-disable-next-line prettier/prettier
            type: 'success',
            message: '授权成功',
          });
          multipleSelection.value = [];
          tableRef.value!.clearSelection();
          accountList();
        });
      })
      .catch(() => {});
  }
  const handleSelectionChange = (val) => {
    console.log('val', val);
    multipleSelection.value = val;
  };
  watch(
    () => nodeId.value,
    (val) => {
      if (!!val) {
        deptId.value = val;
        accountList();
      } else {
        deptId.value = '';
        accountList();
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped lang="scss">
  .content-model {
    display: flex;
    height: 500px;
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    // height: calc(100% - 24px);
    // margin: 12px 12px;
    height: 100%;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;

    .mid {
      height: 100%;
      box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }
  /*左侧div样式*/
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: calc(100vh - 30px);
      .active {
        background-color: #c9e9f7;
      }
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    border-radius: 6px;
    position: relative;
    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 68px;
      font-size: 18px;
      padding-left: 18px;
      background: #ffffff;
      font-weight: 700;
    }
    .auth-btn {
      position: absolute;
      top: 3px;
      right: 20px;
    }
    .search-all {
      background: #ffffff;
    }
    .table-content {
      margin-top: 20px;
      background: #ffffff;
      height: calc(100% - 146px);
      padding: 10px 16px 0;
      overflow: auto;
    }
  }

  .table-content {
    background: #ffffff;
    margin-top: 20px;
  }
  .select-div {
    line-height: 40px;
    font-size: 16px;

    span {
      color: #409eff;
    }
  }
  .demo-tabs {
    background: #ffffff;
    // padding-top: 30px;
  }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }
</style>
