<template>
  <div class="content-model">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane class="pane-left" size="20" min-size="20">
        <div class="left">
          <div class="tree-content">
            <div class="tree-div">
              <VTreeSearch
                ref="tree"
                selectable
                :load="getDeptData"
                @click="handleTreeClick"
              >
                <template #actions>
                  <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
                </template>
                <template #search-input>
                  <el-input
                    class="search-input"
                    placeholder="至少3个字才能触发自动搜索"
                    v-model="searchName"
                    @input="handleSearch(0)"
                  />
                </template>
                <template #node="{ node }">
                  <span
                    :class="node.id == nodeId ? 'active' : ''"
                    :style="{
                      color:
                        name && node.instName.indexOf(name) > -1
                          ? 'rgb(166, 0, 0)'
                          : '',
                      fontWeight:
                        name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                    }"
                    >{{ node.instName }}</span
                  >
                </template>
              </VTreeSearch>
            </div>
          </div></div
        >
      </Pane>
      <Pane class="mid" size="80">
        <!--右侧div内容-->
        <div class="right">
          <!-- <p class="title"> 账号列表 </p> -->
          <basic-search
            :searchArray="searchFormSchema"
            class="search-all"
            ref="searchRef"
            :labelWidth="80"
            :labelShow="false"
            :btnShow="false"
            @onSearch="accountList"
          >
            <template #sub>
              <el-checkbox
                v-model="sub"
                label="查看本机构及下属机构账号"
                @change="handleCheckBox"
              />
            </template>
          </basic-search>
          <div class="table-content">
            <basic-table
              ref="tableRef"
              :columns="columns"
              :data="tableData"
              :total="page.total"
              :page-size="page.pageSize"
              :current-page="page.pageNum"
              @page-change="pageChange"
              @size-change="sizeChange"
              @sortChange="sortChange"
              highlight-current-row
              @selectionChange="handleChangeCheck"
            />
          </div>
        </div>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { AccountListGetResultModel } from '/@/views/upms/api/model/accountModel';

  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  import { columns, searchFormSchema } from './account.data';
  import { getAccountList } from '/@/views/upms/api/account';
  import { getAuthStorage } from '/@/utils/storage/auth';
  import { deptAsyn } from '/@/views/upms/api/dept';
  const tree = ref<InstanceType<typeof VTreeSearch> | null>(null);
  const userInfo = getAuthStorage();
  let emits = defineEmits(['selectAccounts']);
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      // currentIsLeaf.value = e.isLeaf;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  const deptId = ref('');
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const searchRef = ref({});
  const sub = ref(true);
  const tableData = ref([]);
  const selectDatas = ref<any>([]);
  const handleCheckBox = (data) => {
    console.log(data);
    sub.value = data;
    accountList();
  };
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    accountList();
  };
  const accountList = () => {
    const { pageNum, pageSize } = page.value;
    const { employeeName, userName } = searchRef?.value?.['searchValue'] || {};
    getAccountList({
      pageNum,
      pageSize,
      deptId: deptId.value,
      available: '1',
      userName,
      employeeName,
      sub: sub.value ? '1' : '0',
      ...sortData.value,
      selfDeptId: userInfo.sysEmployee.deptId,
    }).then((res: AccountListGetResultModel) => {
      const { list, total, currentPage, pageSize } = res;
      tableData.value = list;
      tableData.value = tableData.value.filter((item: any) => item.id !== '1');
      page.value = {
        total,
        pageNum: currentPage,
        pageSize,
      };
    });
  };
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    accountList();
  };

  const handleChangeCheck = (val) => {
    selectDatas.value = val;
    emits('selectAccounts', selectDatas.value);
  };
  const handleSetSelectData = (row) => {
    tableRef.value.setSelectData(row);
  };
  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    accountList();
  };
  watch(
    () => nodeId.value,
    (val) => {
      if (!!val) {
        deptId.value = val;
        accountList();
      } else {
        deptId.value = '';
        accountList();
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );

  defineExpose({
    handleSetSelectData,
  });
</script>

<style scoped lang="scss">
  .content-model {
    display: flex;
    height: 500px;

    background: #f0f0f0;
  }
  .box {
    width: 100%;
    // height: calc(100% - 24px);
    // margin: 12px 12px;
    height: 100%;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;

    .mid {
      height: 100%;

      box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }

  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: 100%;
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    border-radius: 6px;

    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 68px;
      font-size: 18px;
      padding-left: 18px;
      background: #ffffff;
      font-weight: 700;
    }
    .search-all {
      background: #ffffff;
      padding-top: 8px;
    }
    .table-content {
      margin-top: 10px;
      background: #ffffff;
      height: calc(100% - 90px);
      padding: 10px 16px 0;
      overflow: auto;
    }
  }

  .table-content {
    background: #ffffff;
    margin-top: 20px;
  }
  .select-div {
    line-height: 40px;
    font-size: 16px;

    span {
      color: #409eff;
    }
  }
</style>

<style>
  .conetnt .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }
</style>
