<template>
  <div class="content-model">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane class="pane-left" size="20" min-size="20">
        <div class="left">
          <div class="tree-content">
            <div class="tree-input">
              <el-input placeholder="请输入机构名称" clearable v-model="value" />
            </div>
            <div class="tree-div">
              <VTreeSearch
                ref="tree"
                selectable
                :load="getDeptData"
                @click="handleTreeClick"
              >
                <template #actions>
                  <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
                </template>
                <template #search-input>
                  <el-input
                    class="search-input"
                    placeholder="至少3个字符才能触发自动搜索"
                    v-model="searchName"
                    @input="handleSearch(0)"
                  />
                </template>
                <template #node="{ node }">
                  <span
                    :class="node.id == nodeId ? 'active' : ''"
                    :style="{
                      color:
                        name && node.instName.indexOf(name) > -1
                          ? 'rgb(166, 0, 0)'
                          : '',
                      fontWeight:
                        name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                    }"
                    >{{ node.instName }}</span
                  >
                </template>
              </VTreeSearch>
            </div>
          </div></div
        >
      </Pane>
      <Pane class="mid" size="80">
        <!--右侧div内容-->
        <div class="right">
          <p class="title"> 员工列表 </p>
          <basic-search
            :searchArray="searchFormSchema"
            class="search-all"
            ref="searchRef"
            :labelWidth="80"
            :labelShow="false"
            :btnShow="false"
            @onSearch="accountList"
          >
            <template #sub>
              <el-checkbox
                v-model="sub"
                label="查看本机构及下属机构员工"
                @change="handleCheckBox"
              />
            </template>
          </basic-search>
          <div class="table-content">
            <basic-table
              ref="tableRef"
              :columns="columns"
              :data="tableData"
              :total="page.total"
              :page-size="page.pageSize"
              :current-page="page.pageNum"
              @page-change="pageChange"
              @size-change="sizeChange"
              highlight-current-row
              @currentRowSelect="currentRowSelect"
            />
            <div v-if="clickItem?.id" class="select-div">
              已选择员工工号为<span>{{ clickItem.empCode }}</span
              >,姓名为 <span>{{ clickItem.empName }}</span
              >的数据
            </div>
          </div>
        </div>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import {
    AccountListGetResultModel,
    tableItem,
  } from '/@/views/upms/api/model/accountModel';
  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  import { searchFormSchema, columns } from './user.data';
  import { getUserList } from '/@/views/upms/api/user';
  import { getAuthStorage } from '/@/utils/storage/auth';
  import { deptAsyn } from '/@/views/upms/api/dept';
  const tree = ref<InstanceType<typeof VTreeSearch> | null>(null);
  const userInfo = getAuthStorage();
  let emits = defineEmits(['currentRowSelect']);
  const tableRef = ref<InstanceType<typeof BasicTable>>(null);
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
      // currentIsLeaf.value = e.isLeaf;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  const deptId = ref('');
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const searchRef = ref({});
  const sub = ref(true);
  const tableData = ref([]);
  const deptCode = ref('');
  const clickItem = ref<tableItem | null>({});
  const handleCheckBox = (data) => {
    sub.value = data;
    accountList();
  };
  const accountList = () => {
    const { pageNum, pageSize } = page.value;
    const { empName, email, industryType, instName, empCode } =
      searchRef?.value?.['searchValue'] || {};
    getUserList({
      industryType,
      instName,
      empCode,
      status: '01',
      pageNum,
      pageSize,
      instCode: deptCode.value,
      deptId: deptId.value,
      empName,
      phone: phone ? encryptedSM4(phone) : '',
      email: email ? encryptedSM4(email) : '',
      opType: 'tenant',
      sub: sub.value ? '1' : '0',
      selfInstCode: userInfo.sysEmployee.instCode,
      filterate: 1,
    }).then((res: AccountListGetResultModel) => {
      const { list, total, currentPage, pageSize } = res;
      tableData.value = list;
      page.value = {
        total,
        pageNum: currentPage,
        pageSize,
      };
    });
  };
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    accountList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    accountList();
  };
  const currentRowSelect = (data) => {
    if (data) {
      clickItem.value = data;
    } else {
      clickItem.value = {};
    }
    emits('currentRowSelect', data);
  };
  const handleSetCurrentRow = (row) => {
    if (!row) {
      clickItem.value = null;
    } else {
      clickItem.value = row;
    }
    tableRef.value.setCurrentRow(row);
  };
  // accountList();
  watch(
    () => nodeId.value,
    (val) => {
      if (!!val) {
        deptId.value = val;
        accountList();
      } else {
        deptId.value = '';
        accountList();
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
  defineExpose({
    handleSetCurrentRow,
  });
</script>

<style scoped lang="scss">
  .content-model {
    display: flex;
    height: 500px;
    background: #f0f0f0;
  }
  .box {
    // width: 100%;
    // height: calc(100% - 24px);
    // margin: 12px 12px;
    height: 100%;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    display: flex;

    .mid {
      height: 100%;

      box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }
  /*左侧div样式*/
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: calc(100vh - 62px);
      .active {
        background-color: #c9e9f7;
      }
    }
    .tree-input {
      border-bottom: 1px solid #e4e7ed;
      .el-input {
        padding: 18px;
      }
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 88px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    border-radius: 6px;
    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 68px;
      font-size: 18px;
      padding-left: 18px;
      background: #ffffff;
      font-weight: 700;
    }
    .search-all {
      background: #ffffff;
    }
    .table-content {
      margin-top: 20px;
      background: #ffffff;
      height: calc(100% - 200px);
      padding: 10px 16px 0;
      position: relative;
      overflow: auto;
      .top-button {
        position: absolute;
        top: 10px;
        left: 16px;
        z-index: 9;
      }
    }
  }

  .table-content {
    background: #ffffff;
    margin-top: 20px;
  }
  h4 {
    font-size: 18px;
    margin-bottom: 0;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
</style>

<style lang="scss">
  .content-model .box .splitpanes__splitter {
    background: none !important;
  }

  .splitpanes--vertical > .splitpanes__splitter {
    min-width: 6px;
    background: none !important;
  }

  .dept-tabs-xl .el-tabs__content {
    height: calc(100% - 60px);
  }

  .dept-tabs-xl .el-tabs__content .el-tab-pane {
    height: 100%;
    text-align: center;
  }
  .select-div {
    line-height: 40px;
    font-size: 16px;

    span {
      color: #409eff;
    }
  }
</style>
