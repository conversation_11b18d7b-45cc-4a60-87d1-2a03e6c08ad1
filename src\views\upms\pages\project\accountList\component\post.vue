<template>
  <div class="content-model">
    <div class="box">
      <p class="title"> 岗位列表 </p>
      <basic-search
        :searchArray="searchFormSchema"
        class="search-all"
        ref="searchRef"
        :labelWidth="80"
        :labelShow="false"
        :btnShow="false"
        @onSearch="postList"
      />
      <div class="table-content">
        <basic-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :total="page.total"
          :page-size="page.pageSize"
          :current-page="page.pageNum"
          @page-change="pageChange"
          @size-change="sizeChange"
          @currentRowSelect="currentRowSelect"
          highlight-current-row
        />
        <div v-if="clickItem.id" class="select-div">
          已选择岗位编号为<span>{{ clickItem.postStationCode }}</span
          >,岗位名称为 <span>{{ clickItem.postStationName }}</span
          >的数据
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { columns, searchFormSchema } from './post.data';
  import { getPostList } from '/@/views/upms/api/post';
  import {
    PostListResultModel,
    SearchParams,
    PostListItem,
  } from '/@/views/upms/api/model/post';
  import BasicTable from '/@/components/sys/BasicTable';
  let emits = defineEmits(['currentRowSelect']);
  const tableData = ref<PostListItem[]>([]);
  const tableRef = ref<InstanceType<typeof BasicTable>>(null);
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });

  const clickItem = ref<PostListItem | null>({});
  // 岗位列表
  const postList = (data?: SearchParams) => {
    const params = {
      pageNum: page.value.pageNum,
      pageSize: page.value.pageSize,
      display: '1',
      postStationName: data?.postStationName || '',
      postStationCode: data?.postStationCode || '',
    };
    getPostList(params).then((res: PostListResultModel) => {
      tableData.value = res.list;
      page.value.total = res.total;
    });
  };
  /**
   * 切换分页，每页显示数量
   */
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    postList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    postList();
  };

  postList();
  const currentRowSelect = (data) => {
    if (data) {
      clickItem.value = data;
    } else {
      clickItem.value = {};
    }
    console.log(data);

    emits('currentRowSelect', data);
  };
  const handleSetCurrentRow = (row) => {
    if (!row) {
      clickItem.value = null;
    } else {
      clickItem.value = row;
    }
    tableRef.value.setCurrentRow(row);
  };
  defineExpose({
    handleSetCurrentRow,
  });
</script>

<style scoped lang="scss">
  .content-model {
    display: flex;
    height: 500px;
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    // height: calc(100% - 24px);
    // margin: 12px 12px;
    height: 100%;
    overflow: hidden;
    box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    .title {
      border-bottom: 1px solid #e4e7ed;
      line-height: 68px;
      font-size: 18px;
      padding-left: 24px;
      background: #ffffff;
      font-weight: 700;
    }

    .search-all {
      background: #ffffff;
      padding-left: 12px;
    }
    .table-content {
      margin-top: 20px;
      background: #ffffff;
      position: relative;
      height: calc(100% - 154px);
      padding: 10px 16px 0;
      position: relative;
      overflow: auto;
    }
  }
  .select-div {
    position: absolute;
    top: 10px;
    left: 16px;
    z-index: 9;
    line-height: 40px;
    font-size: 16px;
    span {
      color: #409eff;
    }
  }
</style>
