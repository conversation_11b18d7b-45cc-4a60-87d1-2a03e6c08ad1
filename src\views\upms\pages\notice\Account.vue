<template>
  <div class="account-content">
    <Splitpanes :rtl="false" class="default-theme box">
      <Pane class="pane-left" size="20" min-size="20">
        <div class="left">
          <div class="tree-content">
            <div class="tree-div">
              <VTreeSearch
                ref="tree"
                selectable
                :load="getDeptData"
                @click="handleTreeClick"
              >
                <template #actions>
                  <el-button type="primary" @click="handleSearch(1)">搜索</el-button>
                </template>
                <template #search-input>
                  <el-input
                    class="search-input"
                    placeholder="至少3个字才能触发自动搜索"
                    v-model="searchName"
                    @input="handleSearch(0)"
                  />
                </template>
                <template #node="{ node }">
                  <span
                    :class="node.id == nodeId ? 'active' : ''"
                    :style="{
                      color:
                        name && node.instName.indexOf(name) > -1
                          ? 'rgb(166, 0, 0)'
                          : '',
                      fontWeight:
                        name && node.instName.indexOf(name) > -1 ? 'bold' : '',
                    }"
                    >{{ node.instName }}</span
                  >
                </template>
              </VTreeSearch>
            </div>
          </div></div
        >
      </Pane>
      <Pane class="mid" size="80">
        <!--右侧div内容-->
        <div class="right">
          <basic-search
            :searchArray="accountSearchFormSchema"
            class="search-all"
            ref="searchRef"
            :labelWidth="80"
            :labelShow="false"
            @reset="accountList"
            @onSearch="accountList"
          />
          <div class="table-content">
            <basic-table
              ref="tableRef"
              :columns="accountColumns"
              :data="tableData"
              :total="page.total"
              :page-size="page.pageSize"
              :current-page="page.pageNum"
              :setting="false"
              @page-change="pageChange"
              @size-change="sizeChange"
              @selectionChange="handleCurrentChange"
              @sortChange="sortChange"
            >
              <template #available="{ record }">
                <el-tag v-if="record.available === '1'">已激活</el-tag>
                <el-tag v-else-if="record.available === '0'" type="danger"
                  >已禁用</el-tag
                >
                <el-tag v-else type="info">已锁定</el-tag>
              </template>
              <template #roleIdList="{ record }">
                <div v-if="record.roleIdList.length > 0">
                  <el-tag
                    v-for="item in record.roleNames.split(',')"
                    :key="item"
                    style="margin-right: 5px; margin-bottom: 3px"
                    >{{ item }}</el-tag
                  >
                </div>
              </template>
              <template #empName="{ record }">{{
                record.sysEmployee.empName
              }}</template>
              <template #empCode="{ record }">{{
                record.sysEmployee.empCode
              }}</template>
              <template #email="{ record }">{{ record.sysEmployee.email }}</template>
              <template #phone="{ record }">{{ record.sysEmployee.phone }}</template>
            </basic-table>
          </div>
        </div>
      </Pane>
    </Splitpanes>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { VTreeSearch } from '@wsfe/vue-tree';
  import { Splitpanes, Pane } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { AccountListGetResultModel } from '/@/views/upms/api/model/accountModel';
  import BasicTable from '/@/components/sys/BasicTable';
  import BasicSearch from '/@/components/sys/BasicSearch';
  // import selectLoad from '/@/views/upms/components/seletLoad.vue';
  import { accountColumns, accountSearchFormSchema } from './notice.data';
  import { getAccountList } from '/@/views/upms/api/account';
  import { deptAsyn } from '/@/views/upms/api/dept';
  const emit = defineEmits(['select']);
  const tree = ref<InstanceType<typeof VTreeSearch> | null>(null);
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  // const treeDate = ref<any[]>([]);
  // const getTreeData = async () => {
  //   // const res = await ALLOrgList();
  //   // treeDate.value = res;
  // };
  const deptId = ref('');
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const searchRef = ref({});
  const sub = ref(true);
  const tableData = ref([]);
  // const deptCode = ref('');
  const selectionIds = ref<string[]>([]);
  const name = ref('');
  const searchName = ref('');
  const nodeId = ref('');
  // getTreeData();

  //   console.log(data);
  //   sub.value = data;
  //   accountList();
  // };
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    accountList();
  };
  const getDeptData = (node, resolve) => {
    deptAsyn({
      deptId: node?.id || '',
      name: name.value,
    }).then((res) => {
      if (resolve) {
        resolve(res);
      } else {
        setTimeout(() => {
          if (node?.id) {
            tree.value.updateNode(node.id, { children: res });
          } else {
            tree.value.loadRootNodes(res);
          }
        }, 0);
      }
      // 搜索展开所有节点
      if (name.value) {
        setTimeout(() => {
          tree.value.setExpandAll(true);
        }, 0);
      }
    });
  };
  const handleTreeClick = (e) => {
    if (e.id == nodeId.value) {
      nodeId.value = '';
    } else {
      nodeId.value = e.id;
    }
  };
  function handleSearch(type) {
    // type 0自动触发搜索  1点击搜索按钮
    if (!type && searchName.value.length < 4) {
      return false;
    }
    nodeId.value = '';
    name.value = searchName.value;
    getDeptData();
  }
  const accountList = () => {
    const { pageNum, pageSize } = page.value;
    const { available, employeeName, userName } =
      searchRef?.value?.['searchValue'] || {};
    getAccountList({
      pageNum,
      pageSize,
      // instCode: deptCode.value,
      deptId: deptId.value,
      available,
      userName,
      employeeName,
      sub: sub.value ? '1' : '0',
      ...sortData.value,
    }).then((res: AccountListGetResultModel) => {
      const { list, total, currentPage, pageSize } = res;
      tableData.value = list;
      page.value = {
        total,
        pageNum: currentPage,
        pageSize,
      };
    });
  };
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    accountList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    accountList();
  };
  const handleCurrentChange = (val) => {
    selectionIds.value = [];
    for (let i = 0; i < val.length; i++) {
      // selectionIds.value.push({
      //   userId: val[i].id,
      //   phone: val[i].sysEmployee.phone,
      // });
      selectionIds.value.push(val[i].id);
    }
    emit('select', selectionIds.value);
  };
  watch(
    () => nodeId.value,
    (val) => {
      if (!!val) {
        deptId.value = val;
        accountList();
      } else {
        deptId.value = '';
        accountList();
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );
</script>

<style scoped lang="scss">
  /* 拖拽相关样式 */
  /*包围div样式*/

  /*左侧div样式*/
  .account-content {
    height: calc(100vh - 300px);
  }
  .left {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    background: #ffffff;
    border-radius: 6px;
    .tree-content {
      height: 100%;
    }
    .tree-div {
      padding: 12px;
      height: calc(100% - 155px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .right {
    width: 100%; /*左侧初始化宽度*/
    height: 100%;
    border-radius: 6px;
    // background: #ffffff;
    .search-all {
      background: #ffffff;
    }
    .table-content {
      background: #ffffff;
      height: calc(100% - 54px);
      padding: 10px 16px 0;
      position: relative;
      overflow: auto;
      // margin-top: 10px;
      .top-button {
        position: absolute;
        top: 10px;
        left: 16px;
        z-index: 9;
        .upload-btn {
          display: inline-block;
          margin: 0 12px;
        }
      }
    }
  }

  h4 {
    font-size: 18px;
    margin-bottom: 0;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }
  // .item-div {
  //   display: flex;
  //   .item-btn {
  //     width: 80px;
  //     i {
  //       margin-top: 26px;
  //     }
  //   }
  //   .item-form {
  //     flex: 1;
  //   }
  // }
  .item-div {
    border-bottom: 1px solid #cccccc;
    .item-btn {
      margin-bottom: 12px;
      display: flex;
      justify-content: flex-end;
    }
    .item-form {
      margin-top: 12px;
    }
  }
</style>
