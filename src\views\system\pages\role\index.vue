<template>
  <div class="role-container">
    <div class="role-header">
      <h3>角色管理</h3>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <basic-search
        :formList="searchFormSchema"
        ref="searchRef"
        @onSearch="handleSearch"
        @reset="handleReset"
      />
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <el-button type="primary" @click="handleAdd" :icon="Plus">
        新增角色
      </el-button>
      <el-button
        type="danger"
        @click="handleBatchDelete"
        :disabled="selectedIds.length === 0"
        :icon="Delete"
      >
        批量删除
      </el-button>
      <el-button type="info" @click="handleRefresh" :icon="Refresh">
        刷新
      </el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <basic-table
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :current-page="pagination.current"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
        @selectionChange="handleSelectionChange"
        height="calc(100vh - 350px)"
      >
        <!-- 状态插槽 -->
        <template #status="{ record }">
          <el-tag v-if="record.status === '0'" type="success">正常</el-tag>
          <el-tag v-else type="danger">停用</el-tag>
        </template>

        <!-- 操作插槽 -->
        <template #action="{ record }">
          <el-button type="primary" link @click="handleEdit(record)">
            编辑
          </el-button>
          <el-button type="primary" link @click="handleAssignMenus(record)">
            分配权限
          </el-button>
          <el-button type="danger" link @click="handleDelete(record)">
            删除
          </el-button>
        </template>
      </basic-table>
    </div>

    <!-- 角色编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <basic-form
        ref="formRef"
        :formList="roleFormSchema"
        :formData="formData"
        :showSubmit="false"
        :isCreate="false"
      />
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <el-dialog
      v-model="menuDialogVisible"
      title="分配权限"
      width="400px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-tree
        ref="menuTreeRef"
        :data="menuTreeData"
        show-checkbox
        node-key="id"
        :default-checked-keys="checkedMenuIds"
        :props="{ children: 'children', label: 'title' }"
        check-strictly
      />
      <template #footer>
        <el-button @click="menuDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveMenus" :loading="menuSubmitLoading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Refresh } from '@element-plus/icons-vue';
import BasicSearch from '/@/components/sys/BasicSearch/src/BasicSearch.vue';
import BasicTable from '/@/components/sys/BasicTable/src/BasicTable.vue';
import BasicForm from '/@/components/sys/BasicForm/src/BasicForm.vue';
import { columns, searchFormSchema, roleFormSchema } from '../roles/role.data';
import {
  getRolePageList,
  saveRole,
  updateRole,
  deleteRoles,
  type RoleInfo,
  type RoleQueryParams
} from '/@/api/sys/roles';
import { getMenuList, type MenuInfo } from '/@/api/sys/menus';

// 响应式数据
const searchRef = ref();
const tableRef = ref();
const formRef = ref();
const menuTreeRef = ref();

const tableData = ref<RoleInfo[]>([]);
const selectedIds = ref<string[]>([]);
const loading = ref(false);
const submitLoading = ref(false);
const menuSubmitLoading = ref(false);

// 分页数据
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 对话框状态
const dialogVisible = ref(false);
const menuDialogVisible = ref(false);
const dialogTitle = ref('');
const isEdit = ref(false);

// 表单数据
const formData = ref<RoleInfo>({
  roleName: '',
  roleKey: '',
  roleSort: 0,
  status: '0',
  remark: '',
});

// 菜单权限相关数据
const menuTreeData = ref<MenuInfo[]>([]);
const checkedMenuIds = ref<string[]>([]);
const currentRoleId = ref('');

// 搜索参数
const searchParams = ref<RoleQueryParams>({});

// 生命周期
onMounted(() => {
  loadRoleList();
  loadMenuTree();
});

// 加载角色列表
const loadRoleList = async () => {
  try {
    loading.value = true;
    const params = {
      ...searchParams.value,
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    };

    const result = await getRolePageList(params);
    if (result && result.records) {
      tableData.value = result.records;
      pagination.total = result.total;
    }
  } catch (error) {
    ElMessage.error('加载角色列表失败');
    console.error('Load role list error:', error);
  } finally {
    loading.value = false;
  }
};

// 加载菜单树
const loadMenuTree = async () => {
  try {
    const result = await getMenuList();
    if (result) {
      menuTreeData.value = result;
    }
  } catch (error) {
    console.error('Load menu tree error:', error);
  }
};

// 搜索处理
const handleSearch = (values: any) => {
  searchParams.value = values;
  pagination.current = 1;
  loadRoleList();
};

// 重置搜索
const handleReset = () => {
  searchParams.value = {};
  pagination.current = 1;
  loadRoleList();
};

// 刷新
const handleRefresh = () => {
  loadRoleList();
};

// 分页处理
const handlePageChange = (page: number) => {
  pagination.current = page;
  loadRoleList();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.current = 1;
  loadRoleList();
};

// 选择处理
const handleSelectionChange = (selection: RoleInfo[]) => {
  selectedIds.value = selection.map(item => item.id!);
};

// 新增角色
const handleAdd = () => {
  dialogTitle.value = '新增角色';
  isEdit.value = false;
  formData.value = {
    roleName: '',
    roleKey: '',
    roleSort: 0,
    status: '0',
    remark: '',
  };
  dialogVisible.value = true;
};

// 编辑角色
const handleEdit = (record: RoleInfo) => {
  dialogTitle.value = '编辑角色';
  isEdit.value = true;
  formData.value = { ...record };
  dialogVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate();
    if (!valid) return;

    submitLoading.value = true;

    if (isEdit.value) {
      await updateRole(formData.value);
      ElMessage.success('更新角色成功');
    } else {
      await saveRole(formData.value);
      ElMessage.success('新增角色成功');
    }

    dialogVisible.value = false;
    loadRoleList();
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新角色失败' : '新增角色失败');
    console.error('Submit role error:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 删除角色
const handleDelete = async (record: RoleInfo) => {
  try {
    await ElMessageBox.confirm('确定要删除该角色吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await deleteRoles(record.id!);
    ElMessage.success('删除角色成功');
    loadRoleList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除角色失败');
      console.error('Delete role error:', error);
    }
  }
};

// 批量删除
const handleBatchDelete = async () => {
  if (selectedIds.value.length === 0) {
    ElMessage.warning('请选择要删除的角色');
    return;
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedIds.value.length} 个角色吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    });

    await deleteRoles(selectedIds.value.join(','));
    ElMessage.success('批量删除角色成功');
    selectedIds.value = [];
    loadRoleList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除角色失败');
      console.error('Batch delete roles error:', error);
    }
  }
};

// 分配权限
const handleAssignMenus = async (record: RoleInfo) => {
  currentRoleId.value = record.id!;
  checkedMenuIds.value = record.menuIds || [];
  menuDialogVisible.value = true;
};

// 保存权限分配
const handleSaveMenus = async () => {
  try {
    menuSubmitLoading.value = true;

    const checkedKeys = menuTreeRef.value?.getCheckedKeys() || [];
    const halfCheckedKeys = menuTreeRef.value?.getHalfCheckedKeys() || [];
    const menuIds = [...checkedKeys, ...halfCheckedKeys];

    await updateRole({
      id: currentRoleId.value,
      menuIds,
    } as RoleInfo);

    ElMessage.success('分配权限成功');
    menuDialogVisible.value = false;
    loadRoleList();
  } catch (error) {
    ElMessage.error('分配权限失败');
    console.error('Save role menus error:', error);
  } finally {
    menuSubmitLoading.value = false;
  }
};
</script>

<style scoped lang="scss">
.role-container {
  height: calc(100vh - 62px);
  border: 1px solid #e6e8ee;
  background-color: #fff;
  border-radius: 8px;
  margin: 0 8px 8px 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .role-header {
    border-bottom: 1px solid #e6e8ee;
    background: #fff;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    padding: 16px 20px;
    display: flex;
    position: relative;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .search-section {
    padding: 16px 20px;
    border-bottom: 1px solid #e6e8ee;
    background: #fafafa;
  }

  .action-section {
    padding: 16px 20px;
    border-bottom: 1px solid #e6e8ee;
    display: flex;
    gap: 12px;
  }

  .table-section {
    flex: 1;
    padding: 20px;
    overflow: hidden;
  }
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-tree) {
  max-height: 400px;
  overflow-y: auto;
}
</style>
