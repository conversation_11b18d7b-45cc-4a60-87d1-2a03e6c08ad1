<!DOCTYPE html>
<html>
<head>
    <title>Agent Status Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-case { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .status { font-weight: bold; }
        .status.in_progress { color: orange; }
        .status.completed { color: green; }
        .status.failed { color: red; }
        .status.pending { color: gray; }
    </style>
</head>
<body>
    <h1>Agent Status Debug Test</h1>
    
    <div id="test-results"></div>
    
    <script>
        // 模拟状态映射函数
        function getAgentStatusClass(status) {
            const statusMap = {
                'in_progress': 'status-running',
                'IN_PROGRESS': 'status-running',
                'RUNNING': 'status-running',
                'running': 'status-running',
                'completed': 'status-completed',
                'COMPLETED': 'status-completed',
                'finished': 'status-completed',
                'FINISHED': 'status-completed',
                'failed': 'status-failed',
                'FAILED': 'status-failed',
                'error': 'status-failed',
                'ERROR': 'status-failed',
                'pending': 'status-pending',
                'PENDING': 'status-pending',
                'waiting': 'status-pending',
                'WAITING': 'status-pending'
            };
            return statusMap[status] || 'status-unknown';
        }

        function getAgentStatusText(status) {
            const statusMap = {
                'in_progress': '执行中',
                'IN_PROGRESS': '执行中',
                'RUNNING': '执行中',
                'running': '执行中',
                'completed': '已完成',
                'COMPLETED': '已完成',
                'finished': '已完成',
                'FINISHED': '已完成',
                'failed': '执行失败',
                'FAILED': '执行失败',
                'error': '执行失败',
                'ERROR': '执行失败',
                'pending': '等待中',
                'PENDING': '等待中',
                'waiting': '等待中',
                'WAITING': '等待中'
            };
            return statusMap[status] || status;
        }

        // 模拟智能推断函数
        function inferAgentStatus(group) {
            const { steps, agentCompleted, agentStatus } = group;
            
            if (agentCompleted === true) {
                return 'completed';
            }
            
            if (steps && steps.length > 0) {
                const lastStep = steps[steps.length - 1];
                
                const hasTerminateStep = steps.some(step => 
                    step.toolName === 'terminate' && 
                    (step.status === 'FINISHED' || step.status === 'COMPLETED')
                );
                
                if (hasTerminateStep) {
                    return 'completed';
                }
                
                const hasFailedStep = steps.some(step => 
                    step.status === 'FAILED' || step.status === 'ERROR'
                );
                
                if (hasFailedStep) {
                    return 'failed';
                }
                
                if (lastStep.status === 'FINISHED' || lastStep.status === 'COMPLETED') {
                    return agentStatus || 'in_progress';
                }
                
                if (lastStep.status === 'RUNNING' || lastStep.status === 'IN_PROGRESS') {
                    return 'in_progress';
                }
            }
            
            return agentStatus || 'in_progress';
        }

        // 测试用例
        const testCases = [
            {
                name: "Agent with in_progress status",
                group: {
                    agentStatus: 'in_progress',
                    agentCompleted: false,
                    steps: [
                        { status: 'RUNNING', toolName: 'query' }
                    ]
                }
            },
            {
                name: "Agent with completed status",
                group: {
                    agentStatus: 'completed',
                    agentCompleted: true,
                    steps: [
                        { status: 'FINISHED', toolName: 'terminate' }
                    ]
                }
            },
            {
                name: "Agent with undefined status",
                group: {
                    agentStatus: undefined,
                    agentCompleted: false,
                    steps: [
                        { status: 'RUNNING', toolName: 'query' }
                    ]
                }
            },
            {
                name: "Agent with null status",
                group: {
                    agentStatus: null,
                    agentCompleted: false,
                    steps: [
                        { status: 'FINISHED', toolName: 'query' }
                    ]
                }
            }
        ];

        // 运行测试
        const resultsDiv = document.getElementById('test-results');
        
        testCases.forEach((testCase, index) => {
            const inferredStatus = inferAgentStatus(testCase.group);
            const statusClass = getAgentStatusClass(inferredStatus);
            const statusText = getAgentStatusText(inferredStatus);
            
            const testDiv = document.createElement('div');
            testDiv.className = 'test-case';
            testDiv.innerHTML = `
                <h3>Test ${index + 1}: ${testCase.name}</h3>
                <p>Original Status: <span class="status ${testCase.group.agentStatus}">${testCase.group.agentStatus || 'undefined'}</span></p>
                <p>Inferred Status: <span class="status ${inferredStatus}">${inferredStatus}</span></p>
                <p>Status Class: ${statusClass}</p>
                <p>Status Text: ${statusText}</p>
                <p>Agent Completed: ${testCase.group.agentCompleted}</p>
                <p>Steps: ${JSON.stringify(testCase.group.steps, null, 2)}</p>
            `;
            
            resultsDiv.appendChild(testDiv);
        });
    </script>
</body>
</html>
