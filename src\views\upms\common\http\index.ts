import { createAxios } from '/@/utils/axios';
import { loadEnv, warpperEnv } from '@build/index';
import { ContentTypeEnum, } from '/@/enums/httpEnum';
const { VITE_APP_BASE_URL_PREFIX } = warpperEnv(loadEnv());
export const sysPreFix = VITE_APP_BASE_URL_PREFIX + '/sys';

export const sysHttp = createAxios({
  requestOptions: {
    urlPrefix: sysPreFix,
    cancelTimeIntetval: 0,
  },
});

export const downloadHttp = createAxios({
  requestOptions: {
    urlPrefix: sysPreFix,
    isReturnNativeResponse: true,
  },
  responseType: 'blob',
});

export const uploadHttp = createAxios({
  requestOptions: {
    urlPrefix: sysPreFix,
    isReturnNativeResponse: true,
  },
  headers: { 'Content-Type': ContentTypeEnum.FORM_DATA },
});
