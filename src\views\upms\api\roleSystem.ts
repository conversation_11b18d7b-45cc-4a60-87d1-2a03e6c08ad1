/*
 * @Author: your name
 * @Date: 2021-08-17 08:50:46
 * @LastEditTime: 2021-08-17 09:20:54
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \cpit-upms-ui\src\api\admin\roleSystem.ts
 */
import {
  MenuParams,
  RolePageParams,
  MenuListGetResultModel,
  MenuListGetResultModelAll,
  RolePageListGetResultModel,
  RoleDict,
  RoleDictItem,
  saveRoleModel,
  SaveRoleItem,
  DeleteRoleType,
  AuthorizedUser,
} from '/@/views/upms/api/model/roleSystemModel';
import { sysHttp, downloadHttp, uploadHttp } from '/@/views/upms/common/http';

enum Api {
  DeptList = '/system/getDeptList',
  MenuList = '/menu/user-tree',
  MenuListAll = '/dept/tree/',
  RolePageList = '/role/',
  RoleDictData = '/dict/codes/',
  RoleLockOrUnLock = '/role/',
  GetAllRoleList = '/system/getAllRoleList',
  setRoleStatus = '/system/setRoleStatus',
  getAuthorizedUser = '/role/getAuthorizedUser',
  deleteAuthRole = '/role/deleteAuthRole',
  roleAuthorization = '/role/roleAuthorization',
  download = '/role/download',
  updateMenuUrl = '/role/updateRoleMenu',
  updateDataUrl = '/role/updateDataScope',
  delIdsUrl = '/role/delRoles',
  downloadUrl = '/role/download',
  uploadUrl = '/role/upload',
}
export const getRoleListByPage = (params?: RolePageParams) =>
  sysHttp.get<RolePageListGetResultModel>({ url: Api.RolePageList, params });

// export const getRoleLockOrUnLock = (ids: string) =>
//   defHttp.put<RoleLock>({ url: Api.RoleLockOrUnLock+'/${ids}'});
export const getRoleLockOrUnLock = (id) => sysHttp.put({ url: `/role/${id}` });

export const removeByIds = (params: DeleteRoleType) =>
  sysHttp.delete({
    url: `/role/${params.ids}?tenantId=${params.tenantId}&projectId=${params.projectId}`,
  });

export const getRoleDict = (params?: RoleDictItem) =>
  sysHttp.get<RoleDict>({ url: Api.RoleDictData, params });

export const getMenuList = (params?: MenuParams) =>
  sysHttp.get<MenuListGetResultModel>({ url: Api.MenuList, params });

export const getDeptListAllTree = (params?: MenuParams) =>
  sysHttp.get<MenuListGetResultModelAll>({ url: Api.MenuListAll, params });

export const saveRole = (params?: SaveRoleItem) =>
  sysHttp.post<saveRoleModel>({ url: Api.RolePageList, params });

export const getDetail = (id: string) => sysHttp.get({ url: `/role/${id}` });

export const getAuthorizedUser = (params?: AuthorizedUser) =>
  sysHttp.post({ url: Api.getAuthorizedUser, params });
// 取消授权
export const deleteAuthRole = (params) =>
  sysHttp.post({ url: Api.deleteAuthRole, params });
// 批量授权
export const roleAuthorization = (params) =>
  sysHttp.post({ url: Api.roleAuthorization, params });
// 角色导出
export const roleDownLoad = (params?: RolePageParams) =>
  downloadHttp.post<RolePageListGetResultModel>({ url: Api.download, params });

// const updateMenuApi =
export const updateMenuApi = (params) =>
  sysHttp.post({ url: Api.updateMenuUrl, params });
updateMenuApi;

export const updateDataApi = (params) =>
  sysHttp.post({ url: Api.updateDataUrl, params });

export const delIdsApi = (params) => sysHttp.post({ url: Api.delIdsUrl, params });
export const downLoadApi = (params) =>
  downloadHttp.post({ url: Api.downloadUrl, params });
//

export const uploadApi = (params) => {
  return uploadHttp.post({
    url: Api.uploadUrl,
    params,
  });
};
