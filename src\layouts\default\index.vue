<template>
  <el-container
    v-if="isVertical && !!userStore.getUserInfo?.id"
    class="layout-container"
    v-resize
  >
    <el-aside
      :style="{
        width: getMenuCollapsed
          ? appStore.getProjectConfig.collapseWidth + 'px'
          : appStore.getProjectConfig.menuWidth + 'px',
      }"
    >
      <el-scrollbar>
        <default-side :menu-list="menus" />
      </el-scrollbar>
    </el-aside>
    <el-container>
      <el-header>
        <default-header />
      </el-header>
      <default-main />
    </el-container>
  </el-container>
  <el-container
    v-if="isHorizontal && !!userStore.getUserInfo?.id"
    class="layout-container"
    v-resize
  >
    <el-header>
      <default-side :mode="appStore.getProjectConfig.layout" :menu-list="menus" />
    </el-header>
    <el-main>
      <el-header v-show="!isMicro">
        <default-header />
      </el-header>
      <default-main />
    </el-main>
  </el-container>
  <el-dialog
    v-model="visible"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <template #header>
      <div class="my-header">
        <h4>修改密码</h4>
      </div>
    </template>
    <basic-form
      ref="formRef"
      :isCreate="false"
      :form-list="updatepwdFormSchema"
      :formData="formData"
      :showSubmit="false"
      :check-strictly="false"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="saveForm"> 保存 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup name="DefaultLayout">
  import { ElButton } from 'element-plus';
  import DefaultHeader from './components/DefaultHeader.vue';
  import DefaultSide from '/@/layouts/default/components/DefaultSide.vue';
  import { usePermissionStore } from '/@/stores/modules/permission';
  import emitter from '/@/utils/emitter';
  import { useMenuSettings } from '/@/hooks/settings/useMenuSettings';
  import { useAppStore } from '/@/stores/modules/app';
  import DefaultMain from '/@/layouts/default/components/DefaultMain.vue';
  import router from '/@/router';
  import { getPassWordStatusStorage } from '/@/utils/storage/auth';
  import { computed, ref } from 'vue';
  import { updatepwdFormSchema } from './updatepwd.data';
  import BasicForm from '/@/components/sys/BasicForm';
  import { getAuthStorage } from '/@/utils/storage/auth';
  import { updatePwdParams } from '/@/views/shares/api/model/userSetting';
  import { UpdatePassword } from '/@/views/shares/api/userSetting';
  import type { FormInstance } from 'element-plus';
  import { useLogout } from '/@/hooks/web/useLogout';
  import { encryptedSM4 } from '/@/utils/cipher';
  import { useUserStore } from '/@/stores/modules/user';
  const { logout } = useLogout();
  const userStore = useUserStore();
  const formRef = ref<FormInstance | null>(null);
  const appStore = useAppStore();
  const permissionStore = usePermissionStore();
  const menus = computed(() => permissionStore.getMenuList);
  let isAutoCloseSidebar = false;
  const { toggleClose, toggleOpen, getMenuCollapsed, isVertical, isHorizontal } =
    useMenuSettings();
  const { currentRoute } = router;
  const isMicro = computed(() => {
    return (
      currentRoute.value.path.indexOf('/micro/vite') > -1 ||
      currentRoute.value.path.indexOf('/micro/app') > -1
    );
  });
  // 监听容器
  emitter.on('resize', ({ detail }) => {
    // if (isMobile) return;
    let { width } = detail;
    // width <= 670 ? setTheme("vertical") : setTheme(useAppStoreHook().layout);
    /** width app-wrapper类容器宽度
     * 0 < width <= 760 隐藏侧边栏
     * 760 < width <= 990 折叠侧边栏
     * width > 990 展开侧边栏
     */
    if (width > 0 && width <= 760) {
      isAutoCloseSidebar = true;
      toggleClose();
    } else if (width > 760 && width <= 990) {
      if (isAutoCloseSidebar) {
        isAutoCloseSidebar = false;
        toggleOpen();
      }
    } else if (width > 990) {
      if (isAutoCloseSidebar) {
        isAutoCloseSidebar = false;
        toggleOpen();
      }
    }
  });

  const userInfo: any = getAuthStorage();
  const visible = computed(() => {
    return getPassWordStatusStorage();
  });

  const formData = ref<updatePwdParams>();
  const saveForm = () => {
    const getData = formRef.value && formRef.value?.submitForm;
    const ruleFormRef = formRef.value && formRef.value?.ruleFormRef;
    getData(ruleFormRef, (status, data) => {
      if (status === 'success') {
        console.log(data);
        if (data.newPasswd != data.confirmnewPasswd) {
          ElMessage({
            message: '新密码和确认新密码不一致,请重新输入',
            grouping: true,
            type: 'warning',
          });
          return false;
        }
        if (
          data.userPasswd === data.newPasswd ||
          data.userPasswd === data.confirmnewPasswd
        ) {
          ElMessage({
            message: '新密码和旧密码不可以一致,请重新输入',
            grouping: true,
            type: 'warning',
          });
          return false;
        }
        UpdatePassword({
          id: userInfo.id,
          userName: userInfo.userName,
          userPasswd: encryptedSM4(data.userPasswd),
          newPasswd: encryptedSM4(data.newPasswd),
        }).then(() => {
          ElMessage({
            message: '修改密码成功',
            type: 'success',
          });
          // 退出登录
          // noticedialog.value?.close();
          logout();
        });
      }
    });
  };

  resetPwd();
  function resetPwd() {
    if (!userInfo?.id) {
      logout();
      return;
    }
    formData.value = {
      id: userInfo.id,
      userName: userInfo.userName,
      userPasswd: '',
      newPasswd: '',
    };
  }
</script>

<style scoped lang="scss">
  @import 'src/styles/element-variables.scss';
  .layout-container .el-main {
    height: calc(100vh - #{$headerHeight} - 10px);
    padding: 0;
  }

  .layout-container .el-header {
    --el-header-height: $headerHeight;
    position: relative;
    padding: 0;
    color: var(--el-text-color-primary);
    background-color: var(--el-color-primary-light-7);
    // background-color: rgba(205, 208, 220, 0.8);
  }
</style>
