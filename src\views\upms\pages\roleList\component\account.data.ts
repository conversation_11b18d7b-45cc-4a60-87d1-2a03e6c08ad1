import { TableOptions } from '/@/components/sys/BasicTable/types';
import { SearchOptions } from '/@/components/sys/BasicSearch/types';
export const columns: TableOptions[] = [
  {
    type: 'selection',
    width: 50,
    label: '复选',
    align: 'center',
    selectable: function (row) {
      if (row.mark == '1') {
        return false;
      } else {
        return true;
      }
    },
  },
  {
    label: '账号名',
    prop: 'userName',
    align: 'center',
  },
  {
    label: '员工姓名',
    prop: 'employeeName',
    align: 'center',
  },
  {
    label: '机构',
    prop: 'deptName',
    align: 'center',
  },
  {
    label: '操作',
    prop: 'action',
    action: true,
    fixed: 'right',
    width: 100,
    align: 'center',
  },
];

export const searchFormSchema: SearchOptions[] = [
  {
    field: 'userName',
    label: '账号名:',
    component: 'Input',
    placeholder: '请输入账号名',
    span: 8,
    componentProps: {
      clearable: true,
    },
  },
];
