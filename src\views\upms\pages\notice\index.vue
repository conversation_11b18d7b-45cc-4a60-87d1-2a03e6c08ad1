<template>
  <div class="content">
    <div class="box">
      <div class="search-div">
        <p class="title"> 通知公告搜索 </p>
        <basic-search
          :searchArray="searchFormSchema"
          class="search-all"
          ref="searchRef"
          :labelWidth="80"
          :labelShow="false"
          @reset="noticeList"
          @onSearch="noticeList"
        />
      </div>

      <div class="table-content">
        <div class="table-top-div">
          <p>通知公告列表</p>
          <div class="top-button">
            <el-button
              type="primary"
              @click="handleAdd"
              v-auth="['platform_notice_add']"
              >新增</el-button
            >
            <!--<el-button type="primary" @click="downLoad(false)"
              >导出</el-button
            >
            <el-button
              type="primary"
              @click="fileVisible = true"
              >导入</el-button
            >-->
            <el-button
              type="danger"
              @click="handleDelete(null, '1')"
              :disabled="selectionIds.length > 0 ? false : true"
              v-auth="['platform_notice_delete']"
              >批量删除</el-button
            >
          </div>
        </div>
        <basic-table
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :total="page.total"
          :page-size="page.pageSize"
          :current-page="page.pageNum"
          @page-change="pageChange"
          @size-change="sizeChange"
          @selectionChange="handleCurrentChange"
          @sortChange="sortChange"
          :downSetting="true"
          height="calc(100vh - 392px)"
        >
          <template #type="{ record }">
            <span v-if="record.type === '1'">用户消息</span>
            <span v-else-if="record.type === '2'">系统消息</span>
            <span v-else>公告消息</span>
          </template>
          <template #level="{ record }">
            <span v-if="record.type === '1'">高</span>
            <span v-else-if="record.type === '2'">中</span>
            <span v-else>低</span>
          </template>
          <template #status="{ record }">
            <span v-if="record.status === '1'">未发布</span>
            <span v-else-if="record.status === '2'">已发布</span>
            <span v-else>已撤销</span>
          </template>
          <template #noticeTarget="{ record }">
            <span v-if="record.noticeTarget === '0'">全体账号</span>
            <span v-else-if="record.noticeTarget === '1'">指定账号</span>
            <span v-else>撤销</span>
          </template>

          <template #action="{ record }">
            <el-button
              type="primary"
              link
              @click="handleDelete(record, '0')"
              v-if="record.status != '2'"
              v-auth="['platform_notice_delete']"
              >删除</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleEdit(record)"
              v-if="record.status != '2'"
              v-auth="['platform_notice_edit']"
              >编辑</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleDetail(record)"
              v-auth="['platform_notice_details']"
              >查看</el-button
            >
            <el-button
              type="primary"
              link
              @click="handleRevoke(record)"
              v-show="record.status === '2'"
              v-auth="['platform_notice_revoke']"
              >撤回</el-button
            >
            <el-button
              type="primary"
              link
              @click="handlePush(record)"
              v-show="record.status === '1' || record.status === '3'"
              v-auth="['platform_notice_push']"
              >发布</el-button
            >
          </template>
        </basic-table>
      </div>
    </div>

    <el-dialog v-model="dialog" :destroy-on-close="true" width="800px">
      <template #header>
        <h4>{{ title }}</h4>
      </template>
      <template #default>
        <basic-form
          :formList="formSchema"
          :isCreate="false"
          :formData="formData"
          :showSubmit="false"
          :check-strictly="true"
          :disabled="!isEdit"
          ref="formPost"
        >
          <template #noticeTarget>
            <el-radio-group v-model="formData.noticeTarget">
              <el-radio label="0" @click="onChange('0')">全体账号</el-radio>
              <el-radio label="1" @click="onChange('1')">指定账号</el-radio>
            </el-radio-group>
          </template>
          <template #time>
            <el-date-picker
              v-model="time"
              type="daterange"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              date-format="YYYY/MM/DD"
              @change="
                (e) => {
                  formData.time = e;
                  console.log('e', e);
                }
              "
              :disabled-date="disabledDate"
            />
          </template>
          <template #template>
            <el-select
              style="width: 100%"
              v-model="formData.templateID"
              placeholder="请选择"
              filterable
              @change="onChangeTemplate"
            >
              <el-option
                v-for="item in templateList"
                :key="item.content"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </template>
          <template #content>
            <div id="editor—wrapper">
              <Editor ref="vueWangeditor" style="width: 100%" />
            </div>
          </template>
        </basic-form>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="dialog = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmClick"
            v-if="isEdit"
            :loading="loading"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="accountDialogVisible"
      title="选择人员"
      width="1200px"
      destroy-on-close
    >
      <Account @select="accountSelect" />
      <template #footer>
        <div style="flex: auto">
          <el-button @click="accountDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="accountDialogVisible = false"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
    <el-dialog
      modal-class="model-dialog"
      v-model="viewDialogVisible"
      :show-close="false"
      :width="600"
    >
      <template #header="{ close }">
        <div class="my-header" style="display: flex; justify-content: space-between">
          <h4>查看消息公告</h4>
          <div class="header-right">
            <el-icon @click="close"><Close /></el-icon>
          </div>
        </div>
      </template>
      <el-descriptions class="margin-top" :column="1" border>
        <el-descriptions-item
          v-for="item in viewFormSchema"
          :key="item.code"
          label-class-name="my-label"
        >
          <!--<el-descriptions-item label-class-name="my-label"  >-->
          <template #label>
            <div class="cell-item"> {{ item.name }} </div>
          </template>
          <div v-if="item.field === 'type'">
            <span
              v-for="dictItem in getDictStorage().biz_notice_type"
              :key="dictItem.value"
            >
              <span v-if="dictItem.value == formData[item.field]">{{
                dictItem.label
              }}</span>
            </span>
          </div>
          <div v-else-if="item.field === 'level'">
            <span
              v-for="dictItem in getDictStorage().biz_notice_level"
              :key="dictItem.value"
            >
              <span v-if="dictItem.value == formData[item.field]">{{
                dictItem.label
              }}</span>
            </span>
          </div>
          <div v-else-if="item.field === 'status'">
            <span
              v-for="dictItem in getDictStorage().biz_notice_status"
              :key="dictItem.value"
            >
              <span v-if="dictItem.value == formData[item.field]">{{
                dictItem.label
              }}</span>
            </span>
          </div>
          <div v-else-if="item.field === 'noticeTarget'">
            <span v-if="formData[item.field] == '0'">全体账号</span>
            <span v-else>{{ formData.userNames }}</span>
          </div>
          <!--<div id="editor—wrapper" v-else-if="item.field==='content'">
              <Editor ref="vueWangeditor" style="width: 100%" :readOnly="true" />
            </div>-->
          <div v-else>{{ formData[item.field] }}</div>
        </el-descriptions-item>
        <!--template #label v-else>
            <div class="cell-item">
              {{ item.name }}
            </div>
          </!--template>
          {{ formData[item.field] }}
        </el-descriptions-item>

        <el-descriptions-item label-class-name="my-label">
          <template #label>
            <div class="cell-item"> 发送时间 </div>
          </template>
          <div>
            <span v-if="formData.msgType === '1'">{{ formData.cron }}</span>
            <span v-else>立即发送</span>
          </div>
        </el-descriptions-item>-->
        <el-descriptions-item label-class-name="my-label">
          <template #label>
            <div class="cell-item"> 内容 </div>
          </template>
          <div id="editor—wrapper">
            <Editor ref="vueWangeditor" style="width: 100%" :readOnly="true" />
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  import { ref, markRaw, nextTick } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { CircleClose } from '@element-plus/icons-vue';
  import dayjs from 'dayjs';
  import { getDictStorage } from '/@/utils/storage/dict';
  import { columns, formSchema, searchFormSchema, viewFormSchema } from './notice.data';
  import BasicTable from '/@/components/sys/BasicTable';
  import {
    NoticeItem,
    NoticeListResultModel,
    AddNoticeParams,
  } from '/@/views/upms/api/model/notice';
  import {
    postNoticeList,
    saveNotice,
    deleteNotice,
    deleteNoticeId,
    getNotice,
    pushNotice,
    noticeRevoke,
    getNoticeTemplates,
  } from '/@/views/upms/api/notice';
  // import useUpload from '/@/hooks/upms/upload/useUpload';
  import Editor from '/@/components/sys/Editor.vue';
  // import type { UploadInstance } from 'element-plus';
  import Account from './Account.vue';
  // const upload = ref<UploadInstance>();
  // const { fileVisible, fileData, handleExceed, handleChange } = useUpload(upload);
  const tableData = ref<NoticeItem[]>([]);
  const searchRef = ref({});
  const page = ref({
    total: 0,
    pageSize: 10,
    pageNum: 1,
  });
  const formData = ref<AddNoticeParams>({
    id: '',
    title: '',
    level: '',
    type: '',
    content: '',
    version: '',
    description: '',
    startTime: '',
    endTime: '',
    noticeTarget: '0',
  });
  const time = ref('');
  const userIds = ref([]);
  const tableRef = ref<InstanceType<typeof BasicTable>>();
  const dialog = ref(false);
  const isEdit = ref(true);
  const title = ref('新增');
  const formPost = ref({
    time: '',
  });
  const selectionIds = ref<string[]>([]);
  const loading = ref(false);
  // const uploadLoading = ref(false);
  const sortData = ref({
    orderBy: '',
    sortOrder: '',
  });
  const accountDialogVisible = ref(false);
  const viewDialogVisible = ref(false);
  const vueWangeditor = ref();
  const templateList = ref([]);
  const sortChange = ({ prop, order }) => {
    sortData.value.orderBy = prop;
    if (order === 'descending') {
      sortData.value.sortOrder = 'desc';
    }
    if (order === 'ascending') {
      sortData.value.sortOrder = 'asc';
    }
    if (order === null) {
      sortData.value.sortOrder = '';
    }
    noticeList();
  };
  // 通知公告列表
  const noticeList = () => {
    const { pageNum, pageSize } = page.value;
    const { title } = searchRef?.value?.['searchValue'] || {};
    const params = {
      pageNum,
      pageSize,
      title,
      ...sortData.value,
    };
    postNoticeList(params).then((res: NoticeListResultModel) => {
      tableData.value = res.list;
      page.value.total = res.total;
    });
  };
  /**
   * 切换分页，每页显示数量
   */
  const sizeChange = (val: number) => {
    page.value.pageSize = val;
    noticeList();
  };

  /**
   * 切换分页，当前显示的页数
   */
  const pageChange = (val: number) => {
    page.value.pageNum = val;
    noticeList();
  };
  // const disabledDate = (time, type) => {
  //   if (type === 'start') {
  //     return time.getTime() < new Date().setHours(0,0,0,0);
  //   }
  //   return false; // 结束日期不限制
  // };
  const disabledDate = (time) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // 设置为当天 00:00:00
    return time.getTime() < today.getTime();
  };
  const onChange = (e) => {
    if (e == '1') {
      accountDialogVisible.value = true;
    } else {
      userIds.value = [];
    }
  };
  const onChangeTemplate = (e) => {
    templateList.value.map((item) => {
      if (item.id == e) {
        nextTick(() => {
          vueWangeditor.value.setHtml(item.content);
        });
      }
    });
  };
  const confirmClick = () => {
    const getData = formPost.value.submitForm;
    const ruleFormRef = formPost.value.ruleFormRef;
    if (!vueWangeditor.value.editorRef.isEmpty()) {
      formData.value.content = vueWangeditor.value.valueHtml;
    }

    getData(ruleFormRef, (status, data) => {
      if (status == 'success') {
        loading.value = true;
        data.startTime = dayjs(time.value[0]).format('YYYY-MM-DD');
        data.endTime = dayjs(time.value[1]).format('YYYY-MM-DD');
        data.content = vueWangeditor.value.valueHtml;
        if (data.noticeTarget == '1') {
          data.userIds = userIds.value;
        }
        saveNotice(data)
          .then((res) => {
            if (res) {
              ElMessage({
                type: 'success',
                message: data.id ? `修改通知公告成功` : `新增通知公告成功`,
              });
              noticeList();
              dialog.value = false;
              loading.value = false;
            }
          })
          .catch(() => {
            loading.value = false;
          });
      }
    });
  };
  const handleAdd = () => {
    formData.value = {
      id: '',
      title: '',
      level: '1',
      type: '3',
      content: '',
      description: '',
      noticeTarget: '0',
    };
    isEdit.value = true;
    dialog.value = true;
    title.value = '新增通知公告';
  };

  const handleEdit = (item) => {
    isEdit.value = true;
    dialog.value = true;
    title.value = '编辑通知公告';
    formData.value = item;
    getNotice(item.id).then((res) => {
      userIds.value = res.userIds;
    });
    time.value = [formData.value.startTime, formData.value.endTime];
    nextTick(() => {
      vueWangeditor.value.setHtml(formData.value.content);
    });
  };
  /**
   * 查看通知公告
   */
  const handleDetail = (item) => {
    viewDialogVisible.value = true;
    const viewformData = { ...item };
    formData.value = viewformData;
    getNotice(item.id).then((res) => {
      formData.value.userNames = res.userNames?.join(',');
    });
    nextTick(() => {
      vueWangeditor.value.setHtml(viewformData.content);
    });
  };
  const handleDelete = (item, type) => {
    // type 1 批量删除  0 单独删除
    let title = '';
    if (type === '1') {
      title = `确认删除当前所选中${selectionIds.value.length}条通知公告数据？`;
    } else {
      title = '确认删除该通知公告？';
    }
    ElMessageBox.confirm(title, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        if (type === '1') {
          deleteNotice({ ids: selectionIds.value }).then(() => {
            ElMessage({
              showClose: true,
              type: 'success',
              message: '通知公告删除成功',
            });
            selectionIds.value = [];
            tableRef.value!.clearSelection();
            noticeList();
          });
        } else {
          deleteNoticeId(item.id).then(() => {
            ElMessage({
              showClose: true,
              type: 'success',
              message: '通知公告删除成功',
            });
            noticeList();
          });
        }
      })
      .catch(() => {});
  };
  const handlePush = (item) => {
    ElMessageBox.confirm('确认发布该通知公告？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'succes',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        pushNotice(item.id).then(() => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '通知公告发布成功',
          });
          noticeList();
        });
      })
      .catch(() => {});
  };
  const handleRevoke = (item) => {
    ElMessageBox.confirm('确认撤销该通知公告？', '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
      icon: markRaw(CircleClose),
      customStyle: {
        backgroundColor: 'rgba(255, 241, 240, 1)',
        border: '1px soild rgba(255, 163, 158, 1)',
      },
    })
      .then(() => {
        noticeRevoke(item.id).then(() => {
          ElMessage({
            showClose: true,
            type: 'success',
            message: '通知公告撤销成功',
          });
          noticeList();
        });
      })
      .catch(() => {});
  };
  const handleCurrentChange = (val) => {
    selectionIds.value = [];
    for (let i = 0; i < val.length; i++) {
      selectionIds.value.push(val[i].id);
    }
  };
  const getNoticeTemplatesList = () => {
    getNoticeTemplates().then((res) => {
      templateList.value = res;
    });
  };
  // const downLoad = (isTemplate) => {
  //   const { display, postStationName, postStationCode } =
  //     searchRef?.value?.['searchValue'] || {};
  //   const params = {
  //     display,
  //     postStationName,
  //     postStationCode,
  //     isTemplate,
  //   };
  //   downLoadApi(params).then((res) => {
  //     const blob = new Blob([res.data], {
  //       type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
  //     });
  //     const fileName = '平台通知公告管理' + '.xlsx';
  //     const elink = document.createElement('a'); // 创建a标签
  //     elink.download = fileName; // 为a标签添加download属性 // a.download = fileName; //命名下载名称
  //     elink.style.display = 'none';
  //     elink.href = URL.createObjectURL(blob);
  //     document.body.appendChild(elink);
  //     elink.click(); // 点击下载
  //     console.log(elink.href);
  //     URL.revokeObjectURL(elink.href); // 释放URL 对象
  //     document.body.removeChild(elink); // 释放标签
  //   });
  // };

  // const uploadFile = () => {
  //   uploadLoading.value = true;
  //   let formData = new FormData();
  //   formData.append('file', fileData.value?.raw);
  //   uploadApi(formData)
  //     .then(() => {
  //       ElMessage({
  //         type: 'success',
  //         message: '导入成功',
  //       });
  //       fileVisible.value = false;
  //       noticeList();
  //       uploadLoading.value = false;
  //     })
  //     .catch(() => {
  //       uploadLoading.value = false;
  //     });
  // };
  const accountSelect = (data) => {
    userIds.value = data;
  };
  noticeList();
  getNoticeTemplatesList();
</script>

<style scoped lang="scss">
  .content {
    display: flex;
    height: calc(100vh - 94px);
    background: #f0f0f0;
  }
  .box {
    width: 100%;
    height: calc(100% - 20px);
    margin: 10px;
    overflow: hidden;
    // box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
    // display: flex;

    .mid {
      height: 100%;
      // box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
    }
  }
  .search-div {
    border-radius: 6px;
    background: #fff;
    // padding: 10px 16px;
    .title {
      // border-bottom: 1px solid #e4e7ed;
      // line-height: 68px;

      font-size: 18px;
      padding: 20px 0 6px 20px;
      // background: #ffffff;
      font-weight: 700;
      color: #333333;
    }
  }
  .table-top-div {
    display: flex;
    justify-content: space-between;
    // line-height: 64px;
    margin-bottom: 20px;
    p {
      font-size: 18px;
      color: #333333;
      font-weight: 700;
      margin: 0;
      padding: 0;
      line-height: 32px;
    }
  }

  .table-content {
    margin-top: 16px;
    background: #ffffff;

    padding: 20px 20px 0 20px;
    // position: relative;
    overflow: auto;
    border-radius: 6px;
    .top-button {
      // position: absolute;
      // top: 10px;
      // left: 16px;
      z-index: 9;
      .upload-btn {
        display: inline-block;
        margin: 0 12px;
      }
    }
  }
  h4 {
    font-size: 18px;
    margin-bottom: 0;
    font-weight: 700;
  }
  .inst-code {
    color: #1a5efe;
    cursor: pointer;
  }

  .file-dialog .el-dialog__body {
    padding: 20px;
  }
  .cell-item {
    width: 80px;
  }
</style>
